# VP Test Tool 安裝指南

本文檔提供 VP Test Tool 的安裝和設置指南，包括依賴庫安裝、環境配置和功能啟用說明。

## 目錄

1. [系統需求](#系統需求)
2. [基本安裝](#基本安裝)
3. [高級功能安裝](#高級功能安裝)
4. [虛擬環境設置](#虛擬環境設置)
5. [打包為可執行文件](#打包為可執行文件)
6. [常見問題](#常見問題)

## 系統需求

- **作業系統**：Windows 10 或更高版本（推薦）、Windows 7 SP1（最低）
- **Python 版本**：Python 3.8 或更高版本
- **硬碟空間**：至少 500MB 可用空間
- **記憶體**：至少 4GB RAM（推薦 8GB 或更高）
- **螢幕解析度**：最低 1366x768（推薦 1920x1080 或更高）

## 基本安裝

### 步驟 1：安裝 Python

1. 從 [Python 官網](https://www.python.org/downloads/) 下載並安裝 Python 3.8 或更高版本
2. 安裝時勾選「Add Python to PATH」選項
3. 安裝完成後，打開命令提示字元（CMD）或 PowerShell，輸入以下命令確認 Python 已正確安裝：

```bash
python --version
```

### 步驟 2：下載 VP Test Tool

1. 從 GitLab 或其他指定位置下載 VP Test Tool 原始碼
2. 解壓縮到您選擇的目錄，例如 `C:\VP_Test_Tool`

### 步驟 3：安裝基本依賴庫

1. 打開命令提示字元（CMD）或 PowerShell
2. 切換到 VP Test Tool 目錄：

```bash
cd C:\VP_Test_Tool
```

3. 安裝基本依賴庫：

```bash
pip install -r requirements.txt
```

這將安裝以下基本依賴庫：
- requests：用於 HTTP 請求
- pandas：用於數據處理
- openpyxl：用於 Excel 文件處理
- urllib3：用於 HTTP 客戶端
- Pillow：用於圖像處理

### 步驟 4：運行 VP Test Tool

安裝完成後，您可以通過以下命令運行 VP Test Tool：

```bash
python main.py
```

## 高級功能安裝

VP Test Tool 提供了一些高級功能，這些功能需要安裝額外的依賴庫。

### 內存監控功能

內存監控功能用於監控和優化程序的內存使用，需要安裝 `psutil` 庫：

```bash
pip install psutil
```

### 網絡恢復功能

網絡恢復功能用於自動檢測和恢復網絡連接問題，依賴於基本安裝中的 `requests` 庫。

### 增強日誌功能

增強日誌功能提供更詳細的日誌記錄和導出功能，不需要額外的依賴庫。

### 資源監控功能

資源監控功能用於顯示系統資源使用情況，需要安裝 `psutil` 庫：

```bash
pip install psutil
```

### 自動更新功能

自動更新功能用於檢查和下載程序更新，依賴於基本安裝中的 `requests` 庫。

## 虛擬環境設置

建議在虛擬環境中運行 VP Test Tool，以避免依賴庫衝突。

### 創建虛擬環境

1. 打開命令提示字元（CMD）或 PowerShell
2. 切換到 VP Test Tool 目錄：

```bash
cd C:\VP_Test_Tool
```

3. 創建虛擬環境：

```bash
python -m venv .venv
```

4. 激活虛擬環境：

在 Windows 上：
```bash
.venv\Scripts\activate
```

在 Linux/macOS 上：
```bash
source .venv/bin/activate
```

5. 安裝依賴庫：

```bash
pip install -r requirements.txt
```

6. 運行 VP Test Tool：

```bash
python main.py
```

7. 使用完畢後，可以通過以下命令退出虛擬環境：

```bash
deactivate
```

## 打包為可執行文件

VP Test Tool 提供了多種打包方式，可以將程序打包為獨立的可執行文件。

### 使用 cx_Freeze 打包（推薦）

1. 安裝 cx_Freeze：

```bash
pip install cx_Freeze
```

2. 運行打包腳本：

```bash
python setup_cx_freeze_optimized.py build
```

3. 打包完成後，可執行文件將位於 `dist/cx_freeze_optimized` 目錄中。

### 使用 py2exe 打包

1. 安裝 py2exe：

```bash
pip install py2exe
```

2. 運行打包腳本：

```bash
python setup_py2exe.py
```

3. 打包完成後，可執行文件將位於 `dist/py2exe` 目錄中。

## 常見問題

### 1. 安裝依賴庫時出現錯誤

**問題**：安裝依賴庫時出現 `ERROR: Could not find a version that satisfies the requirement...` 錯誤。

**解決方案**：
- 確保您的 Python 版本與依賴庫兼容
- 嘗試使用國內鏡像源：
  ```bash
  pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
  ```
- 嘗試單獨安裝有問題的依賴庫

### 2. 運行程序時出現 `ModuleNotFoundError`

**問題**：運行程序時出現 `ModuleNotFoundError: No module named 'xxx'` 錯誤。

**解決方案**：
- 確保您已安裝所有依賴庫：
  ```bash
  pip install -r requirements.txt
  ```
- 檢查是否在正確的虛擬環境中運行程序
- 嘗試重新安裝有問題的模塊

### 3. 內存監控功能無法使用

**問題**：程序啟動時顯示「內存監控功能未啟用」。

**解決方案**：
- 安裝 psutil 庫：
  ```bash
  pip install psutil
  ```
- 如果已安裝 psutil 但仍然無法使用，嘗試重新安裝：
  ```bash
  pip uninstall psutil
  pip install psutil
  ```

### 4. 打包為可執行文件時出現錯誤

**問題**：使用 cx_Freeze 或 py2exe 打包時出現錯誤。

**解決方案**：
- 確保已安裝打包工具：
  ```bash
  pip install cx_Freeze py2exe
  ```
- 檢查打包腳本中的路徑是否正確
- 嘗試使用管理員權限運行打包命令

### 5. 運行可執行文件時出現錯誤

**問題**：運行打包後的可執行文件時出現錯誤。

**解決方案**：
- 確保目標電腦上已安裝 Visual C++ Redistributable
- 檢查是否缺少依賴文件，嘗試複製整個 dist 目錄
- 嘗試使用 `setup_cx_freeze_full.py` 打包，包含所有依賴

如果您遇到其他問題，請聯繫開發團隊或在 GitLab 上提交 Issue。
