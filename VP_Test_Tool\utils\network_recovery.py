"""網絡連接恢復器

此模組提供網絡連接問題的自動恢復功能。
"""
import time
import socket
import logging
import threading
import requests
from typing import Dict, Any, Optional, Callable, List, Tuple, Union
from .exceptions import NetworkConnectionError, NetworkTimeoutError, NetworkDNSError

logger = logging.getLogger(__name__)

class NetworkRecovery:
    """網絡連接恢復器
    
    提供網絡連接問題的自動恢復功能。
    
    Args:
        max_retries: 最大重試次數
        retry_interval: 重試間隔（秒）
        check_interval: 檢查間隔（秒）
        on_recovery_start: 恢復開始回調函數
        on_recovery_success: 恢復成功回調函數
        on_recovery_failure: 恢復失敗回調函數
        on_connection_status: 連接狀態回調函數
    """
    
    def __init__(
        self,
        max_retries: int = 5,
        retry_interval: float = 2.0,
        check_interval: float = 30.0,
        on_recovery_start: Optional[Callable[[], None]] = None,
        on_recovery_success: Optional[Callable[[], None]] = None,
        on_recovery_failure: Optional[Callable[[Exception], None]] = None,
        on_connection_status: Optional[Callable[[bool, Optional[str]], None]] = None
    ):
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        self.check_interval = check_interval
        self.on_recovery_start = on_recovery_start
        self.on_recovery_success = on_recovery_success
        self.on_recovery_failure = on_recovery_failure
        self.on_connection_status = on_connection_status
        
        self.is_monitoring = False
        self.monitor_thread = None
        self.connection_status = True
        self.last_error = None
        self.lock = threading.RLock()
        
        # 測試用的 URL 列表
        self.test_urls = [
            "https://www.google.com",
            "https://www.baidu.com",
            "https://www.microsoft.com"
        ]
        
        # 測試用的 DNS 服務器列表
        self.dns_servers = [
            "8.8.8.8",  # Google DNS
            "1.1.1.1",  # Cloudflare DNS
            "114.114.114.114"  # 114 DNS
        ]
        
    def start_monitoring(self):
        """開始監控"""
        with self.lock:
            if self.is_monitoring:
                logger.warning("網絡連接監控器已在運行中")
                return
                
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_thread,
                daemon=True
            )
            self.monitor_thread.start()
            logger.info(f"網絡連接監控器已啟動，檢查間隔: {self.check_interval} 秒")
            
    def stop_monitoring(self):
        """停止監控"""
        with self.lock:
            if not self.is_monitoring:
                logger.warning("網絡連接監控器未在運行中")
                return
                
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=1.0)
                self.monitor_thread = None
            logger.info("網絡連接監控器已停止")
            
    def check_connection(self) -> Tuple[bool, Optional[str]]:
        """檢查網絡連接
        
        Returns:
            Tuple[bool, Optional[str]]: 連接狀態和錯誤信息
        """
        # 檢查 DNS 解析
        dns_ok = self._check_dns()
        if not dns_ok:
            error_msg = "DNS 解析失敗，無法連接到 DNS 服務器"
            logger.warning(error_msg)
            return False, error_msg
            
        # 檢查網絡連接
        for url in self.test_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return True, None
            except requests.exceptions.RequestException:
                continue
                
        error_msg = "網絡連接失敗，無法連接到測試 URL"
        logger.warning(error_msg)
        return False, error_msg
        
    def _check_dns(self) -> bool:
        """檢查 DNS 解析
        
        Returns:
            bool: DNS 解析是否正常
        """
        for dns_server in self.dns_servers:
            try:
                socket.gethostbyname(dns_server)
                return True
            except socket.gaierror:
                continue
                
        return False
        
    def recover_connection(self) -> bool:
        """恢復網絡連接
        
        Returns:
            bool: 是否成功恢復連接
        """
        # 調用恢復開始回調
        if self.on_recovery_start:
            try:
                self.on_recovery_start()
            except Exception as e:
                logger.error(f"恢復開始回調函數執行失敗: {e}")
                
        logger.info("開始恢復網絡連接...")
        
        # 重試連接
        for i in range(self.max_retries):
            logger.info(f"嘗試恢復網絡連接 ({i+1}/{self.max_retries})...")
            
            # 檢查連接
            connection_ok, error_msg = self.check_connection()
            if connection_ok:
                logger.info("網絡連接已恢復")
                
                # 調用恢復成功回調
                if self.on_recovery_success:
                    try:
                        self.on_recovery_success()
                    except Exception as e:
                        logger.error(f"恢復成功回調函數執行失敗: {e}")
                        
                return True
                
            # 等待重試
            time.sleep(self.retry_interval)
            
        logger.error(f"恢復網絡連接失敗，已重試 {self.max_retries} 次")
        
        # 調用恢復失敗回調
        if self.on_recovery_failure:
            try:
                self.on_recovery_failure(NetworkConnectionError("恢復網絡連接失敗"))
            except Exception as e:
                logger.error(f"恢復失敗回調函數執行失敗: {e}")
                
        return False
        
    def _monitor_thread(self):
        """監控線程"""
        logger.info("網絡連接監控線程已啟動")
        
        try:
            while self.is_monitoring:
                # 檢查連接
                connection_ok, error_msg = self.check_connection()
                
                # 更新連接狀態
                if connection_ok != self.connection_status:
                    if connection_ok:
                        logger.info("網絡連接已恢復")
                    else:
                        logger.warning(f"網絡連接已斷開: {error_msg}")
                        
                    self.connection_status = connection_ok
                    self.last_error = error_msg if not connection_ok else None
                    
                    # 調用連接狀態回調
                    if self.on_connection_status:
                        try:
                            self.on_connection_status(connection_ok, error_msg)
                        except Exception as e:
                            logger.error(f"連接狀態回調函數執行失敗: {e}")
                            
                # 如果連接斷開，嘗試恢復
                if not connection_ok:
                    self.recover_connection()
                    
                # 等待下一次檢查
                time.sleep(self.check_interval)
                
        except Exception as e:
            logger.error(f"網絡連接監控線程異常: {e}")
            
        finally:
            logger.info("網絡連接監控線程已停止")
            
    def get_connection_status(self) -> Dict[str, Any]:
        """獲取連接狀態
        
        Returns:
            Dict[str, Any]: 連接狀態信息
        """
        return {
            "is_connected": self.connection_status,
            "last_error": self.last_error,
            "is_monitoring": self.is_monitoring
        }
        
    def diagnose_connection(self) -> Dict[str, Any]:
        """診斷連接問題
        
        Returns:
            Dict[str, Any]: 診斷結果
        """
        result = {
            "is_connected": False,
            "dns_ok": False,
            "url_status": {},
            "error_type": None,
            "error_message": None,
            "suggestions": []
        }
        
        # 檢查 DNS 解析
        try:
            result["dns_ok"] = self._check_dns()
            if not result["dns_ok"]:
                result["error_type"] = "DNS_ERROR"
                result["error_message"] = "DNS 解析失敗，無法連接到 DNS 服務器"
                result["suggestions"].append("檢查網絡連接設置")
                result["suggestions"].append("檢查 DNS 服務器設置")
                result["suggestions"].append("嘗試重啟網絡設備")
        except Exception as e:
            result["error_type"] = "DNS_CHECK_ERROR"
            result["error_message"] = f"檢查 DNS 解析時發生錯誤: {e}"
            
        # 檢查網絡連接
        for url in self.test_urls:
            try:
                response = requests.get(url, timeout=5)
                result["url_status"][url] = {
                    "status_code": response.status_code,
                    "ok": response.status_code == 200
                }
                if response.status_code == 200:
                    result["is_connected"] = True
            except requests.exceptions.Timeout:
                result["url_status"][url] = {
                    "status_code": None,
                    "ok": False,
                    "error": "TIMEOUT"
                }
                if not result["error_type"]:
                    result["error_type"] = "TIMEOUT_ERROR"
                    result["error_message"] = f"連接 {url} 超時"
                    result["suggestions"].append("檢查網絡連接速度")
                    result["suggestions"].append("檢查防火牆設置")
            except requests.exceptions.ConnectionError:
                result["url_status"][url] = {
                    "status_code": None,
                    "ok": False,
                    "error": "CONNECTION_ERROR"
                }
                if not result["error_type"]:
                    result["error_type"] = "CONNECTION_ERROR"
                    result["error_message"] = f"無法連接到 {url}"
                    result["suggestions"].append("檢查網絡連接設置")
                    result["suggestions"].append("檢查代理服務器設置")
            except Exception as e:
                result["url_status"][url] = {
                    "status_code": None,
                    "ok": False,
                    "error": str(e)
                }
                
        # 如果所有 URL 都無法連接，但 DNS 正常
        if not result["is_connected"] and result["dns_ok"] and not result["error_type"]:
            result["error_type"] = "NETWORK_ERROR"
            result["error_message"] = "網絡連接失敗，無法連接到任何測試 URL"
            result["suggestions"].append("檢查網絡連接設置")
            result["suggestions"].append("檢查防火牆設置")
            result["suggestions"].append("嘗試重啟網絡設備")
            
        return result
        
    def get_diagnostic_report(self) -> str:
        """獲取診斷報告
        
        Returns:
            str: 診斷報告
        """
        try:
            # 獲取診斷結果
            diagnosis = self.diagnose_connection()
            
            # 構建報告
            report = [
                "===== 網絡連接診斷報告 =====",
                f"連接狀態: {'正常' if diagnosis['is_connected'] else '異常'}",
                f"DNS 解析: {'正常' if diagnosis['dns_ok'] else '異常'}",
                f"錯誤類型: {diagnosis['error_type'] or '無'}",
                f"錯誤信息: {diagnosis['error_message'] or '無'}",
                "",
                "URL 狀態:"
            ]
            
            for url, status in diagnosis["url_status"].items():
                if status.get("ok", False):
                    report.append(f"  ✅ {url}: 狀態碼 {status.get('status_code')}")
                else:
                    error = status.get("error", "未知錯誤")
                    report.append(f"  ❌ {url}: {error}")
                    
            if diagnosis.get("suggestions"):
                report.append("")
                report.append("建議:")
                for suggestion in diagnosis["suggestions"]:
                    report.append(f"  • {suggestion}")
                    
            report.append("=============================")
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"生成診斷報告失敗: {e}")
            return f"生成診斷報告失敗: {e}"

# 創建全局網絡連接恢復器實例
network_recovery = NetworkRecovery()
