"""現代化下拉選單元件"""
import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, List, Any
from utils.theme import ThemeManager

class ModernCombobox(ttk.Frame):
    """現代化下拉選單元件
    
    提供更現代化的下拉選單外觀和互動效果，支援標籤和驗證功能。
    
    Args:
        parent: 父元件
        label: 欄位標籤
        values: 選項值列表
        on_select: 選擇變更時的回調函數
        validation_func: 驗證函數，接受選擇值並返回布林值
        error_message: 驗證失敗時顯示的錯誤訊息
        **kwargs: 其他 ttk.Combobox 參數
    """
    
    def __init__(
        self, 
        parent, 
        label: Optional[str] = None, 
        values: Optional[List[Any]] = None, 
        on_select: Optional[Callable[[str], None]] = None, 
        validation_func: Optional[Callable[[str], bool]] = None, 
        error_message: str = "選擇無效", 
        **kwargs
    ):
        super().__init__(parent)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.validation_func = validation_func
        self.error_message = error_message
        self.on_select = on_select
        
        # 建立標籤 (如果有)
        if label:
            self.label = ttk.Label(
                self, 
                text=label, 
                font=self.theme_manager.get_font("label"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.label.pack(anchor="w", pady=(0, 2))
        
        # 建立下拉選單
        self.combobox = ttk.Combobox(self, values=values if values else [], **kwargs)
        self.combobox.pack(fill="x", expand=True)
        
        # 建立錯誤訊息標籤
        self.error_label = ttk.Label(
            self, 
            text="", 
            font=self.theme_manager.get_font("small"),
            foreground=self.theme_manager.get_color("danger")
        )
        self.error_label.pack(anchor="w", pady=(2, 0))
        
        # 綁定事件
        self.combobox.bind("<<ComboboxSelected>>", self._on_select)
        self.combobox.bind("<FocusOut>", self._on_focus_out)
        
    def _on_select(self, event):
        """當選擇變更時"""
        self._validate()
        
        if self.on_select:
            self.on_select(self.combobox.get())
            
    def _on_focus_out(self, event):
        """當下拉選單失去焦點時"""
        self._validate()
            
    def _validate(self) -> bool:
        """驗證選擇內容
        
        Returns:
            bool: 驗證是否通過
        """
        if not self.validation_func:
            return True
            
        value = self.combobox.get()
        is_valid = self.validation_func(value)
        
        if not is_valid:
            self.error_label.config(text=self.error_message)
        else:
            self.error_label.config(text="")
                
        return is_valid
        
    def get(self) -> str:
        """取得選擇值
        
        Returns:
            str: 選擇值
        """
        return self.combobox.get()
        
    def set(self, value: str):
        """設定選擇值
        
        Args:
            value: 要設定的值
        """
        self.combobox.set(value)
        self._validate()
            
    def set_values(self, values: List[Any]):
        """設定選項值列表
        
        Args:
            values: 選項值列表
        """
        self.combobox['values'] = values
        
    def clear(self):
        """清除選擇值"""
        self.combobox.set('')
        self.error_label.config(text="")
