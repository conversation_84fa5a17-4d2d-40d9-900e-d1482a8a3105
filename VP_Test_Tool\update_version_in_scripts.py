"""更新打包腳本中的版本號

此腳本會讀取 utils/version.py 中的版本號，然後更新所有打包腳本中的版本號。
"""
import os
import re
import sys

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 導入版本信息
from utils.version import VERSION

def update_version_in_file(file_path, version):
    """更新文件中的版本號
    
    Args:
        file_path (str): 文件路徑
        version (str): 版本號
    """
    try:
        # 讀取文件內容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正則表達式替換版本號
        pattern = r'APP_VERSION\s*=\s*["\']([0-9]+\.[0-9]+\.[0-9]+)["\']'
        new_content = re.sub(pattern, f'APP_VERSION = "{version}"', content)
        
        # 如果內容有變化，寫入文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"已更新 {file_path} 中的版本號為 {version}")
        else:
            print(f"{file_path} 中的版本號已經是 {version}")
    except Exception as e:
        print(f"更新 {file_path} 中的版本號失敗: {e}")

def main():
    """主函數"""
    # 打包腳本列表
    setup_scripts = [
        'setup_py2exe.py',
        'setup_cx_freeze.py',
        'setup_cx_freeze_full.py',
        'setup_cx_freeze_optimized.py',
        'setup_cx_freeze_pandas.py',
        'setup_cx_freeze_simple.py'
    ]
    
    # 更新每個打包腳本中的版本號
    for script in setup_scripts:
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script)
        if os.path.exists(script_path):
            update_version_in_file(script_path, VERSION)
        else:
            print(f"找不到腳本 {script_path}")
    
    print("版本號更新完成")

if __name__ == "__main__":
    main()
