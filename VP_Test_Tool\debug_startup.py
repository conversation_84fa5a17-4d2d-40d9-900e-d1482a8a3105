"""
VP Test Tool V2.3.4 - 診斷模式
簡化版啟動腳本，用於診斷啟動問題
"""
import os
import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def show_error(title, message):
    """顯示錯誤對話框"""
    root = tk.Tk()
    root.withdraw()  # 隱藏主窗口
    messagebox.showerror(title, message)
    root.destroy()

def main():
    """主函數"""
    try:
        print("正在啟動簡化版測試程式...")

        # 建立基本視窗
        root = tk.Tk()
        root.title("VP Test Tool - 診斷模式")
        root.geometry("800x600")

        # 添加標籤
        label = tk.Label(root, text="正在診斷模式中運行...", font=("Microsoft JhengHei UI", 14))
        label.pack(pady=20)

        # 添加狀態標籤
        status_label = tk.Label(root, text="正在初始化...", font=("Microsoft JhengHei UI", 10))
        status_label.pack(pady=10)

        # 更新狀態函數
        def update_status(message):
            status_label.config(text=message)
            root.update()

        # 嘗試導入主要模組
        update_status("正在導入必要模組...")

        # 嘗試導入設定模組
        try:
            from utils.config import Config
            update_status("成功導入設定模組")

            # 嘗試載入設定
            try:
                config = Config()
                update_status("成功載入設定")
            except Exception as e:
                update_status(f"載入設定失敗: {e}")
                raise
        except Exception as e:
            update_status(f"導入設定模組失敗: {e}")
            raise

        # 嘗試導入 HTTP 客戶端
        try:
            from utils.http_client import HttpClient
            update_status("成功導入 HTTP 客戶端模組")

            # 嘗試創建 HTTP 客戶端（使用較短的超時時間）
            try:
                http_client = HttpClient(timeout=5)
                update_status("成功創建 HTTP 客戶端")
            except Exception as e:
                update_status(f"創建 HTTP 客戶端失敗: {e}")
                raise
        except Exception as e:
            update_status(f"導入 HTTP 客戶端模組失敗: {e}")
            raise

        # 嘗試導入主視窗模組
        try:
            from views.main_window import MainWindow
            update_status("成功導入主視窗模組")

            # 嘗試創建主視窗
            try:
                main_window = MainWindow(root, config)
                update_status("成功創建主視窗")
            except Exception as e:
                update_status(f"創建主視窗失敗: {e}")
                raise
        except Exception as e:
            update_status(f"導入主視窗模組失敗: {e}")
            raise

        # 嘗試導入服務模組
        try:
            from models.member import MemberService
            from models.agent import AgentService
            update_status("成功導入服務模組")

            # 嘗試創建服務
            try:
                member_service = MemberService(http_client)
                agent_service = AgentService(http_client)
                update_status("成功創建服務")
            except Exception as e:
                update_status(f"創建服務失敗: {e}")
                raise
        except Exception as e:
            update_status(f"導入服務模組失敗: {e}")
            raise

        # 診斷完成
        update_status("診斷完成，所有模組載入正常")

        # 添加啟動主程式的按鈕
        def start_main_app():
            root.destroy()
            try:
                import main
                main.main()
            except Exception as e:
                show_error("啟動失敗", f"主程式啟動失敗: {e}\n\n{traceback.format_exc()}")

        start_button = tk.Button(root, text="啟動主程式", command=start_main_app, font=("Microsoft JhengHei UI", 12))
        start_button.pack(pady=20)

        # 啟動主循環
        root.mainloop()

    except Exception as e:
        error_message = f"診斷過程中發生錯誤: {e}\n\n{traceback.format_exc()}"
        print(error_message)
        show_error("診斷錯誤", error_message)

if __name__ == "__main__":
    main()
