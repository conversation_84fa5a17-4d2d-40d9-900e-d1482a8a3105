"""內存監控器

此模組提供內存監控功能，用於監控和優化程序的內存使用。
"""
import os
import sys
import gc
import time
import logging
import threading
import psutil
from typing import Dict, Any, Optional, Callable, List, Tuple

logger = logging.getLogger(__name__)

class MemoryMonitor:
    """內存監控器
    
    提供內存監控功能，用於監控和優化程序的內存使用。
    
    Args:
        threshold_mb: 內存使用閾值（MB），超過此值將觸發垃圾回收
        check_interval: 檢查間隔（秒）
        on_threshold_exceeded: 閾值超過回調函數
        on_memory_status: 內存狀態回調函數
    """
    
    def __init__(
        self,
        threshold_mb: float = 200.0,
        check_interval: float = 5.0,
        on_threshold_exceeded: Optional[Callable[[float], None]] = None,
        on_memory_status: Optional[Callable[[Dict[str, Any]], None]] = None
    ):
        self.threshold_mb = threshold_mb
        self.check_interval = check_interval
        self.on_threshold_exceeded = on_threshold_exceeded
        self.on_memory_status = on_memory_status
        
        self.process = psutil.Process(os.getpid())
        self.is_monitoring = False
        self.monitor_thread = None
        self.memory_history = []
        self.max_history_size = 100
        self.lock = threading.RLock()
        
    def start_monitoring(self):
        """開始監控"""
        with self.lock:
            if self.is_monitoring:
                logger.warning("內存監控器已在運行中")
                return
                
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_thread,
                daemon=True
            )
            self.monitor_thread.start()
            logger.info(f"內存監控器已啟動，閾值: {self.threshold_mb} MB，檢查間隔: {self.check_interval} 秒")
            
    def stop_monitoring(self):
        """停止監控"""
        with self.lock:
            if not self.is_monitoring:
                logger.warning("內存監控器未在運行中")
                return
                
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=1.0)
                self.monitor_thread = None
            logger.info("內存監控器已停止")
            
    def get_memory_usage(self) -> Dict[str, Any]:
        """獲取內存使用情況
        
        Returns:
            Dict[str, Any]: 內存使用情況
        """
        try:
            # 刷新進程信息
            self.process.cpu_percent()
            
            # 獲取內存使用情況
            memory_info = self.process.memory_info()
            memory_usage_mb = memory_info.rss / (1024 * 1024)
            
            # 獲取系統內存使用情況
            system_memory = psutil.virtual_memory()
            system_memory_total_mb = system_memory.total / (1024 * 1024)
            system_memory_available_mb = system_memory.available / (1024 * 1024)
            system_memory_used_mb = system_memory.used / (1024 * 1024)
            system_memory_percent = system_memory.percent
            
            # 獲取 Python 內存分配器信息
            gc_counts = gc.get_count()
            gc_thresholds = gc.get_threshold()
            
            # 構建結果
            result = {
                "timestamp": time.time(),
                "process_memory_mb": memory_usage_mb,
                "system_memory_total_mb": system_memory_total_mb,
                "system_memory_available_mb": system_memory_available_mb,
                "system_memory_used_mb": system_memory_used_mb,
                "system_memory_percent": system_memory_percent,
                "gc_counts": gc_counts,
                "gc_thresholds": gc_thresholds,
                "threshold_exceeded": memory_usage_mb > self.threshold_mb
            }
            
            # 添加到歷史記錄
            with self.lock:
                self.memory_history.append(result)
                # 限制歷史記錄大小
                if len(self.memory_history) > self.max_history_size:
                    self.memory_history = self.memory_history[-self.max_history_size:]
                    
            return result
            
        except Exception as e:
            logger.error(f"獲取內存使用情況失敗: {e}")
            return {
                "timestamp": time.time(),
                "error": str(e)
            }
            
    def force_garbage_collection(self):
        """強制執行垃圾回收"""
        try:
            # 記錄垃圾回收前的內存使用情況
            before = self.get_memory_usage()
            before_mb = before.get("process_memory_mb", 0)
            
            # 執行垃圾回收
            gc.collect()
            
            # 記錄垃圾回收後的內存使用情況
            after = self.get_memory_usage()
            after_mb = after.get("process_memory_mb", 0)
            
            # 計算釋放的內存
            freed_mb = before_mb - after_mb
            
            logger.info(f"垃圾回收已執行，釋放內存: {freed_mb:.2f} MB")
            return freed_mb
            
        except Exception as e:
            logger.error(f"執行垃圾回收失敗: {e}")
            return 0
            
    def get_memory_history(self) -> List[Dict[str, Any]]:
        """獲取內存使用歷史記錄
        
        Returns:
            List[Dict[str, Any]]: 內存使用歷史記錄
        """
        with self.lock:
            return self.memory_history.copy()
            
    def get_memory_trend(self) -> Dict[str, Any]:
        """獲取內存使用趨勢
        
        Returns:
            Dict[str, Any]: 內存使用趨勢
        """
        with self.lock:
            if not self.memory_history:
                return {
                    "trend": "unknown",
                    "growth_rate": 0,
                    "average_usage": 0,
                    "peak_usage": 0,
                    "current_usage": 0
                }
                
            # 計算趨勢
            memory_values = [entry.get("process_memory_mb", 0) for entry in self.memory_history]
            current_usage = memory_values[-1]
            peak_usage = max(memory_values)
            average_usage = sum(memory_values) / len(memory_values)
            
            # 計算增長率
            if len(memory_values) >= 2:
                first_value = memory_values[0]
                last_value = memory_values[-1]
                time_span = self.memory_history[-1]["timestamp"] - self.memory_history[0]["timestamp"]
                
                if time_span > 0:
                    growth_rate = (last_value - first_value) / time_span
                else:
                    growth_rate = 0
            else:
                growth_rate = 0
                
            # 判斷趨勢
            if growth_rate > 0.1:  # 每秒增長超過 0.1 MB
                trend = "increasing"
            elif growth_rate < -0.1:  # 每秒減少超過 0.1 MB
                trend = "decreasing"
            else:
                trend = "stable"
                
            return {
                "trend": trend,
                "growth_rate": growth_rate,
                "average_usage": average_usage,
                "peak_usage": peak_usage,
                "current_usage": current_usage
            }
            
    def _monitor_thread(self):
        """監控線程"""
        logger.info("內存監控線程已啟動")
        
        try:
            while self.is_monitoring:
                # 獲取內存使用情況
                memory_info = self.get_memory_usage()
                memory_usage_mb = memory_info.get("process_memory_mb", 0)
                
                # 檢查是否超過閾值
                if memory_usage_mb > self.threshold_mb:
                    logger.warning(f"內存使用超過閾值: {memory_usage_mb:.2f} MB > {self.threshold_mb} MB")
                    
                    # 執行垃圾回收
                    freed_mb = self.force_garbage_collection()
                    
                    # 調用回調函數
                    if self.on_threshold_exceeded:
                        try:
                            self.on_threshold_exceeded(memory_usage_mb)
                        except Exception as e:
                            logger.error(f"閾值超過回調函數執行失敗: {e}")
                            
                # 調用內存狀態回調函數
                if self.on_memory_status:
                    try:
                        self.on_memory_status(memory_info)
                    except Exception as e:
                        logger.error(f"內存狀態回調函數執行失敗: {e}")
                        
                # 等待下一次檢查
                time.sleep(self.check_interval)
                
        except Exception as e:
            logger.error(f"內存監控線程異常: {e}")
            
        finally:
            logger.info("內存監控線程已停止")
            
    def get_memory_report(self) -> str:
        """獲取內存報告
        
        Returns:
            str: 內存報告
        """
        try:
            # 獲取當前內存使用情況
            memory_info = self.get_memory_usage()
            memory_usage_mb = memory_info.get("process_memory_mb", 0)
            system_memory_total_mb = memory_info.get("system_memory_total_mb", 0)
            system_memory_available_mb = memory_info.get("system_memory_available_mb", 0)
            system_memory_used_mb = memory_info.get("system_memory_used_mb", 0)
            system_memory_percent = memory_info.get("system_memory_percent", 0)
            
            # 獲取內存趨勢
            trend_info = self.get_memory_trend()
            trend = trend_info.get("trend", "unknown")
            growth_rate = trend_info.get("growth_rate", 0)
            average_usage = trend_info.get("average_usage", 0)
            peak_usage = trend_info.get("peak_usage", 0)
            
            # 構建報告
            report = [
                "===== 內存使用報告 =====",
                f"當前進程內存使用: {memory_usage_mb:.2f} MB",
                f"系統總內存: {system_memory_total_mb:.2f} MB",
                f"系統可用內存: {system_memory_available_mb:.2f} MB",
                f"系統已用內存: {system_memory_used_mb:.2f} MB ({system_memory_percent:.1f}%)",
                f"內存使用趨勢: {trend}",
                f"內存增長率: {growth_rate:.2f} MB/s",
                f"平均內存使用: {average_usage:.2f} MB",
                f"峰值內存使用: {peak_usage:.2f} MB",
                "========================="
            ]
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"生成內存報告失敗: {e}")
            return f"生成內存報告失敗: {e}"
            
    @staticmethod
    def get_object_size(obj) -> int:
        """獲取對象大小
        
        Args:
            obj: 要測量的對象
            
        Returns:
            int: 對象大小（字節）
        """
        import sys
        
        # 基本類型直接返回
        if obj is None:
            return 0
        if isinstance(obj, (int, float, bool)):
            return sys.getsizeof(obj)
        if isinstance(obj, str):
            return sys.getsizeof(obj)
            
        # 容器類型需要遞歸計算
        if isinstance(obj, dict):
            size = sys.getsizeof(obj)
            for key, value in obj.items():
                size += MemoryMonitor.get_object_size(key)
                size += MemoryMonitor.get_object_size(value)
            return size
            
        if isinstance(obj, (list, tuple, set)):
            size = sys.getsizeof(obj)
            for item in obj:
                size += MemoryMonitor.get_object_size(item)
            return size
            
        # 其他類型
        return sys.getsizeof(obj)
        
    @staticmethod
    def format_size(size_bytes: int) -> str:
        """格式化大小
        
        Args:
            size_bytes: 大小（字節）
            
        Returns:
            str: 格式化後的大小
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

# 創建全局內存監控器實例
memory_monitor = MemoryMonitor()
