"""線程管理模塊"""
import logging
import threading
import time
from typing import Dict, Any, Callable, List, Optional
from .exceptions import ThreadCreateError, ThreadTerminateError

logger = logging.getLogger(__name__)

class ThreadManager:
    """線程管理類，用於管理線程的創建、監控和終止"""

    def __init__(self):
        """初始化線程管理器"""
        self._threads: Dict[str, threading.Thread] = {}
        self._thread_status: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        logger.info("線程管理器初始化完成")

    def create_thread(self, name: str, target: Callable, args: tuple = (), kwargs: Dict[str, Any] = None, daemon: bool = True) -> str:
        """創建並啟動一個新線程

        Args:
            name: 線程名稱，用於標識線程
            target: 線程執行的函數
            args: 傳遞給函數的位置參數
            kwargs: 傳遞給函數的關鍵字參數
            daemon: 是否為守護線程

        Returns:
            str: 線程ID

        Raises:
            ThreadCreateError: 創建線程失敗
        """
        try:
            with self._lock:
                # 檢查是否已存在同名線程
                if name in self._threads and self._threads[name].is_alive():
                    logger.warning(f"線程 {name} 已存在且正在運行")
                    return name

                # 創建線程
                thread = threading.Thread(
                    target=self._thread_wrapper,
                    args=(target, args, kwargs or {}, name),
                    daemon=daemon
                )

                # 記錄線程狀態
                self._thread_status[name] = {
                    "start_time": time.time(),
                    "status": "created",
                    "error": None
                }

                # 啟動線程
                thread.start()
                self._threads[name] = thread
                logger.info(f"線程 {name} 已創建並啟動")
                return name

        except Exception as e:
            logger.error(f"創建線程 {name} 失敗: {e}")
            raise ThreadCreateError(f"創建線程 {name} 失敗: {e}")

    def _thread_wrapper(self, target: Callable, args: tuple, kwargs: Dict[str, Any], name: str):
        """線程包裝函數，用於捕獲線程異常並更新狀態

        Args:
            target: 線程執行的函數
            args: 傳遞給函數的位置參數
            kwargs: 傳遞給函數的關鍵字參數
            name: 線程名稱
        """
        try:
            with self._lock:
                self._thread_status[name]["status"] = "running"

            # 執行目標函數
            result = target(*args, **kwargs)

            with self._lock:
                self._thread_status[name]["status"] = "completed"
                self._thread_status[name]["result"] = result
                self._thread_status[name]["end_time"] = time.time()

        except Exception as e:
            logger.error(f"線程 {name} 執行失敗: {e}")
            with self._lock:
                self._thread_status[name]["status"] = "error"
                self._thread_status[name]["error"] = str(e)
                self._thread_status[name]["end_time"] = time.time()

    def get_thread_status(self, name: str) -> Dict[str, Any]:
        """獲取線程狀態

        Args:
            name: 線程名稱

        Returns:
            Dict[str, Any]: 線程狀態信息
        """
        with self._lock:
            if name not in self._thread_status:
                return {"status": "not_found"}

            status = self._thread_status[name].copy()
            if name in self._threads:
                status["is_alive"] = self._threads[name].is_alive()
            else:
                status["is_alive"] = False

            return status

    def is_thread_alive(self, name: str) -> bool:
        """檢查線程是否存活

        Args:
            name: 線程名稱

        Returns:
            bool: 線程是否存活
        """
        with self._lock:
            return name in self._threads and self._threads[name].is_alive()

    def wait_for_thread(self, name: str, timeout: float = None) -> bool:
        """等待線程完成

        Args:
            name: 線程名稱
            timeout: 超時時間（秒），None 表示無限等待

        Returns:
            bool: 線程是否已完成

        Raises:
            ValueError: 線程不存在
        """
        with self._lock:
            if name not in self._threads:
                raise ValueError(f"線程 {name} 不存在")

            thread = self._threads[name]

        # 等待線程完成
        thread.join(timeout)
        return not thread.is_alive()

    def terminate_thread(self, name: str) -> bool:
        """終止線程（注意：Python 不支持直接終止線程，這只是標記線程狀態）

        Args:
            name: 線程名稱

        Returns:
            bool: 是否成功標記線程為終止狀態

        Raises:
            ThreadTerminateError: 終止線程失敗
        """
        try:
            with self._lock:
                if name not in self._threads:
                    logger.warning(f"線程 {name} 不存在，無法終止")
                    return False

                if not self._threads[name].is_alive():
                    logger.info(f"線程 {name} 已經結束，無需終止")
                    return True

                # 標記線程狀態為終止
                self._thread_status[name]["status"] = "terminated"
                logger.info(f"線程 {name} 已標記為終止狀態")
                return True

        except Exception as e:
            logger.error(f"終止線程 {name} 失敗: {e}")
            raise ThreadTerminateError(f"終止線程 {name} 失敗: {e}")

    def get_all_threads(self) -> List[Dict[str, Any]]:
        """獲取所有線程信息

        Returns:
            List[Dict[str, Any]]: 所有線程信息
        """
        with self._lock:
            result = []
            for name, thread in self._threads.items():
                status = self._thread_status.get(name, {}).copy()
                status["name"] = name
                status["is_alive"] = thread.is_alive()
                result.append(status)
            return result

    def cleanup(self):
        """清理已完成的線程"""
        with self._lock:
            completed_threads = []
            for name, thread in list(self._threads.items()):
                if not thread.is_alive():
                    completed_threads.append(name)

            # 移除已完成的線程
            for name in completed_threads:
                del self._threads[name]
                logger.debug(f"已清理完成的線程: {name}")

    def shutdown(self, wait: bool = True, timeout: float = None):
        """關閉線程管理器，等待所有線程完成

        Args:
            wait: 是否等待所有線程完成
            timeout: 每個線程的等待超時時間（秒）
        """
        if wait:
            with self._lock:
                thread_names = list(self._threads.keys())

            # 等待所有線程完成
            for name in thread_names:
                try:
                    self.wait_for_thread(name, timeout)
                except ValueError:
                    pass  # 線程可能已經被其他地方移除

        # 清理所有線程
        with self._lock:
            self._threads.clear()
            logger.info("線程管理器已關閉")

# 創建全局線程管理器實例
thread_manager = ThreadManager()
