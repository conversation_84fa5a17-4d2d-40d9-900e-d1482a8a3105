"""載入指示器元件"""
import tkinter as tk
from tkinter import ttk
# 不需要導入 typing 模組
from utils.theme import ThemeManager

class LoadingIndicator(ttk.Frame):
    """載入指示器元件

    提供載入中的視覺效果，支援不同類型的載入動畫。

    Args:
        parent: 父元件
        size: 指示器大小
        type: 指示器類型，可以是 'spinner', 'bar', 'dots'
        **kwargs: 其他 ttk.Frame 參數
    """

    def __init__(
        self,
        parent,
        size: int = 30,
        type: str = "spinner",
        **kwargs
    ):
        super().__init__(parent, **kwargs)

        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 設定變數
        self.size = size
        self.type = type
        self._is_running = False
        self._animation_id = None
        self._animation_count = 0  # 初始化動畫計數器

        # 初始化 UI
        self._init_ui()

    def _init_ui(self):
        """初始化 UI"""
        # 建立外框容器
        self.container = ttk.Frame(self, padding=5)
        self.container.pack(padx=5, pady=5)

        # 建立畫布 - 增加大小使指示器更明顯
        self.canvas = tk.Canvas(
            self.container,
            width=self.size,
            height=self.size,
            bg=self["background"] if self["background"] else self.theme_manager.get_color("surface"),
            highlightthickness=0
        )
        self.canvas.pack(side=tk.LEFT, padx=5)

        # 添加文字標籤
        self.label = ttk.Label(
            self.container,
            text="處理中...",
            font=self.theme_manager.get_font("label"),
            foreground=self.theme_manager.get_color("primary")
        )
        self.label.pack(side=tk.LEFT, padx=5)

        # 根據類型建立不同的載入動畫
        if self.type == "spinner":
            # 建立旋轉載入動畫 - 使用更現代的設計
            # 外圈
            self.outer_arc = self.canvas.create_arc(
                3,
                3,
                self.size - 3,
                self.size - 3,
                start=0,
                extent=100,
                outline=self.theme_manager.get_color("primary"),
                width=4,
                style="arc"
            )

            # 內圈 - 增加層次感
            self.inner_arc = self.canvas.create_arc(
                self.size // 4,
                self.size // 4,
                self.size - self.size // 4,
                self.size - self.size // 4,
                start=180,
                extent=80,
                outline=self.theme_manager.get_color("secondary"),
                width=3,
                style="arc"
            )
        elif self.type == "bar":
            # 建立進度條載入動畫 - 使用更現代的設計
            # 先建立背景條
            self.bar_bg = self.canvas.create_rectangle(
                2,
                self.size // 2 - 4,
                self.size - 2,
                self.size // 2 + 4,
                fill=self.theme_manager.get_color("border"),
                outline=""
            )

            # 再建立進度條
            self.bar = self.canvas.create_rectangle(
                2,
                self.size // 2 - 4,
                2,
                self.size // 2 + 4,
                fill=self.theme_manager.get_color("primary"),
                outline=""
            )

            # 添加圓角效果 - 使用標籤以便於更新
            self.end_circle = self.canvas.create_oval(
                0,
                self.size // 2 - 4,
                8,
                self.size // 2 + 4,
                fill=self.theme_manager.get_color("primary"),
                outline="",
                tags="end_circle"
            )
        elif self.type == "dots":
            # 建立點狀載入動畫
            self.dots = []
            dot_size = self.size // 8
            dot_spacing = self.size // 4

            for i in range(3):
                x = (i + 1) * dot_spacing
                y = self.size // 2
                dot = self.canvas.create_oval(
                    x - dot_size,
                    y - dot_size,
                    x + dot_size,
                    y + dot_size,
                    fill=self.theme_manager.get_color("primary"),
                    outline=""
                )
                self.dots.append(dot)

    def start(self):
        """開始動畫"""
        self._is_running = True
        self._animate()

    def stop(self):
        """停止動畫"""
        self._is_running = False
        if self._animation_id:
            self.after_cancel(self._animation_id)
            self._animation_id = None

    def _animate(self):
        """執行動畫"""
        if not self._is_running:
            return

        if self.type == "spinner":
            # 更新外圈旋轉角度 - 順時針旋轉
            outer_start = self.canvas.itemcget(self.outer_arc, "start")
            new_outer_start = (float(outer_start) + 8) % 360
            self.canvas.itemconfig(self.outer_arc, start=new_outer_start)

            # 更新內圈旋轉角度 - 逆時針旋轉，增加視覺複雜度
            inner_start = self.canvas.itemcget(self.inner_arc, "start")
            new_inner_start = (float(inner_start) - 12) % 360
            self.canvas.itemconfig(self.inner_arc, start=new_inner_start)

            # 週期性變更彩色
            if int(new_outer_start) % 90 == 0:
                # 每旋轉 90 度變更一次顏色
                colors = [
                    self.theme_manager.get_color("primary"),
                    self.theme_manager.get_color("secondary"),
                    self.theme_manager.get_color("accent"),
                    self.theme_manager.get_color("info")
                ]
                color_index = int(new_outer_start) // 90
                self.canvas.itemconfig(self.outer_arc, outline=colors[color_index % 4])

        elif self.type == "bar":
            # 更新進度條位置 - 使用更平滑的動畫
            x1, y1, x2, y2 = self.canvas.coords(self.bar)

            if x2 >= self.size - 2:
                # 重置進度條
                self.canvas.coords(self.bar, 2, y1, 2, y2)

                # 變更進度條顏色，增加視覺變化
                colors = [
                    self.theme_manager.get_color("primary"),
                    self.theme_manager.get_color("secondary"),
                    self.theme_manager.get_color("accent"),
                    self.theme_manager.get_color("info")
                ]
                color_index = (self._animation_count // 2) % 4
                self.canvas.itemconfig(self.bar, fill=colors[color_index])

                # 更新圓角效果的顏色
                self.canvas.itemconfig("end_circle", fill=colors[color_index])

                self._animation_count += 1
            else:
                # 移動進度條 - 使用非線性速度增加視覺變化
                speed = 3 + (x2 / self.size) * 5  # 進度越大，速度越快
                self.canvas.coords(self.bar, x1, y1, x2 + speed, y2)

        elif self.type == "dots":
            # 更新點狀動畫
            for i, dot in enumerate(self.dots):
                # 計算透明度
                alpha = (self._animation_count + i * 5) % 15
                alpha = 0.2 + (alpha / 15) * 0.8

                # 更新點的顏色
                color = self.theme_manager.get_color("primary")
                r, g, b = self._hex_to_rgb(color)
                self.canvas.itemconfig(dot, fill=self._rgb_to_hex(r, g, b, alpha))

            self._animation_count = (self._animation_count + 1) % 15

        # 繼續動畫
        self._animation_id = self.after(50, self._animate)

    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """將十六進位色碼轉換為 RGB 值"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _rgb_to_hex(self, r: int, g: int, b: int, a: float = 1.0) -> str:
        """將 RGB 值轉換為十六進位色碼"""
        r = int(r * a)
        g = int(g * a)
        b = int(b * a)
        return f'#{r:02x}{g:02x}{b:02x}'

    def __del__(self):
        """解構子"""
        self.stop()
