"""設定面板"""
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Optional
from utils.theme import ThemeManager
from utils.config import Config
from utils.constants import PADDING
from widgets.modern_button import ModernButton
from widgets.modern_combobox import ModernCombobox
from widgets.card_frame import CardFrame

class SettingsPanel(ttk.Frame):
    """設定面板

    提供應用程式設定的管理介面，包含主題、字體大小等設定。

    Args:
        parent: 父元件
        config: 設定物件
        on_apply: 套用設定時的回調函數
    """

    def __init__(
        self,
        parent,
        config: Config,
        on_apply: Optional[Callable[[], None]] = None
    ):
        super().__init__(parent)

        self.config = config
        self.on_apply = on_apply
        self.theme_manager = ThemeManager()

        self._init_ui()

    def _init_ui(self):
        """初始化 UI"""
        # 主題設定卡片
        self.theme_card = CardFrame(self, title="主題設定", icon="🎨")
        self.theme_card.pack(fill="x", padx=PADDING, pady=PADDING)

        theme_content = self.theme_card.get_content_frame()

        # 主題選擇
        self.theme_combobox = ModernCombobox(
            theme_content,
            label="主題:",
            values=["light", "dark"],
            on_select=self._on_theme_change
        )
        self.theme_combobox.pack(fill="x", padx=PADDING, pady=PADDING)

        # 設定目前的主題
        current_theme = self.config.get("ui", {}).get("theme", "light")
        self.theme_combobox.set(current_theme)

        # 字體設定卡片
        self.font_card = CardFrame(self, title="字體設定", icon="🔤")
        self.font_card.pack(fill="x", padx=PADDING, pady=PADDING)

        font_content = self.font_card.get_content_frame()

        # 字體大小設定
        font_size_frame = ttk.Frame(font_content)
        font_size_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        ttk.Label(
            font_size_frame,
            text="字體大小:",
            font=self.theme_manager.get_font("label")
        ).pack(side="left", padx=PADDING)

        self.font_size_var = tk.IntVar(value=self.config.get("ui", {}).get("font_size", 10))

        font_size_spinbox = ttk.Spinbox(
            font_size_frame,
            from_=8,
            to=16,
            textvariable=self.font_size_var,
            width=5
        )
        font_size_spinbox.pack(side="left", padx=PADDING)

        # 視窗設定卡片
        self.window_card = CardFrame(self, title="視窗設定", icon="🖼️")
        self.window_card.pack(fill="x", padx=PADDING, pady=PADDING)

        window_content = self.window_card.get_content_frame()

        # 視窗寬度設定
        window_width_frame = ttk.Frame(window_content)
        window_width_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        ttk.Label(
            window_width_frame,
            text="視窗寬度:",
            font=self.theme_manager.get_font("label")
        ).pack(side="left", padx=PADDING)

        self.window_width_var = tk.IntVar(value=self.config.get("ui", {}).get("window_width", 1000))

        window_width_spinbox = ttk.Spinbox(
            window_width_frame,
            from_=800,
            to=1920,
            textvariable=self.window_width_var,
            width=5
        )
        window_width_spinbox.pack(side="left", padx=PADDING)

        # 視窗高度設定
        window_height_frame = ttk.Frame(window_content)
        window_height_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        ttk.Label(
            window_height_frame,
            text="視窗高度:",
            font=self.theme_manager.get_font("label")
        ).pack(side="left", padx=PADDING)

        self.window_height_var = tk.IntVar(value=self.config.get("ui", {}).get("window_height", 700))

        window_height_spinbox = ttk.Spinbox(
            window_height_frame,
            from_=600,
            to=1080,
            textvariable=self.window_height_var,
            width=5
        )
        window_height_spinbox.pack(side="left", padx=PADDING)

        # 按鈕區域
        button_frame = ttk.Frame(self)
        button_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        self.reset_button = ModernButton(
            button_frame,
            text="重置預設值",
            icon="🔄",
            command=self._reset_defaults,
            button_type="secondary"
        )
        self.reset_button.pack(side="right", padx=PADDING)

    def _on_theme_change(self, theme: str):
        """當主題變更時"""
        # 這裡可以加入即時預覽主題的功能
        pass

    def _apply_settings(self):
        """套用設定"""
        # 更新設定
        # 確保設定檔中有 ui 節點
        if "ui" not in self.config.config:
            self.config.config["ui"] = {}

        # 更新 UI 設定
        ui_config = self.config.config["ui"]
        ui_config["theme"] = self.theme_combobox.get()
        ui_config["font_size"] = self.font_size_var.get()
        ui_config["window_width"] = self.window_width_var.get()
        ui_config["window_height"] = self.window_height_var.get()

        # 儲存設定
        self.config.save_config()

        # 顯示訊息
        messagebox.showinfo("設定已更新", "部分設定需要重新啟動應用程式才能生效。")

        # 呼叫回調函數
        if self.on_apply:
            self.on_apply()

    def _reset_defaults(self):
        """重置預設值"""
        # 確認是否要重置
        if messagebox.askyesno("重置預設值", "確定要將所有設定重置為預設值嗎？"):
            # 重置設定
            default_config = self.config._get_default_config()
            ui_config = default_config.get("ui", {})

            # 更新 UI
            self.theme_combobox.set(ui_config.get("theme", "light"))
            self.font_size_var.set(ui_config.get("font_size", 10))
            self.window_width_var.set(ui_config.get("window_width", 1000))
            self.window_height_var.set(ui_config.get("window_height", 700))

            # 更新設定
            self.config.config["ui"] = ui_config

            # 儲存設定
            self.config.save_config()

            # 顯示訊息
            messagebox.showinfo("設定已重置", "所有設定已重置為預設值。部分設定需要重新啟動應用程式才能生效。")

            # 呼叫回調函數
            if self.on_apply:
                self.on_apply()

