"""
VP Test Tool V2.3.4 - 網絡連接測試腳本
用於測試程式所需的網絡連接是否正常
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import traceback
import sys
import os
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 導入 HTTP 客戶端
try:
    from utils.http_client import HttpClient
    from utils.constants import API_URLS
except ImportError as e:
    logger.error(f"導入模組失敗: {e}")
    # 創建一個臨時的 Tk 根窗口
    root = tk.Tk()
    root.withdraw()  # 隱藏主窗口
    messagebox.showerror("導入錯誤", f"導入模組失敗: {e}\n\n請確保您在正確的目錄中運行此腳本。")
    root.destroy()
    sys.exit(1)

class NetworkTestApp:
    """網絡連接測試應用程式"""
    def __init__(self, root):
        self.root = root
        self.root.title("VP Test Tool - 網絡連接測試")
        self.root.geometry("800x600")
        self.root.minsize(800, 600)

        # 設定視窗圖示
        try:
            icon_path = os.path.join("assets", "icons", "vp_test_tool.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            logger.warning(f"無法載入應用程式圖示: {e}")

        # 創建 HTTP 客戶端
        self.http_client = HttpClient(timeout=5, show_error_dialog=False)  # 不顯示錯誤對話框，由我們自己處理

        # 初始化 UI
        self._init_ui()

        # 啟動測試
        self.start_tests()

    def _init_ui(self):
        """初始化 UI"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 標題
        title_label = ttk.Label(
            main_frame,
            text="VP Test Tool - 網絡連接測試",
            font=("Microsoft JhengHei UI", 16, "bold")
        )
        title_label.pack(pady=(0, 20))

        # 說明
        desc_label = ttk.Label(
            main_frame,
            text="此工具將測試程式所需的所有網絡連接是否正常。\n如果有任何連接問題，將顯示詳細的錯誤訊息。",
            font=("Microsoft JhengHei UI", 10)
        )
        desc_label.pack(pady=(0, 20))

        # 測試結果框架
        self.results_frame = ttk.LabelFrame(main_frame, text="測試結果", padding=10)
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 測試結果文本框
        self.results_text = tk.Text(
            self.results_frame,
            wrap=tk.WORD,
            font=("Consolas", 10),
            height=15
        )
        self.results_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        # 滾動條
        scrollbar = ttk.Scrollbar(self.results_frame, command=self.results_text.yview)
        scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        self.results_text.config(yscrollcommand=scrollbar.set)

        # 按鈕框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 重新測試按鈕
        self.retest_button = ttk.Button(
            button_frame,
            text="重新測試",
            command=self.start_tests
        )
        self.retest_button.pack(side=tk.LEFT, padx=5)

        # 啟動主程式按鈕
        self.start_button = ttk.Button(
            button_frame,
            text="啟動主程式",
            command=self.start_main_app,
            state=tk.DISABLED  # 初始時禁用
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        # 退出按鈕
        exit_button = ttk.Button(
            button_frame,
            text="退出",
            command=self.root.destroy
        )
        exit_button.pack(side=tk.RIGHT, padx=5)

        # 狀態標籤
        self.status_label = ttk.Label(
            main_frame,
            text="準備開始測試...",
            font=("Microsoft JhengHei UI", 10)
        )
        self.status_label.pack(fill=tk.X)

    def log(self, message, level="INFO"):
        """記錄訊息到文本框"""
        # 設定顏色
        tag = None
        if level == "ERROR":
            tag = "error"
        elif level == "WARNING":
            tag = "warning"
        elif level == "SUCCESS":
            tag = "success"

        # 添加時間戳
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] [{level}] {message}\n"

        # 插入訊息
        self.results_text.insert(tk.END, full_message)

        # 應用標籤
        if tag:
            self.results_text.tag_add(tag, f"end-{len(full_message)+1}c", "end-1c")

        # 滾動到底部
        self.results_text.see(tk.END)

        # 更新 UI
        self.root.update()

    def set_status(self, message, is_error=False):
        """設定狀態標籤"""
        self.status_label.config(
            text=message,
            foreground="red" if is_error else "black"
        )
        self.root.update()

    def start_tests(self):
        """啟動測試"""
        # 禁用按鈕
        self.retest_button.config(state=tk.DISABLED)
        self.start_button.config(state=tk.DISABLED)

        # 清空結果
        self.results_text.delete(1.0, tk.END)

        # 設定標籤
        self.results_text.tag_configure("error", foreground="red")
        self.results_text.tag_configure("warning", foreground="orange")
        self.results_text.tag_configure("success", foreground="green")

        # 設定狀態
        self.set_status("正在測試網絡連接...")

        # 在新線程中運行測試
        threading.Thread(target=self._run_tests, daemon=True).start()

    def _run_tests(self):
        """運行測試"""
        try:
            self.log("開始網絡連接測試...")

            # 測試所有 API 端點
            all_success = True

            # 測試 API URLs
            for name, url in API_URLS.items():
                self.log(f"測試 {name}: {url}")

                try:
                    # 只測試連接，不發送實際請求
                    import socket
                    import urllib.parse

                    # 解析 URL
                    parsed_url = urllib.parse.urlparse(url)
                    host = parsed_url.netloc

                    # 如果有端口號，分離出來
                    if ":" in host:
                        host, port = host.split(":")
                        port = int(port)
                    else:
                        # 默認端口
                        port = 443 if parsed_url.scheme == "https" else 80

                    # 創建 socket
                    sock = None
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(5)  # 5 秒超時

                        # 嘗試連接
                        result = sock.connect_ex((host, port))
                    finally:
                        # 確保 socket 被關閉
                        if sock:
                            sock.close()

                    if result == 0:
                        self.log(f"✅ {name} 連接成功", "SUCCESS")
                    else:
                        self.log(f"❌ {name} 連接失敗: 錯誤碼 {result}", "ERROR")
                        all_success = False

                except Exception as e:
                    self.log(f"❌ {name} 測試失敗: {e}", "ERROR")
                    all_success = False

            # 測試完成
            if all_success:
                self.log("✅ 所有網絡連接測試通過！", "SUCCESS")
                self.set_status("所有網絡連接測試通過！")
                # 啟用啟動主程式按鈕
                self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
            else:
                self.log("❌ 部分網絡連接測試失敗，請檢查網絡設置或聯繫管理員。", "ERROR")
                self.set_status("部分網絡連接測試失敗！", True)

            # 啟用重新測試按鈕
            self.root.after(0, lambda: self.retest_button.config(state=tk.NORMAL))

        except Exception as e:
            self.log(f"❌ 測試過程中發生錯誤: {e}", "ERROR")
            self.log(traceback.format_exc(), "ERROR")
            self.set_status("測試過程中發生錯誤！", True)
            # 啟用重新測試按鈕
            self.root.after(0, lambda: self.retest_button.config(state=tk.NORMAL))

    def start_main_app(self):
        """啟動主程式"""
        try:
            self.root.destroy()
            import main
            main.main()
        except Exception as e:
            # 創建一個臨時的 Tk 根窗口
            error_root = tk.Tk()
            error_root.withdraw()  # 隱藏主窗口
            messagebox.showerror("啟動失敗", f"主程式啟動失敗: {e}\n\n{traceback.format_exc()}")
            error_root.destroy()

def main():
    """主函數"""
    try:
        # 建立根視窗
        root = tk.Tk()
        app = NetworkTestApp(root)
        root.mainloop()
    except Exception as e:
        logger.error(f"程式啟動失敗: {e}")
        logger.error(traceback.format_exc())
        # 創建一個臨時的 Tk 根窗口
        error_root = tk.Tk()
        error_root.withdraw()  # 隱藏主窗口
        messagebox.showerror("啟動錯誤", f"程式啟動失敗: {e}\n\n{traceback.format_exc()}")
        error_root.destroy()

if __name__ == "__main__":
    main()
