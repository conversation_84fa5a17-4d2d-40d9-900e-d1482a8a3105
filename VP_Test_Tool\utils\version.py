"""版本管理模塊

此模塊集中管理應用程式的版本信息，所有需要使用版本號的地方都應該從這裡導入。
"""

# 主版本號
MAJOR = 2
# 次版本號
MINOR = 5
# 修訂版本號
PATCH = 2

# 完整版本號
VERSION = f"{MAJOR}.{MINOR}.{PATCH}"
# 應用程式標題
APP_TITLE = f"VP Test Tool V{VERSION}"
# 版本發布日期 (YYYY-MM-DD)
RELEASE_DATE = "2025-05-13"

# 版本描述
VERSION_DESCRIPTION = """
VP Test Tool V{VERSION} 版本更新：

1. 錯誤修復：
   - 修復 Git 下載功能線程安全問題：解決 "main thread is not in main loop" 錯誤
   - 修復 RNG 設置功能中的線程安全問題：改進 UI 更新機制
   - 修復帳號產生線程失敗問題：解決 "main thread is not in main loop" 錯誤
   - 修復 MemberService.create_member() 參數錯誤：移除不支援的 password 參數

2. 穩定性改進：
   - 改進 Git 下載功能的穩定性：添加超時處理和備用下載方法
   - 改進 UI 更新機制：使用隊列進行線程間通信，避免線程安全問題
   - 改進帳號產生器的穩定性：使用主線程分批處理帳號，避免線程安全問題
   - 優化錯誤處理機制：提供更詳細的錯誤訊息和日誌記錄
""".format(VERSION=VERSION)

def get_version_info():
    """獲取版本信息

    Returns:
        dict: 包含版本信息的字典
    """
    return {
        "major": MAJOR,
        "minor": MINOR,
        "patch": PATCH,
        "version": VERSION,
        "app_title": APP_TITLE,
        "release_date": RELEASE_DATE,
        "description": VERSION_DESCRIPTION
    }

def is_newer_version(version_to_check):
    """檢查指定的版本是否比當前版本更新

    Args:
        version_to_check (str): 要檢查的版本號，格式為 "x.y.z"

    Returns:
        bool: 如果指定的版本比當前版本更新，則返回 True，否則返回 False
    """
    try:
        # 解析版本號
        major, minor, patch = map(int, version_to_check.split('.'))

        # 比較版本號
        if major > MAJOR:
            return True
        if major == MAJOR and minor > MINOR:
            return True
        if major == MAJOR and minor == MINOR and patch > PATCH:
            return True

        return False
    except:
        # 如果解析失敗，返回 False
        return False

def get_version_string(include_v=True):
    """獲取版本號字符串

    Args:
        include_v (bool): 是否包含 "V" 前綴

    Returns:
        str: 版本號字符串
    """
    if include_v:
        return f"V{VERSION}"
    else:
        return VERSION

if __name__ == "__main__":
    # 測試代碼
    print(f"當前版本: {VERSION}")
    print(f"應用程式標題: {APP_TITLE}")
    print(f"版本發布日期: {RELEASE_DATE}")
    print(f"版本描述: {VERSION_DESCRIPTION}")

    # 測試版本比較
    test_versions = ["2.4.0", "2.5.0", "2.5.1", "2.6.0", "3.0.0"]
    for test_version in test_versions:
        print(f"{test_version} 是否比當前版本更新: {is_newer_version(test_version)}")
