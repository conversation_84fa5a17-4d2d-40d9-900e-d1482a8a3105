"""版本管理模塊

此模塊集中管理應用程式的版本信息，所有需要使用版本號的地方都應該從這裡導入。
"""

# 主版本號
MAJOR = 2
# 次版本號
MINOR = 6
# 修訂版本號
PATCH = 1

# 完整版本號
VERSION = f"{MAJOR}.{MINOR}.{PATCH}"
# 應用程式標題
APP_TITLE = f"VP Test Tool V{VERSION}"
# 版本發布日期 (YYYY-MM-DD)
RELEASE_DATE = "2025-01-27"

# 版本描述
VERSION_DESCRIPTION = """
VP Test Tool V{VERSION} 版本更新 - IP 管理系統：

🎯 重大功能新增：
1. IP 管理系統：
   - 環境配置管理器：支援多環境配置（開發、測試、生產等）
   - 快速 IP 切換工具：支援批量 IP 替換、模板應用和歷史記錄恢復
   - 動態 API URLs：根據當前環境自動生成 API URLs，支援熱重載
   - 配置模板系統：預設多種環境模板，支援自訂模板管理
   - 備份與恢復：支援環境配置的備份和恢復功能
   - 配置驗證：自動驗證環境配置的完整性和正確性
   - 操作歷史：記錄所有 IP 切換操作，支援一鍵恢復

🖥️ UI 新增：
2. 環境管理界面：新增專用的環境管理頁籤，提供圖形化的環境配置管理
3. IP 切換工具界面：新增專用的 IP 切換工具頁籤，提供直觀的 IP 管理操作
4. 快捷鍵支援：新增 Ctrl+5 (環境管理) 和 Ctrl+6 (IP 切換工具) 快捷鍵

🔧 架構改進：
5. 模組化設計：將 IP 管理功能設計為獨立模組，易於維護和擴展
6. 配置檔案管理：使用 JSON 格式儲存環境配置，支援版本控制
7. API 更新：將所有硬編碼的 API URL 替換為動態配置

📚 文件更新：
8. 新增完整的 IP 管理系統使用指南和測試腳本
""".format(VERSION=VERSION)

def get_version_info():
    """獲取版本信息

    Returns:
        dict: 包含版本信息的字典
    """
    return {
        "major": MAJOR,
        "minor": MINOR,
        "patch": PATCH,
        "version": VERSION,
        "app_title": APP_TITLE,
        "release_date": RELEASE_DATE,
        "description": VERSION_DESCRIPTION
    }

def is_newer_version(version_to_check):
    """檢查指定的版本是否比當前版本更新

    Args:
        version_to_check (str): 要檢查的版本號，格式為 "x.y.z"

    Returns:
        bool: 如果指定的版本比當前版本更新，則返回 True，否則返回 False
    """
    try:
        # 解析版本號
        major, minor, patch = map(int, version_to_check.split('.'))

        # 比較版本號
        if major > MAJOR:
            return True
        if major == MAJOR and minor > MINOR:
            return True
        if major == MAJOR and minor == MINOR and patch > PATCH:
            return True

        return False
    except:
        # 如果解析失敗，返回 False
        return False

def get_version_string(include_v=True):
    """獲取版本號字符串

    Args:
        include_v (bool): 是否包含 "V" 前綴

    Returns:
        str: 版本號字符串
    """
    if include_v:
        return f"V{VERSION}"
    else:
        return VERSION

if __name__ == "__main__":
    # 測試代碼
    print(f"當前版本: {VERSION}")
    print(f"應用程式標題: {APP_TITLE}")
    print(f"版本發布日期: {RELEASE_DATE}")
    print(f"版本描述: {VERSION_DESCRIPTION}")

    # 測試版本比較
    test_versions = ["2.4.0", "2.5.0", "2.5.1", "2.6.0", "3.0.0"]
    for test_version in test_versions:
        print(f"{test_version} 是否比當前版本更新: {is_newer_version(test_version)}")
