"""遊戲卡片工具面板"""
import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.scrolledtext import ScrolledText
from utils.constants import PADDING
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from widgets.modern_button import ModernButton

class MemberPanel(tk.Frame):
    """遊戲卡片工具面板"""
    def __init__(self, parent):
        # 初始化主題管理器
        self.theme_manager = ThemeManager()

        # 使用 tk.Frame 而非 ttk.Frame
        super().__init__(parent, bg=self.theme_manager.get_color("surface"))

        self._init_ui()
        self._setup_button_styles()

    def _init_ui(self):
        """初始化 UI"""
        # 建立主容器框架
        container_frame = tk.Frame(self, bg=self.theme_manager.get_color("surface"))
        container_frame.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 左側設定區域 - 佔空間的 50%
        left_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING/2))

        # 右側日誌區域 - 佔空間的 50%
        right_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING/2, 0))

        # 從卡片框架模組導入 CardFrame
        from widgets.card_frame import CardFrame
        # 從幫助按鈕模組導入 HelpButton
        from widgets.help_button import HelpButton

        # 添加幫助按鈕
        help_text = """遊戲卡片工具可以幫助您管理會員的遊戲卡片，包括新增、刪除和修改卡片。

使用步驟：
1. 在「會員資訊」區域輸入平台帳號或 VP 會員 ID
2. 點擊「查詢會員」按鈕獲取會員資訊
3. 在「卡片設定與操作」區域選擇遊戲、投注額和設定卡片數量
4. 點擊「新增卡片」或「刪除所有卡片」按鈕進行操作

批次處理：
1. 在「批次處理」區域選擊「批次匯入」按鈕匯入帳號檔案
2. 選擇遊戲、投注額和設定卡片數量
3. 點擊「批次更新」按鈕新增卡片，或點擊「批次刪除」按鈕刪除卡片

所有操作日誌將顯示在右側的「操作日誌」區域。"""
        self.help_button = HelpButton(left_frame, help_text=help_text, title="遊戲卡片工具幫助")
        self.help_button.pack(side=tk.TOP, anchor=tk.NW, padx=PADDING, pady=PADDING)

        # 會員資訊區域 - 使用卡片式設計
        member_card = CardFrame(left_frame, title="會員資訊", icon=IconManager.get('user'))
        member_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        member_frame = member_card.get_content_frame()

        # 設定列的權重，使輸入欄位更寬
        member_frame.columnconfigure(0, weight=2)  # 標籤列
        member_frame.columnconfigure(1, weight=5)  # 輸入欄位列
        member_frame.columnconfigure(2, weight=2)  # 按鈕列

        # 資料庫來源選擇
        tk.Label(
            member_frame,
            text="資料庫來源",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=0, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.db_source = ttk.Combobox(
            member_frame,
            values=["OCMS", "OCIntegrator"],
            width=15,
            state="readonly",
            font=("Microsoft JhengHei UI", 10)
        )
        self.db_source.set("OCMS")  # 預設值
        self.db_source.grid(row=0, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # 平台帳號輸入
        tk.Label(
            member_frame,
            text="平台帳號",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=1, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_account = ttk.Entry(member_frame, width=25, font=("Microsoft JhengHei UI", 10))
        self.entry_account.grid(row=1, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # VP Member ID 輸入
        tk.Label(
            member_frame,
            text="VP Member ID",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=2, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_member_id = ttk.Entry(member_frame, width=25, font=("Microsoft JhengHei UI", 10))
        self.entry_member_id.grid(row=2, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # 按鈕容器 - 垂直居中
        button_container = tk.Frame(member_frame, bg=self.theme_manager.get_color("surface"))
        button_container.grid(row=0, column=2, rowspan=3, padx=PADDING, pady=PADDING, sticky="ns")

        # 查詢按鈕
        self.btn_query = ModernButton(
            button_container,
            text="查詢會員",
            icon=IconManager.get('search'),
            button_type="primary"
        )
        self.btn_query.pack(side=tk.TOP, pady=(0, PADDING))

        # 清除資料按鈕
        self.btn_clear_member = ModernButton(
            button_container,
            text="清除資料",
            icon=IconManager.get('delete'),
            button_type="danger"
        )
        self.btn_clear_member.pack(side=tk.TOP)

        # 卡片設定與操作區域 - 使用卡片式設計
        card_settings_card = CardFrame(left_frame, title="卡片設定與操作", icon=IconManager.get('game'))
        card_settings_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        card_frame = card_settings_card.get_content_frame()

        # 創建一個主框架來容納2x2網格
        card_main_frame = tk.Frame(card_frame, bg=self.theme_manager.get_color("surface"))
        card_main_frame.pack(fill=tk.X, padx=PADDING, pady=PADDING)

        # 上排左側 - 遊戲選擇
        game_select_frame = tk.Frame(card_main_frame, bg=self.theme_manager.get_color("surface"))
        game_select_frame.grid(row=0, column=0, padx=PADDING, pady=PADDING, sticky="ew")

        tk.Label(
            game_select_frame,
            text="選擇遊戲",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.LEFT, padx=PADDING)
        self.cb_game = ttk.Combobox(game_select_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.cb_game.pack(side=tk.LEFT, padx=PADDING, fill=tk.X, expand=True)

        # 上排右側 - 投注模式選擇
        bet_mode_frame = tk.Frame(card_main_frame, bg=self.theme_manager.get_color("surface"))
        bet_mode_frame.grid(row=0, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        tk.Label(
            bet_mode_frame,
            text="選擇投注模式",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.LEFT, padx=PADDING)

        self.cb_bet_mode = ttk.Combobox(bet_mode_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.cb_bet_mode.pack(side=tk.LEFT, padx=PADDING, fill=tk.X, expand=True)

        # 下排左側 - 押注線選擇
        bet_line_frame = tk.Frame(card_main_frame, bg=self.theme_manager.get_color("surface"))
        bet_line_frame.grid(row=1, column=0, padx=PADDING, pady=PADDING, sticky="ew")

        tk.Label(
            bet_line_frame,
            text="選擇投注額",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.LEFT, padx=PADDING)
        self.cb_bet_line = ttk.Combobox(bet_line_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.cb_bet_line.pack(side=tk.LEFT, padx=PADDING, fill=tk.X, expand=True)

        # 下排右側 - 卡片數量設定
        card_count_frame = tk.Frame(card_main_frame, bg=self.theme_manager.get_color("surface"))
        card_count_frame.grid(row=1, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        tk.Label(
            card_count_frame,
            text="卡片數量(1~99)",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.LEFT, padx=PADDING)
        self.entry_card_count = ttk.Entry(card_count_frame, width=10, font=("Microsoft JhengHei UI", 10))
        self.entry_card_count.pack(side=tk.LEFT, padx=PADDING)
        self.entry_card_count.insert(0, "1")

        # 添加說明標籤
        tk.Label(
            card_count_frame,
            text="(共用)",
            font=("Microsoft JhengHei UI", 9),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_secondary")
        ).pack(side=tk.LEFT, padx=(5, 0))

        # 設定列的權重，使兩列等寬
        card_main_frame.columnconfigure(0, weight=1)
        card_main_frame.columnconfigure(1, weight=1)

        # 卡片操作按鈕
        card_button_frame = tk.Frame(card_frame, bg=self.theme_manager.get_color("surface"))
        card_button_frame.pack(fill=tk.X, padx=PADDING, pady=PADDING)

        self.btn_add_card = ModernButton(
            card_button_frame,
            text="新增卡片",
            icon=IconManager.get('add'),
            button_type="primary"
        )
        self.btn_add_card.pack(side=tk.LEFT, padx=PADDING, pady=PADDING)

        self.btn_delete_all_cards = ModernButton(
            card_button_frame,
            text="刪除所有卡片",
            icon=IconManager.get('delete'),
            button_type="danger"
        )
        self.btn_delete_all_cards.pack(side=tk.LEFT, padx=PADDING, pady=PADDING)

        # 批次處理區域 - 使用卡片式設計
        batch_card = CardFrame(left_frame, title="批次處理", icon=IconManager.get('import'))
        batch_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        batch_frame = batch_card.get_content_frame()

        # 主要操作框架 - 水平排列所有元素
        main_batch_frame = tk.Frame(batch_frame, bg=self.theme_manager.get_color("surface"))
        main_batch_frame.pack(fill=tk.X, padx=PADDING, pady=PADDING)

        # 資料庫來源選擇區域
        db_source_container = tk.Frame(main_batch_frame, bg=self.theme_manager.get_color("surface"))
        db_source_container.pack(side=tk.LEFT, padx=(0, PADDING*2))

        # 資料庫來源標籤
        tk.Label(
            db_source_container,
            text="資料庫來源",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.TOP, anchor="w", pady=(0, 2))

        # 資料庫來源下拉選單
        self.batch_db_source = ttk.Combobox(
            db_source_container,
            values=["OCMS", "OCIntegrator"],
            width=15,
            state="readonly",
            font=("Microsoft JhengHei UI", 10)
        )
        self.batch_db_source.set("OCMS")  # 預設值
        self.batch_db_source.pack(side=tk.TOP, fill=tk.X)

        # 批次匯入按鈕
        self.btn_import = ModernButton(
            main_batch_frame,
            text="批次匯入",
            icon=IconManager.get('import'),
            button_type="success"
        )
        self.btn_import.pack(side=tk.LEFT, padx=PADDING)

        # 批次更新按鈕
        self.btn_batch_update = ModernButton(
            main_batch_frame,
            text="批次更新",
            icon=IconManager.get('update'),
            button_type="primary"
        )
        self.btn_batch_update.pack(side=tk.LEFT, padx=PADDING)

        # 批次刪除按鈕
        self.btn_batch_delete = ModernButton(
            main_batch_frame,
            text="批次刪除",
            icon=IconManager.get('delete'),
            button_type="danger"
        )
        self.btn_batch_delete.pack(side=tk.LEFT, padx=PADDING)

        # 批次狀態標籤
        self.batch_status_label = tk.Label(
            batch_frame,
            text="尚未匯入帳號",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        )
        self.batch_status_label.pack(anchor="w", padx=PADDING, pady=PADDING)

        # 操作日誌區域 - 使用卡片式設計
        log_card = CardFrame(right_frame, title="操作日誌與說明", icon=IconManager.get('comment'))
        log_card.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)
        log_frame = log_card.get_content_frame()

        self.debug_text = ScrolledText(log_frame, width=40, height=20, font=("Microsoft JhengHei UI", 10))
        self.debug_text.pack(fill=tk.BOTH, expand=True)

        # 初始日誌內容
        self.debug_text.insert(tk.END, "👋 歡迎使用遊戲卡片工具\n")
        self.debug_text.insert(tk.END, "⚠️ 請先查詢會員資訊，再進行卡片操作\n")
        self.debug_text.insert(tk.END, "📝 操作流程：\n")
        self.debug_text.insert(tk.END, "  1. 查詢會員 → 2. 選擇遊戲與投注額 → 3. 設定卡片數量 → 4. 新增卡片\n")
        self.debug_text.insert(tk.END, "  批次操作：匯入帳號檔案 → 選擇遊戲與投注額 → 設定卡片數量 → 批次更新/刪除\n")

        # 清除日誌按鈕
        self.btn_clear_log = ModernButton(
            log_frame,
            text="清除日誌",
            icon=IconManager.get('delete'),
            button_type="secondary",
            command=lambda: self.debug_text.delete(1.0, tk.END)
        )
        self.btn_clear_log.pack(side=tk.RIGHT, padx=PADDING, pady=PADDING)

    def _setup_button_styles(self):
        """設定按鈕樣式"""
        style = ttk.Style()

        # 主要按鈕樣式
        style.configure("Accent.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 成功按鈕樣式
        style.configure("Success.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 信息按鈕樣式
        style.configure("Info.TButton",
                        background="#2196F3",
                        foreground="white",
                        padding=5)

        # 次要按鈕樣式
        style.configure("Secondary.TButton",
                        background="#9E9E9E",
                        foreground="white",
                        padding=5)

        # 危險按鈕樣式
        style.configure("Danger.TButton",
                        background="#F44336",
                        foreground="white",
                        padding=5)

    def clear_member_info(self):
        """清除會員資訊"""
        self.entry_account.delete(0, tk.END)
        self.entry_member_id.delete(0, tk.END)
        self.log("✅ 已清除會員資訊")

    def update_batch_status(self, status_text):
        """更新批次狀態標籤"""
        self.batch_status_label.config(text=status_text)
        self.update_idletasks()

    def log(self, message):
        """記錄日誌"""
        self.debug_text.insert(tk.END, f"{message}\n")
        self.debug_text.see(tk.END)
        self.update_idletasks()

    def log_batch(self, messages):
        """批量記錄日誌，提高效率"""
        if not messages:
            return

        # 禁用文本框更新以提高效率
        self.debug_text.config(state=tk.DISABLED)

        try:
            # 一次性添加所有消息
            text = "\n".join(messages) + "\n"
            self.debug_text.config(state=tk.NORMAL)
            self.debug_text.insert(tk.END, text)
            self.debug_text.see(tk.END)
        finally:
            # 確保文本框恢復正常狀態
            self.debug_text.config(state=tk.NORMAL)

        # 只更新一次UI
        self.update_idletasks()

    def show_error(self, title, message):
        """顯示錯誤訊息"""
        messagebox.showerror(title, message)
        self.log(f"❌ 錯誤: {message}")

    def show_info(self, title, message):
        """顯示信息訊息"""
        messagebox.showinfo(title, message)
        self.log(f"ℹ️ 信息: {message}")

    def show_success(self, title, message):
        """顯示成功訊息"""
        messagebox.showinfo(title, message)
        self.log(f"✅ 成功: {message}")


