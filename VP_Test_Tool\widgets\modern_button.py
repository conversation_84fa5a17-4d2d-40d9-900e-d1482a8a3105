"""現代化按鈕元件"""
import tkinter as tk
from typing import Callable, Optional, Dict, Any
from utils.theme import ThemeManager

class ModernButton(tk.Button):
    """現代化按鈕元件

    提供更現代化的按鈕外觀和互動效果，支援不同的按鈕類型和圖示。

    Args:
        parent: 父元件
        text: 按鈕文字
        icon: 按鈕圖示 (Unicode 字元)
        command: 點擊時執行的函數
        button_type: 按鈕類型，可以是 'primary', 'secondary', 'danger', 'info', 'warning', 'success'
        **kwargs: 其他 tk.Button 參數
    """

    def __init__(
        self,
        parent,
        text: str,
        icon: Optional[str] = None,
        command: Optional[Callable] = None,
        button_type: str = "primary",
        **kwargs
    ):
        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 取得按鈕樣式
        style = self.theme_manager.get_button_style(button_type)

        # 合併樣式和其他參數
        # 設定預設的按鈕大小和內邊距
        default_kwargs = {
            "width": 10,  # 統一按鈕寬度
            "height": 1,  # 統一按鈕高度
            "padx": 5,   # 統一水平內邊距
            "pady": 3,   # 統一垂直內邊距
            "font": ("Microsoft JhengHei UI", 10)  # 統一字體大小
        }

        # 先套用預設值，再套用樣式，最後套用自定義參數
        button_kwargs = {**default_kwargs, **style, **kwargs}

        # 如果有圖示，加到文字前面
        display_text = f"{icon} {text}" if icon else text

        super().__init__(parent, text=display_text, command=command, **button_kwargs)

        # 儲存原始顏色和樣式
        self._original_bg = self["bg"]
        self._original_fg = self["fg"]
        self._original_font = self["font"]
        self._button_type = button_type

        # 添加懸停和點擊效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<ButtonPress-1>", self._on_press)
        self.bind("<ButtonRelease-1>", self._on_release)

    def _on_enter(self, event):
        """滑鼠進入時的效果"""
        # 提亮背景色 - 使用更亮的顏色
        r, g, b = self._hex_to_rgb(self._original_bg)
        hover_color = self._rgb_to_hex(min(r + 30, 255), min(g + 30, 255), min(b + 30, 255))
        self.config(bg=hover_color)

        # 增加按鈕立體感 - 使用更突出的浮雙效果
        self.config(relief=tk.RAISED, borderwidth=2)

        # 增加按鈕光暗效果 - 使用更深的陰影
        if self._button_type in ["primary", "success", "info"]:
            # 主要按鈕使用深色陰影
            self.config(highlightbackground="#333333", highlightthickness=1)
        else:
            # 次要按鈕使用深色陰影
            self.config(highlightbackground="#666666", highlightthickness=1)

        # 保持按鈕大小不變，只增加粗細
        current_font = self["font"]
        if isinstance(current_font, tuple) and len(current_font) >= 2:
            # 如果是元組格式
            family, size = current_font[0], current_font[1]
            # 只增加粗細，不改變大小
            self.config(font=(family, size, "bold"))
        elif isinstance(current_font, str):
            # 如果是字串格式，嘗試解析
            try:
                font_parts = current_font.split()
                if len(font_parts) >= 2:
                    family = font_parts[0]
                    size = int(font_parts[1])
                    # 只增加粗細，不改變大小
                    self.config(font=(family, size, "bold"))
            except:
                pass

        # 增加文字陰影效果，使文字更清晰
        self._add_text_shadow()

    def _on_leave(self, event):
        """滑鼠離開時的效果"""
        # 恢復背景色
        self.config(bg=self._original_bg)

        # 恢復按鈕樣式
        self.config(relief=tk.RAISED, borderwidth=1)

        # 移除陰影效果
        self.config(highlightbackground=self._original_bg, highlightthickness=0)

        # 恢復文字顏色
        self.config(fg=self._original_fg)

        # 恢復文字樣式
        if hasattr(self, '_original_font'):
            self.config(font=self._original_font)
        else:
            # 如果沒有儲存原始字體，嘗試使用主題管理器的字體
            try:
                self.config(font=self.theme_manager.get_font("label"))
            except:
                pass

    def _on_press(self, event):
        """滑鼠點擊時的效果"""
        # 使用更深的背景色
        r, g, b = self._hex_to_rgb(self._original_bg)
        pressed_color = self._rgb_to_hex(max(r - 30, 0), max(g - 30, 0), max(b - 30, 0))
        self.config(bg=pressed_color)

        # 使用壓下效果
        self.config(relief=tk.SUNKEN, borderwidth=2)

        # 輕微移動文字位置來增強壓下感
        self.config(padx=self["padx"]+1, pady=self["pady"]+1)

    def _on_release(self, event):
        """滑鼠釋放時的效果"""
        # 如果滑鼠仍在按鈕上，恢復懸停效果
        # 否則恢復原始狀態
        x, y = event.x, event.y
        if 0 <= x <= self.winfo_width() and 0 <= y <= self.winfo_height():
            self._on_enter(event)
        else:
            self._on_leave(event)

        # 恢復文字位置
        self.config(padx=self["padx"]-1, pady=self["pady"]-1)

    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """將十六進位色碼轉換為 RGB 值"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _rgb_to_hex(self, r: int, g: int, b: int) -> str:
        """將 RGB 值轉換為十六進位色碼"""
        return f'#{r:02x}{g:02x}{b:02x}'

    def _add_text_shadow(self):
        """添加文字陰影效果

        在 Tkinter 中沒有直接的文字陰影效果，所以我們使用一些視覺技巧來增強文字的可見度。
        這裡我們使用更粗的字體和更大的字體大小來增強文字的可見度。
        """
        # 取得目前的文字和背景顏色
        fg_color = self["fg"]
        bg_color = self["bg"]

        # 確保文字和背景顏色對比度足夠
        # 如果背景是深色，文字應該是白色
        # 如果背景是淺色，文字應該是黑色
        r, g, b = self._hex_to_rgb(bg_color)
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        if brightness < 128:  # 背景是深色
            self.config(fg="#FFFFFF")  # 白色文字
        else:  # 背景是淺色
            self.config(fg="#000000")  # 黑色文字

    def set_loading(self, is_loading: bool = True):
        """設定按鈕為載入中狀態

        Args:
            is_loading: 是否為載入中狀態
        """
        if is_loading:
            self._original_text = self["text"]
            self.config(text="⏳ 處理中...", state=tk.DISABLED)
        else:
            if hasattr(self, '_original_text'):
                self.config(text=self._original_text, state=tk.NORMAL)

    def flash(self, times: int = 3, interval: int = 500):
        """閃爍按鈕

        Args:
            times: 閃爍次數
            interval: 閃爍間隔 (毫秒)
        """
        # 如果已經有閃爍任務在執行，先取消它
        if hasattr(self, '_flash_job') and self._flash_job:
            try:
                self.after_cancel(self._flash_job)
                self._flash_job = None
            except Exception:
                pass

        self._flash_count = 0
        self._flash_max = times * 2  # 一次閃爍包含亮->暗->亮，所以乘以 2
        self._flash_interval = interval // 2  # 間隔時間除以 2，因為一次閃爍包含兩個狀態變化
        self._do_flash()

    def _do_flash(self):
        """執行閃爍效果"""
        # 檢查視窗是否仍然存在
        try:
            if not self.winfo_exists():
                return
        except Exception:
            return

        if self._flash_count >= self._flash_max:
            # 閃爍結束，恢復原始背景色
            try:
                self.config(bg=self._original_bg)
            except Exception:
                pass
            return

        try:
            if self._flash_count % 2 == 0:
                # 閃爍：亮 -> 暗
                self.config(bg=self.theme_manager.get_color("accent"))
            else:
                # 閃爍：暗 -> 亮
                self.config(bg=self._original_bg)

            self._flash_count += 1
            # 保存 after 任務的 ID，以便後續取消
            self._flash_job = self.after(self._flash_interval, self._do_flash)
        except Exception:
            # 如果發生異常，停止閃爍
            return
