@echo off
chcp 65001 >nul
title VP Test Tool V2.6.1 啟動器

echo ========================================
echo    VP Test Tool V2.6.1 啟動器
echo ========================================
echo.
echo 正在啟動 VP Test Tool...
echo.

REM 檢查主程式是否存在
if not exist "VP_Test_Tool.exe" (
    echo 錯誤：找不到 VP_Test_Tool.exe
    echo 請確認您在正確的目錄下運行此批次檔
    pause
    exit /b 1
)

REM 檢查配置文件
if not exist "config.json" (
    echo 警告：找不到 config.json 配置文件
    echo 程式將使用預設配置
    echo.
)

REM 啟動程式
echo 啟動中...
start "" "VP_Test_Tool.exe"

REM 等待一下確認程式啟動
timeout /t 3 /nobreak >nul

echo.
echo VP Test Tool 已啟動！
echo 如果程式沒有出現，請檢查：
echo 1. 防毒軟體是否阻擋程式
echo 2. 是否有足夠的系統權限
echo 3. 系統是否安裝了必要的運行時庫
echo.
echo 按任意鍵關閉此視窗...
pause >nul
