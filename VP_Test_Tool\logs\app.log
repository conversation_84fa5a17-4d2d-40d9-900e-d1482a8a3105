2025-04-11 10:05:48,596 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:05:48,596 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:05:48,596 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:05:48,596 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:05:48,597 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:05:48,597 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:05:48,597 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:05:48,597 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:10:49,921 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:10:49,921 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:10:49,921 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:10:49,922 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:10:49,922 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:10:49,923 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:10:49,923 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:10:49,923 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:18:23,767 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:18:23,767 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:18:23,767 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:18:23,767 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:18:23,767 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:18:23,768 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:18:23,768 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:18:23,768 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:21:08,656 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:21:08,656 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:21:08,657 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:25:58,621 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:25:58,621 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:25:58,622 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:25:58,622 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:25:58,622 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:25:58,622 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:25:58,622 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:25:58,623 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:27:36,060 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:27:36,061 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:34:28,835 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:34:28,835 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:34:28,835 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:34:28,835 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:34:28,836 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:34:28,836 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:34:28,836 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:34:28,836 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:35:13,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 10:35:13,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 10:35:13,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 10:35:13,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 10:35:13,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 10:35:13,365 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 10:35:13,365 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 10:35:13,365 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 10:43:44,978 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 10:53:12,761 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 10:57:17,595 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:11:17,316 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:13:08,913 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:21:13,028 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:24:45,591 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:25:15,740 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:28:44,603 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:29:07,793 - __main__ - ERROR - 應用程式啟動失敗: unknown option "-font"
2025-04-11 11:29:41,445 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:29:41,445 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:29:41,445 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:29:41,446 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:29:41,446 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:29:41,446 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:29:41,446 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:29:41,446 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:30:23,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:30:23,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:30:23,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:30:23,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:30:23,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:30:23,636 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:30:23,636 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:30:23,636 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:33:19,994 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:33:19,994 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:33:19,994 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:33:19,995 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:33:19,995 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:33:19,995 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:33:19,995 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:33:19,995 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:35:00,304 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:35:00,304 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:35:00,305 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:37:58,925 - __main__ - ERROR - 應用程式啟動失敗: Layout CustomNotebook not found
2025-04-11 11:39:43,513 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:39:43,513 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:39:43,514 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:39:43,514 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:39:43,514 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:39:43,514 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:39:43,514 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:39:43,515 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:41:33,860 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:41:33,860 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:41:33,860 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:41:33,860 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:41:33,860 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:41:33,861 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:41:33,861 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:41:33,861 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:45:45,916 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:45:45,916 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:45:45,916 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:45:45,917 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:45:45,917 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:45:45,917 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:45:45,917 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:45:45,917 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:48:38,383 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:48:39,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:48:39,309 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:48:39,309 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:51:46,630 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 11:51:47,478 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:51:47,478 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:51:47,478 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:51:47,479 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:51:47,479 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:51:47,479 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:51:47,479 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:51:47,479 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:56:45,570 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 11:56:46,378 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:56:46,378 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:56:46,378 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:56:46,379 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:56:46,379 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:56:46,379 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:56:46,379 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:56:46,379 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:57:31,765 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 11:57:32,578 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:57:32,578 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:57:32,579 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 11:58:54,502 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 11:58:55,306 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 11:58:55,306 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 11:58:55,306 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 11:58:55,307 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 11:58:55,307 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 11:58:55,307 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 11:58:55,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 11:58:55,308 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 12:44:17,405 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 12:44:18,363 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 12:44:18,364 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 12:46:11,413 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 12:46:12,252 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 12:46:12,252 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 12:46:12,252 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 12:46:12,253 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 12:46:12,253 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 12:46:12,253 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 12:46:12,253 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 12:46:12,253 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 12:48:29,913 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 12:48:30,773 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 12:48:30,774 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:13:19,190 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 13:13:20,004 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:13:20,004 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:13:20,004 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:13:20,005 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:13:20,005 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:13:20,005 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:13:20,005 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:13:20,005 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:22:20,825 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 13:22:21,840 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:22:21,840 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:22:21,840 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:22:21,840 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:22:21,840 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:22:21,841 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:22:21,841 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:22:21,841 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:29:34,932 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 13:29:35,957 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:29:35,958 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:31:45,435 - __main__ - WARNING - 無法載入應用程式圖示: No module named 'PIL'
2025-04-11 13:31:46,443 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:31:46,443 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:31:46,443 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:31:46,443 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:31:46,443 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:31:46,444 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:31:46,444 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:31:46,444 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:33:08,273 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:33:08,274 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-11 13:53:59,002 - __main__ - INFO - 使用預設圖示
2025-04-11 13:54:00,072 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-11 13:54:00,072 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-11 13:54:00,072 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-11 13:54:00,072 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-11 13:54:00,072 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-11 13:54:00,073 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-11 13:54:00,073 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-11 13:54:00,073 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-16 15:38:08,936 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-16 15:38:08,937 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-16 15:38:09,225 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-16 15:38:09,226 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-16 15:38:09,226 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-16 15:38:09,226 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-16 15:38:09,226 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-16 15:38:09,227 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-16 15:38:09,227 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-16 15:38:09,227 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-16 15:38:09,227 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-16 15:38:09,227 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-16 15:43:58,386 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-16 15:43:58,387 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-16 15:43:58,652 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-16 15:43:58,653 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-16 15:43:58,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-16 15:43:58,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-16 15:43:58,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-16 15:43:58,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-16 15:43:58,654 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-16 15:43:58,654 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-16 15:43:58,654 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-16 15:43:58,654 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-16 15:45:34,533 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-16 15:45:34,535 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-16 15:45:34,789 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-16 15:45:34,790 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-16 15:45:34,790 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-16 15:45:34,791 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-16 15:51:37,985 - __main__ - INFO - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-16 15:51:40,184 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-16 15:51:40,185 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-16 15:51:40,422 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-16 15:51:40,423 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-16 15:51:40,423 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-16 15:51:40,423 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-16 15:51:40,423 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-16 15:51:40,424 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-16 15:51:40,425 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-16 15:51:40,425 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-16 15:51:40,425 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-16 15:51:40,425 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-17 15:54:21,309 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-17 15:56:34,774 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-17 15:56:37,576 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-17 15:56:37,577 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-17 15:56:37,962 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-17 15:56:37,963 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-17 15:56:37,963 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-17 15:56:37,963 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-17 15:56:37,963 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-17 15:56:37,964 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-17 15:56:37,964 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-17 15:56:37,964 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-17 15:56:37,964 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-17 15:56:37,964 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-17 16:04:55,309 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-17 16:04:58,129 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-17 16:04:58,130 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-17 16:04:58,502 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-17 16:04:58,503 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-17 16:04:58,503 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-17 16:04:58,503 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-17 16:04:58,504 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-17 16:12:44,186 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-17 16:12:47,020 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-17 16:12:47,020 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-17 16:12:47,357 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-17 16:12:47,358 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-17 16:12:47,358 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-17 16:12:47,358 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-17 16:12:47,358 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-17 16:12:47,358 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-17 16:12:47,359 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-17 16:12:47,359 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-17 16:12:47,359 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-17 16:12:47,359 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-17 16:17:01,318 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-17 16:17:04,100 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-17 16:17:04,101 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-17 16:17:04,523 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-17 16:17:04,523 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-17 16:17:04,523 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-17 16:17:04,524 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 14:25:09,504 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 14:25:11,907 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 14:25:11,908 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 14:25:12,202 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 14:25:12,203 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 14:25:12,203 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 14:25:12,204 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 14:27:19,678 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 14:27:21,886 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 14:27:21,886 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 14:27:22,162 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 14:27:22,163 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 14:27:22,163 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 14:27:22,164 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:13:01,378 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:13:03,889 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:13:03,890 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:13:04,134 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:13:04,134 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:13:04,135 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:13:04,136 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:17:36,733 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:17:43,080 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:17:45,390 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:17:45,391 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:17:45,652 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:17:45,653 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:17:45,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:25:11,755 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:25:14,001 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:25:14,002 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:25:14,258 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:25:14,259 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:25:14,259 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:25:14,260 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:30:16,052 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:30:18,312 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:30:18,313 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:30:18,582 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:30:18,583 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:30:18,583 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:30:18,583 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:30:18,583 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:30:18,583 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:30:18,583 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:30:18,584 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:30:18,584 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:30:18,584 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:46:43,233 - __main__ - INFO - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:46:45,589 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:46:45,603 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:46:45,863 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:46:45,863 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:46:45,863 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:46:45,864 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 15:55:14,698 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 15:55:17,104 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 15:55:17,106 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 15:55:17,367 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 15:55:17,369 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 15:55:17,369 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 15:55:17,370 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 16:06:20,882 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:06:21,596 - __main__ - ERROR - 應用程式啟動失敗: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-04-28 16:06:30,710 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:06:31,408 - __main__ - ERROR - 應用程式啟動失敗: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-04-28 16:06:38,492 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:06:39,169 - __main__ - ERROR - 應用程式啟動失敗: bad option "-width": must be -after, -anchor, -before, -expand, -fill, -in, -ipadx, -ipady, -padx, -pady, or -side
2025-04-28 16:08:06,798 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:08:09,107 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 16:08:09,108 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 16:08:09,347 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 16:08:09,348 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 16:08:09,348 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 16:35:28,675 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:35:30,983 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 16:35:30,984 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 16:35:31,233 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 16:35:31,233 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 16:35:31,233 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 16:35:31,234 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 16:35:31,235 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 16:40:50,826 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:40:53,070 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 16:40:53,071 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 16:40:53,328 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 16:40:53,329 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 16:40:53,330 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 16:40:53,330 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 16:40:53,331 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 16:44:39,791 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:44:42,064 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 16:44:42,065 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 16:44:42,327 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 16:44:42,328 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 16:44:42,328 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 16:44:42,328 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 16:44:42,328 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 16:44:42,328 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 16:44:42,328 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 16:44:42,329 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 16:44:42,329 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 16:44:42,329 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 16:50:00,465 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 16:50:02,709 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 16:50:02,712 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 16:50:02,966 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 16:50:02,967 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 16:50:02,967 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 16:50:02,967 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 16:50:02,967 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 16:50:02,967 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 16:50:02,968 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 16:50:02,968 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 16:50:02,968 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 16:50:02,968 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:03:38,996 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:03:41,310 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:03:41,311 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:03:41,638 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:03:41,638 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:03:41,638 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:03:41,639 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:03:41,639 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:10:30,179 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:10:32,404 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:10:32,404 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:10:32,660 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:10:32,661 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:10:32,661 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:10:32,661 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:10:32,661 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:10:32,661 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:10:32,662 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:10:32,662 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:10:32,662 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:10:32,662 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:13:54,450 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:13:56,809 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:13:56,810 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:13:57,083 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:13:57,083 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:13:57,083 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:13:57,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:23:01,861 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:23:04,120 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:23:04,121 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:23:04,361 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:23:04,361 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:23:04,361 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:23:04,361 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:23:04,361 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:23:04,361 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:23:04,362 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:23:04,362 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:23:04,362 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:23:04,362 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:30:16,447 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:30:52,576 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:30:52,576 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:30:52,807 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:30:52,807 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:30:52,807 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:30:52,807 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:30:52,808 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:31:05,623 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:31:51,256 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:31:51,257 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:31:51,531 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:31:51,531 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:31:51,531 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:31:51,531 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:31:51,532 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:33:13,917 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:33:16,227 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:33:16,228 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:33:16,480 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:33:16,480 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:33:16,481 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 17:47:46,192 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 17:47:48,376 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 17:47:48,377 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 17:47:48,633 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 17:47:48,634 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 17:47:48,634 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 17:47:48,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-28 19:16:10,760 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-28 19:16:13,032 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-28 19:16:13,033 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-28 19:16:13,297 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-28 19:16:13,298 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-28 19:16:13,298 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-28 19:16:13,299 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 10:05:06,380 - __main__ - INFO - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 10:05:08,679 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-29 10:05:08,679 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-29 10:05:09,078 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-29 10:05:09,078 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-29 10:05:09,078 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 10:05:09,079 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 10:05:09,080 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 11:59:14,843 - __main__ - INFO - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 11:59:17,066 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-29 11:59:17,068 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-29 11:59:17,586 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-29 11:59:17,587 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 11:59:17,587 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 12:54:12,357 - __main__ - INFO - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 12:54:14,846 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-29 12:54:14,846 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-29 12:54:15,486 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-29 12:54:15,487 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 12:54:15,487 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 12:54:15,488 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 12:54:15,488 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 13:12:48,977 - __main__ - INFO - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 13:12:51,335 - __main__ - INFO - 成功初始化遊戲卡片工具控制器
2025-04-29 13:12:51,336 - __main__ - INFO - 成功初始化資源調整工具控制器
2025-04-29 13:12:51,925 - __main__ - INFO - 成功初始化帳號產生器控制器
2025-04-29 13:12:51,926 - __main__ - INFO - 成功初始化 RNG 控制器
2025-04-29 13:12:51,926 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 13:12:51,926 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 13:12:51,926 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 13:12:51,927 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 13:12:51,927 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 13:12:51,927 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 13:12:51,927 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 13:12:51,927 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 15:41:36,385 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 15:41:36,385 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 15:41:36,386 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 15:41:36,391 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 39194.04 MB, 使用率 40.0%
2025-04-29 15:41:36,392 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 15:41:36,403 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 15:41:36,749 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 15:41:36,750 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 15:41:37,652 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 15:41:37,692 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 15:41:37,698 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 15:41:37,698 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 15:41:39,180 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 15:41:39,181 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 15:41:39,779 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 15:41:39,780 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 15:41:39,780 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 15:41:39,780 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 15:41:39,780 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 15:41:39,781 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 15:41:39,782 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 15:41:39,782 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 15:41:39,782 - INFO - 43800 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 16:37:04,664 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 16:37:04,678 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 16:37:04,678 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 16:37:04,679 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 16:37:04,684 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38278.68 MB, 使用率 41.4%
2025-04-29 16:37:04,685 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 16:37:04,694 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 16:37:04,886 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 16:37:04,887 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 16:37:06,988 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 16:37:07,022 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 16:37:07,023 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 16:37:07,023 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 16:37:08,662 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 16:37:08,664 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 16:37:09,204 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 16:37:09,206 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 16:37:09,206 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 16:37:09,206 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 16:37:09,206 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 16:37:09,206 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 16:37:09,206 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 16:37:09,207 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 16:37:09,207 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 16:37:09,207 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 16:37:09,208 - INFO - 39376 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 16:38:41,509 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 16:38:41,523 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 16:38:41,524 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 16:38:41,524 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 16:38:41,530 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38232.96 MB, 使用率 41.5%
2025-04-29 16:38:41,530 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 16:38:41,540 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 16:38:41,800 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 16:38:41,801 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 16:38:43,646 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 16:38:43,672 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 16:38:43,678 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 16:38:43,678 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 16:38:45,142 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 16:38:45,143 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 16:38:45,528 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 16:38:45,529 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 16:38:45,529 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 16:38:45,530 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 16:38:45,530 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 16:38:45,530 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 16:38:45,530 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 16:38:45,530 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 16:38:45,530 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 16:38:45,530 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 16:38:45,530 - INFO - 43780 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 16:41:34,810 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 16:41:34,828 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 16:41:34,828 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 16:41:34,829 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 16:41:34,835 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 37769.02 MB, 使用率 42.2%
2025-04-29 16:41:34,836 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 16:41:34,847 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 16:41:35,087 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 16:41:35,088 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 16:41:36,969 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 16:41:36,993 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 16:41:37,000 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 16:41:37,000 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 16:41:42,999 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 16:41:42,999 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 16:41:43,535 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 16:41:43,536 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 16:41:43,536 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 16:41:43,537 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 16:41:43,537 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 16:41:43,537 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 16:41:43,537 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 16:41:43,537 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 16:41:43,537 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 16:41:43,537 - INFO - 32236 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 16:47:21,072 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 16:47:21,088 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 16:47:21,088 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 16:47:21,089 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 16:47:21,094 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 37759.58 MB, 使用率 42.2%
2025-04-29 16:47:21,095 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 16:47:21,102 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 16:47:21,389 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 16:47:21,390 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 16:47:23,130 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 16:47:23,157 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 16:47:23,164 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 16:47:23,165 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 16:47:24,828 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 16:47:24,828 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 16:47:25,435 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 16:47:25,436 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 16:47:25,436 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 16:47:25,437 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 16:47:25,438 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 16:47:25,438 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 16:47:25,438 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 16:47:25,438 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 16:47:25,438 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 16:47:25,439 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 16:47:25,439 - INFO - 41428 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 17:22:24,558 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 17:22:24,578 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 17:22:24,579 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 17:22:24,579 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 17:22:24,586 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36804.12 MB, 使用率 43.6%
2025-04-29 17:22:24,586 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 17:22:24,595 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 17:22:24,847 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 17:22:24,847 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 17:22:26,616 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 17:22:26,644 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 17:22:26,644 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 17:22:26,645 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 17:22:28,212 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 17:22:28,213 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 17:22:29,663 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 17:22:29,664 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 17:22:29,664 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 17:22:29,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 17:22:29,665 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 17:22:29,666 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 17:22:29,666 - INFO - 7044 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 17:29:28,774 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 17:29:28,791 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 17:29:28,791 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 17:29:28,791 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 17:29:28,797 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36570.99 MB, 使用率 44.0%
2025-04-29 17:29:28,798 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 17:29:28,805 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 17:29:29,101 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 17:29:29,102 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 17:29:30,705 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 17:29:30,733 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 17:29:30,733 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 17:29:30,734 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 17:29:32,264 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 17:29:32,265 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 17:29:32,815 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 17:29:32,816 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 17:29:32,817 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 17:29:32,818 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 17:29:32,818 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 17:29:32,818 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 17:29:32,818 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 17:29:32,819 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 17:29:32,819 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 17:29:32,819 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 17:29:32,819 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 17:29:32,819 - INFO - 39428 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 17:39:27,693 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 17:39:27,717 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 17:39:27,717 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 17:39:27,718 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 17:39:27,724 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36560.61 MB, 使用率 44.0%
2025-04-29 17:39:27,724 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.61 GB, 使用率 95.4%
2025-04-29 17:39:27,732 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 17:39:27,924 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 17:39:27,925 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 17:39:29,542 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 17:39:29,561 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 17:39:29,567 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 17:39:29,567 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 17:39:31,117 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 17:39:31,118 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 17:39:32,314 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 17:39:32,324 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 17:39:32,334 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 17:39:32,334 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 17:39:32,335 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 17:39:32,335 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 17:39:32,335 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 17:39:32,344 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 17:39:32,344 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 17:39:32,345 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 17:39:32,345 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 17:39:32,345 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 17:39:32,346 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 17:39:32,347 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 17:39:32,347 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 17:39:32,355 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 17:39:32,356 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 17:39:32,358 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 17:39:32,359 - INFO - 39488 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 19:40:52,421 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 19:40:52,436 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 19:40:52,436 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 19:40:52,436 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 19:40:52,443 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36758.02 MB, 使用率 43.7%
2025-04-29 19:40:52,443 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 19:40:52,452 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 19:40:52,798 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 19:40:52,799 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 19:40:54,278 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 19:40:54,295 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 19:40:54,302 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 19:40:54,302 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 19:40:56,197 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 19:40:56,197 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 19:40:56,564 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 19:40:56,565 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 19:40:56,565 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 19:40:56,565 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 19:40:56,565 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 19:40:56,566 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 19:40:56,566 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 19:40:56,567 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 19:40:56,567 - INFO - 34388 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 19:53:18,962 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 19:53:18,977 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 19:53:18,978 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 19:53:18,978 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 19:53:18,984 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36580.75 MB, 使用率 44.0%
2025-04-29 19:53:18,984 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 19:53:18,993 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 19:53:19,255 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 19:53:19,255 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 19:53:20,814 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 19:53:20,835 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 19:53:20,836 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 19:53:20,836 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 19:53:22,725 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 19:53:22,727 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 19:53:23,056 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 19:53:23,057 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 19:53:23,057 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 19:53:23,057 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 19:53:23,057 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 19:53:23,057 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 19:53:23,058 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 19:53:23,059 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 19:53:23,059 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 19:53:23,059 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 19:53:23,059 - INFO - 11488 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-29 20:00:55,569 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 20:00:55,592 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 20:00:55,593 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 20:00:55,594 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 20:00:55,608 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36361.80 MB, 使用率 44.3%
2025-04-29 20:00:55,609 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 20:00:55,622 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:00:55,794 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:00:55,797 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:00:57,447 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 20:00:57,469 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 20:00:57,469 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 20:00:57,469 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 20:00:59,427 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 20:00:59,428 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 20:00:59,757 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 20:00:59,758 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 20:00:59,758 - INFO - 40192 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 20:00:59,822 - INFO - 40192 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-29 20:01:00,912 - ERROR - 40192 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 308, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-29 20:03:27,155 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 20:03:27,179 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 20:03:27,179 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 20:03:27,179 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 20:03:27,187 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36590.82 MB, 使用率 44.0%
2025-04-29 20:03:27,187 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 20:03:27,195 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:03:27,346 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:03:27,349 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:03:28,740 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 20:03:28,756 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 20:03:28,762 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 20:03:28,762 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 20:03:30,693 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 20:03:30,694 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 20:03:31,211 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 20:03:31,211 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 20:03:31,212 - INFO - 43556 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 20:03:31,254 - INFO - 43556 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-29 20:03:32,335 - WARNING - 43556 - MainThread - app - enhanced_logger.py:245 - 顯示根視窗失敗: main thread is not in main loop，嘗試使用替代方法
2025-04-29 20:03:33,436 - ERROR - 43556 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 311, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 315, in main
    root.attributes('-alpha', 1.0)  # 設置透明度為 1.0（完全不透明）
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2032, in wm_attributes
    return self.tk.call(args)
           ^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-29 20:07:05,212 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 20:07:05,228 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 20:07:05,229 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 20:07:05,229 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 20:07:05,235 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36460.78 MB, 使用率 44.2%
2025-04-29 20:07:05,235 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 20:07:05,243 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:07:05,438 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:07:05,443 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:07:06,858 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 20:07:06,880 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 20:07:06,881 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 20:07:06,881 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 20:07:08,825 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 20:07:08,826 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 20:07:09,126 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 20:07:09,127 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 20:07:09,127 - INFO - 16564 - Thread-2 (_run_init) - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 20:07:09,152 - INFO - 16564 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-29 20:07:10,248 - WARNING - 16564 - MainThread - app - enhanced_logger.py:245 - 顯示根視窗失敗: main thread is not in main loop，嘗試使用替代方法
2025-04-29 20:07:11,331 - ERROR - 16564 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 311, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 315, in main
    root.attributes('-alpha', 1.0)  # 設置透明度為 1.0（完全不透明）
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2032, in wm_attributes
    return self.tk.call(args)
           ^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-29 20:12:08,489 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 20:12:08,564 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 20:12:08,565 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 20:12:08,565 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 20:12:08,570 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36414.15 MB, 使用率 44.2%
2025-04-29 20:12:08,571 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 20:12:08,632 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-29 20:12:08,686 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-29 20:12:08,752 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:08,812 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:08,933 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:12:08,934 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:12:08,985 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:09,089 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:12:09,089 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:12:09,090 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:10,651 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 20:12:10,962 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:11,057 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:12:11,061 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:12:11,071 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 20:12:11,123 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 20:12:11,123 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 20:12:11,187 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:12:11,287 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:12:11,287 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:12:11,288 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:13:08,255 - WARNING - 29036 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-29 20:13:08,255 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-29 20:13:08,255 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-29 20:13:08,256 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-29 20:13:08,256 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-29 20:13:08,261 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36527.47 MB, 使用率 44.1%
2025-04-29 20:13:08,262 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-29 20:13:08,262 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-29 20:13:08,262 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-29 20:13:08,263 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-29 20:13:08,443 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-29 20:13:08,444 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-29 20:13:08,667 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-29 20:13:08,670 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-29 20:13:08,671 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-29 20:13:08,671 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-29 20:13:10,639 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-29 20:13:10,640 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-29 20:13:10,997 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-29 20:13:10,998 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-29 20:13:10,998 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-29 20:13:11,011 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-29 20:13:11,012 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-29 20:13:11,013 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-29 20:13:11,013 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-29 20:13:11,013 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-29 20:13:11,013 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-29 20:13:11,013 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-29 20:13:11,013 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-29 20:13:11,013 - INFO - 29036 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 09:32:39,881 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:32:39,974 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:32:39,975 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:32:39,975 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:32:39,981 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34896.85 MB, 使用率 46.6%
2025-04-30 09:32:39,982 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:32:40,053 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:32:40,108 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:32:40,172 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:40,231 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:40,362 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:32:40,362 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:32:40,414 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:40,527 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:32:40,528 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:32:40,528 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:41,834 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:32:42,159 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:42,275 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:32:42,279 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:32:42,288 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:32:42,338 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:32:42,339 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:32:42,389 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:32:42,506 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:32:42,507 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:32:42,507 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:07,792 - WARNING - 43504 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-30 09:33:07,793 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:33:07,794 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:33:07,794 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:33:07,794 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:33:07,801 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34933.68 MB, 使用率 46.5%
2025-04-30 09:33:07,802 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:33:07,804 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:33:07,805 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:33:07,806 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:08,155 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:33:08,161 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:33:08,413 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:33:08,431 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:33:08,448 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:33:08,466 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:33:10,237 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 09:33:10,241 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 09:33:10,610 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 09:33:10,611 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 09:33:10,612 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 09:33:10,629 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 09:33:10,635 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-30 09:33:10,643 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-30 09:33:10,644 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-30 09:33:10,644 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-30 09:33:10,644 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-30 09:33:10,645 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-30 09:33:10,653 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-30 09:33:10,663 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-30 09:33:10,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-30 09:33:10,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-30 09:33:10,665 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-30 09:33:10,667 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-30 09:33:10,668 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-30 09:33:10,669 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-30 09:33:10,669 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 09:33:10,671 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 09:33:15,712 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:33:15,784 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:33:15,784 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:33:15,785 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:33:15,791 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34873.22 MB, 使用率 46.6%
2025-04-30 09:33:15,791 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:33:15,851 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:33:15,909 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:33:15,975 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:16,034 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:16,165 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:33:16,165 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:33:16,221 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:16,351 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:33:16,352 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:33:16,352 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:17,654 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:33:17,946 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:18,066 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:33:18,067 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:33:18,076 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:33:18,127 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:33:18,128 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:33:18,187 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:33:18,303 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:33:18,303 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:33:18,304 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:40,478 - WARNING - 5528 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-30 09:35:40,479 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:35:40,479 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:35:40,480 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:35:40,480 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:35:40,487 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34796.58 MB, 使用率 46.7%
2025-04-30 09:35:40,488 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:35:40,488 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:35:40,489 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:35:40,489 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:40,696 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:35:40,697 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:35:40,982 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:35:40,990 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:35:40,990 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:35:40,991 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:35:42,760 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 09:35:42,761 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 09:35:43,064 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 09:35:43,065 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 09:35:43,065 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 09:35:43,077 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 09:35:43,083 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-30 09:35:43,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-30 09:35:43,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-30 09:35:43,084 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-30 09:35:43,085 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-30 09:35:43,086 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-30 09:35:43,086 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-30 09:35:43,086 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-30 09:35:43,087 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-30 09:35:43,087 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-30 09:35:43,087 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-30 09:35:43,087 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-30 09:35:43,088 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-30 09:35:43,090 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-30 09:35:43,091 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 09:35:43,092 - INFO - 5528 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 09:35:54,099 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:35:54,178 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:35:54,179 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:35:54,179 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:35:54,185 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34787.68 MB, 使用率 46.7%
2025-04-30 09:35:54,186 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:35:54,258 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:35:54,314 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:35:54,380 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:54,442 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:54,669 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:35:54,669 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:35:54,721 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:54,898 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:35:54,898 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:35:54,898 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:56,483 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:35:56,806 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:56,983 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:35:56,984 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:35:56,993 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:35:57,044 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:35:57,044 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:35:57,108 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:35:57,258 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:35:57,259 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:35:57,259 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:39:36,778 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:39:36,851 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:39:36,852 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:39:36,852 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:39:36,858 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34686.66 MB, 使用率 46.9%
2025-04-30 09:39:36,858 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:39:36,921 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:39:36,976 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:39:37,042 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:39:37,204 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:39:37,205 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:39:38,575 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:39:38,657 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:39:38,714 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:39:38,715 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:40:08,143 - WARNING - 37480 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-30 09:40:08,143 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:40:08,144 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:40:08,144 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:40:08,144 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:40:08,149 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34607.42 MB, 使用率 47.0%
2025-04-30 09:40:08,150 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:40:08,150 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:40:08,151 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:40:08,151 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:40:08,354 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:40:08,355 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:40:08,571 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:40:08,576 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:40:08,576 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:40:08,576 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:40:09,778 - WARNING - 43504 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-30 09:40:09,778 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:40:09,779 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:40:09,779 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:40:09,779 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:40:09,786 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34601.08 MB, 使用率 47.0%
2025-04-30 09:40:09,786 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:40:09,787 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:40:09,787 - INFO - 43504 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:40:09,787 - ERROR - 43504 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 373, in main
    results = splash.show()
              ^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 74, in show
    raise self.init_error
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 177, in _run_next_step
    result = step_func()
             ^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 276, in _init_controllers
    main_window = _init_main_window()
                  ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 225, in _init_main_window
    main_window = MainWindow(_root_window, _config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 396, in main
    root = _init_root_window()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 182, in _init_root_window
    if _root_window is not None and _root_window.winfo_exists():
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-04-30 09:40:10,318 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 09:40:10,319 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 09:40:10,610 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 09:40:10,611 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 09:40:10,612 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 09:40:10,639 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 09:40:10,639 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-30 09:40:10,639 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-30 09:40:10,639 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-30 09:40:10,640 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-30 09:40:10,641 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-30 09:40:10,641 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-30 09:40:10,641 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-30 09:40:10,641 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-30 09:40:10,641 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 09:40:10,641 - INFO - 37480 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 09:44:30,689 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:44:30,768 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:44:30,768 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:44:30,768 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:44:30,775 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34431.56 MB, 使用率 47.3%
2025-04-30 09:44:30,775 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:44:30,845 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:44:30,902 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:44:30,966 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:44:31,169 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:44:31,170 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:44:32,585 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:44:32,667 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:44:32,722 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:44:32,723 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:46:22,411 - WARNING - 37200 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: Theme MyTheme already exists，使用直接初始化
2025-04-30 09:46:22,412 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:46:22,412 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:46:22,412 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:46:22,413 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:46:22,420 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34330.30 MB, 使用率 47.4%
2025-04-30 09:46:22,421 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:46:22,421 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:46:22,421 - INFO - 37200 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:46:22,422 - ERROR - 37200 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 373, in main
    # 檢查是否有 get_available_features 方法
            ^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 74, in show
    raise self.init_error
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 177, in _run_next_step
    result = step_func()
             ^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 276, in _init_controllers
    resource_frame = tk.Frame(_root_window)
          ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 225, in _init_main_window
    _root_window = None
              ^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 396, in main
    _init_system_info,
           ^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 182, in _init_root_window
    try:
         
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-04-30 09:47:50,625 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:47:50,696 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:47:50,696 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:47:50,696 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:47:50,702 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34526.12 MB, 使用率 47.1%
2025-04-30 09:47:50,702 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:47:50,767 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:47:50,822 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:47:50,887 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:47:51,056 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:47:51,056 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:47:52,545 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:47:52,626 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:47:52,678 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:47:52,678 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:47:52,748 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化主視窗失敗: Theme MyTheme already exists
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 247, in _init_main_window
    main_window = MainWindow(_root_window, _config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists
2025-04-30 09:47:52,753 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:47:52,754 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化遊戲卡片工具控制器失敗: 'NoneType' object has no attribute 'member_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 327, in _init_controllers
    main_window.member_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'member_panel'
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化資源調整工具控制器失敗: 'NoneType' object has no attribute 'resource_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 338, in _init_controllers
    main_window.resource_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'resource_panel'
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化帳號產生器控制器失敗: 'NoneType' object has no attribute 'account_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 349, in _init_controllers
    main_window.account_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'account_panel'
2025-04-30 09:47:52,755 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化 RNG 控制器失敗: 'NoneType' object has no attribute 'rng_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 360, in _init_controllers
    main_window.rng_panel,
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'rng_panel'
2025-04-30 09:47:52,880 - INFO - 35380 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 09:59:28,125 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 09:59:28,200 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 09:59:28,201 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 09:59:28,201 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 09:59:28,207 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34358.95 MB, 使用率 47.4%
2025-04-30 09:59:28,207 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 09:59:28,271 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 09:59:28,326 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 09:59:28,389 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 09:59:28,771 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 09:59:28,772 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 09:59:30,443 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 09:59:30,533 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 09:59:30,596 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:59:30,596 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:59:30,661 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 09:59:30,662 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 09:59:32,356 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 09:59:32,357 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 09:59:32,750 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 09:59:32,752 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 09:59:32,816 - INFO - 42184 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:01:07,902 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:01:07,966 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:01:07,967 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:01:07,967 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:01:07,973 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34387.11 MB, 使用率 47.3%
2025-04-30 10:01:07,973 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:01:08,035 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:01:08,092 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:01:08,172 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:01:08,373 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:01:08,373 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:01:09,924 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:01:10,008 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:01:10,075 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:01:10,076 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:01:10,139 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:01:10,139 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:01:13,380 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:01:13,380 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:01:14,034 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:01:14,035 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:01:14,095 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:31:13,744 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:31:13,818 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:31:13,819 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:31:13,819 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:31:13,826 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34094.48 MB, 使用率 47.8%
2025-04-30 10:31:13,826 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:31:13,887 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:31:13,943 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:31:14,010 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:31:14,202 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:31:14,203 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:31:15,664 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:31:15,755 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:31:15,810 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:31:15,810 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:31:15,865 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:31:15,866 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:31:17,036 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:31:17,046 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:31:17,046 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,046 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-s> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-o> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,047 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F6> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,048 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F7> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,048 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:17,048 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 10:31:17,048 - INFO - 35656 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:31:17,054 - ERROR - 35656 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    main_window.show_message(f"歡迎使用 {APP_TITLE}", "info")
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:31:17,729 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:31:17,730 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:31:18,054 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:31:18,055 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:31:18,106 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:31:38,181 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:31:38,192 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:31:38,192 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-s> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-o> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,193 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F6> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,194 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F7> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,194 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:31:38,194 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 10:31:38,194 - INFO - 18580 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:31:38,199 - ERROR - 18580 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    main_window.show_message(f"歡迎使用 {APP_TITLE}", "info")
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:31:43,185 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:31:43,251 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:31:43,251 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:31:43,251 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:31:43,257 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34008.14 MB, 使用率 47.9%
2025-04-30 10:31:43,258 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:31:43,324 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:31:43,376 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:31:43,441 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:31:43,670 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:31:43,671 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:31:45,333 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:31:45,418 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:31:45,483 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:31:45,483 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:31:45,553 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:31:45,553 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:31:47,415 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:31:47,415 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:31:47,742 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:31:47,743 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:31:47,799 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:32:06,724 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:32:06,735 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-s> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-o> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-1> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,736 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F2> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F3> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F4> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F6> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <F7> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - utils.keyboard_shortcuts - ERROR - 註冊快捷鍵失敗: <Control-5> - can't invoke "bind" command: application has been destroyed
2025-04-30 10:32:06,737 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 10:32:06,737 - INFO - 40588 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:32:06,758 - ERROR - 40588 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    try:
  File "d:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:32:53,456 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:32:53,526 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:32:53,527 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:32:53,527 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:32:53,534 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33916.16 MB, 使用率 48.1%
2025-04-30 10:32:53,534 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:32:53,596 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:32:53,654 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:32:53,720 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:32:53,904 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:32:53,905 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:32:55,410 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:32:55,502 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:32:55,556 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:32:55,556 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:32:55,607 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:32:55,607 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:32:57,379 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:32:57,380 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:32:57,671 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:32:57,671 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:32:57,725 - INFO - 30992 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:34:48,401 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:34:48,471 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:34:48,471 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:34:48,471 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:34:48,479 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33924.59 MB, 使用率 48.1%
2025-04-30 10:34:48,479 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:34:48,543 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:34:48,599 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:34:48,663 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:34:48,866 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:34:48,866 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:34:50,551 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:34:50,636 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:34:50,705 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:34:50,706 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:34:50,765 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:34:50,766 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:34:52,460 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:34:52,461 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:34:52,762 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:34:52,763 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:34:52,814 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:38:44,146 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:38:44,294 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:38:44,294 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:38:44,295 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:38:44,301 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33874.95 MB, 使用率 48.1%
2025-04-30 10:38:44,302 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:38:44,371 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:38:44,427 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:38:44,490 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:38:44,732 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:38:44,733 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:38:46,268 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:38:46,353 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:38:46,414 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:38:46,415 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:38:46,477 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:38:46,477 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:38:46,908 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:38:46,917 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:38:46,917 - WARNING - 35260 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:38:46,917 - INFO - 35260 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:38:46,922 - WARNING - 35260 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 10:38:46,922 - WARNING - 35260 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:38:46,922 - ERROR - 35260 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:38:48,208 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:38:48,209 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:38:48,532 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:38:48,533 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:38:48,597 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:39:30,224 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:39:30,319 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:39:30,319 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:39:30,320 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:39:30,326 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33850.18 MB, 使用率 48.2%
2025-04-30 10:39:30,327 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:39:30,396 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:39:30,451 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:39:30,518 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:39:30,828 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:39:30,829 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:39:32,269 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:39:32,353 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:39:32,410 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:39:32,411 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:39:32,470 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:39:32,470 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:39:34,259 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:39:34,260 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:39:34,591 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:39:34,592 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:39:34,651 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:39:36,228 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:39:36,241 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:39:36,241 - WARNING - 39332 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:39:36,241 - INFO - 39332 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:39:36,248 - WARNING - 39332 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 10:39:36,249 - WARNING - 39332 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:39:36,249 - ERROR - 39332 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:42:24,807 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:42:24,815 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:42:24,816 - WARNING - 28848 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:42:24,816 - INFO - 28848 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:42:24,821 - WARNING - 28848 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 10:42:24,821 - WARNING - 28848 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:42:24,821 - ERROR - 28848 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:45:00,271 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:45:00,352 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:45:00,352 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:45:00,353 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:45:00,362 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34211.71 MB, 使用率 47.6%
2025-04-30 10:45:00,363 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:45:00,430 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:45:00,487 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:45:00,553 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:45:00,911 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:45:00,911 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:45:02,302 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:45:02,399 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:45:02,453 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:45:02,453 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:45:02,523 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:45:02,523 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:45:04,379 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:45:04,380 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:45:04,689 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:45:04,690 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:45:04,741 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:45:49,792 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:45:49,866 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:45:49,866 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:45:49,866 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:45:49,872 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34197.11 MB, 使用率 47.6%
2025-04-30 10:45:49,872 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:45:49,930 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:45:49,986 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:45:50,049 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:45:50,207 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:45:50,208 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:45:51,617 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:45:51,701 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:45:51,753 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:45:51,754 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:45:51,823 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:45:51,823 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:45:53,634 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:45:53,635 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:45:53,944 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:45:53,945 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:45:53,997 - INFO - 9088 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:45:54,025 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 10:45:54,035 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 10:45:54,035 - WARNING - 25624 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:45:54,035 - INFO - 25624 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 10:45:54,039 - WARNING - 25624 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 10:45:54,040 - WARNING - 25624 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 10:45:54,040 - ERROR - 25624 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 681, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:47:23,037 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:47:23,109 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:47:23,109 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:47:23,109 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:47:23,116 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34417.32 MB, 使用率 47.3%
2025-04-30 10:47:23,116 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:47:23,190 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:47:23,251 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:47:23,314 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:47:23,545 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:47:23,545 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:47:25,307 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:47:25,397 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:47:25,449 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:47:25,450 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:47:25,514 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:47:25,515 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:47:27,269 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:47:27,270 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:47:27,607 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:47:27,607 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:47:27,726 - INFO - 2412 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:52:44,271 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:52:44,348 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:52:44,349 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:52:44,349 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:52:44,355 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34113.22 MB, 使用率 47.8%
2025-04-30 10:52:44,355 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:52:44,418 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:52:44,483 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:52:44,568 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:52:44,816 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:52:44,817 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:52:46,201 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:52:46,284 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:52:46,336 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:52:46,336 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:52:46,404 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:52:46,405 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:52:48,141 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:52:48,141 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:52:48,568 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:52:48,568 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:52:48,628 - INFO - 36192 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:54:57,543 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:54:57,618 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:54:57,619 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:54:57,619 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:54:57,625 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33961.79 MB, 使用率 48.0%
2025-04-30 10:54:57,625 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:54:57,688 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:54:57,746 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:54:57,809 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:54:57,978 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:54:57,978 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:54:59,796 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:54:59,884 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:54:59,945 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:54:59,945 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:55:00,004 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:55:00,005 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:55:01,851 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:55:01,852 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:55:02,174 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:55:02,175 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:55:02,239 - INFO - 4316 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 10:57:36,921 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 10:57:36,991 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 10:57:36,991 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 10:57:36,991 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 10:57:36,998 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33665.61 MB, 使用率 48.5%
2025-04-30 10:57:36,998 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 10:57:37,064 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 10:57:37,119 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 10:57:37,183 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 10:57:37,364 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 10:57:37,365 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 10:57:39,825 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 10:57:39,939 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 10:57:40,014 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:57:40,015 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:57:40,076 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 10:57:40,087 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 10:57:42,227 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 10:57:42,228 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 10:57:42,705 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 10:57:42,706 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 10:57:42,756 - INFO - 41976 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:00:31,670 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 11:00:31,735 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 11:00:31,735 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 11:00:31,735 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 11:00:31,741 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34063.95 MB, 使用率 47.8%
2025-04-30 11:00:31,741 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 11:00:31,806 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 11:00:31,862 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 11:00:31,928 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 11:00:32,084 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:00:32,084 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 11:00:33,823 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 11:00:33,908 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 11:00:33,964 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:00:33,964 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:00:34,018 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:00:34,018 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:00:35,921 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 11:00:35,922 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 11:00:36,249 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 11:00:36,249 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 11:00:36,313 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:28:21,899 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 11:28:21,911 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 11:28:21,911 - WARNING - 24828 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 11:28:21,911 - INFO - 24828 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 11:28:21,918 - WARNING - 24828 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 11:28:21,919 - WARNING - 24828 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 11:28:21,919 - ERROR - 24828 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 694, in main
    # 啟動應用程式
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 11:30:57,575 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 11:30:57,645 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 11:30:57,645 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 11:30:57,645 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 11:30:57,656 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34389.40 MB, 使用率 47.3%
2025-04-30 11:30:57,656 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 11:30:57,726 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 11:30:57,783 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 11:30:57,850 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 11:30:58,013 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:30:58,013 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,164 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:30:58,164 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,165 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化主視窗：根視窗初始化失敗
2025-04-30 11:30:58,308 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:30:58,309 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,309 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化資源監控元件：根視窗初始化失敗
2025-04-30 11:30:58,361 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:30:58,362 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:30:58,658 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:30:58,658 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,659 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化主視窗：根視窗初始化失敗
2025-04-30 11:30:58,660 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:30:58,660 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:30:58,660 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化遊戲卡片工具控制器失敗: 'NoneType' object has no attribute 'member_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 368, in _init_controllers
    main_window.member_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'member_panel'
2025-04-30 11:30:58,661 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化資源調整工具控制器失敗: 'NoneType' object has no attribute 'resource_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 379, in _init_controllers
    main_window.resource_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'resource_panel'
2025-04-30 11:30:58,661 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化帳號產生器控制器失敗: 'NoneType' object has no attribute 'account_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 390, in _init_controllers
    main_window.account_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'account_panel'
2025-04-30 11:30:58,662 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化 RNG 控制器失敗: 'NoneType' object has no attribute 'rng_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 401, in _init_controllers
    main_window.rng_panel,
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'rng_panel'
2025-04-30 11:30:59,118 - INFO - 35052 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:48:06,572 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 11:48:06,643 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 11:48:06,643 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 11:48:06,643 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 11:48:06,649 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34521.09 MB, 使用率 47.1%
2025-04-30 11:48:06,649 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 11:48:06,714 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 11:48:06,769 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 11:48:06,841 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 11:48:07,032 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:48:07,033 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 11:48:08,459 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 11:48:08,552 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 11:48:08,603 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:48:08,604 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:48:08,656 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:48:08,656 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:48:10,496 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 11:48:10,497 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 11:48:10,792 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 11:48:10,793 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 11:48:10,843 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:51:27,629 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 11:51:27,700 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 11:51:27,700 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 11:51:27,701 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 11:51:27,707 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34177.14 MB, 使用率 47.7%
2025-04-30 11:51:27,708 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 11:51:27,782 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 11:51:27,840 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 11:51:27,910 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 11:51:28,241 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:51:28,242 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 11:51:29,699 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 11:51:29,789 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 11:51:29,844 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:51:29,844 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:51:29,926 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:51:29,927 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:51:30,605 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 11:51:30,615 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 11:51:30,615 - WARNING - 11608 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 11:51:30,616 - INFO - 11608 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 11:51:30,629 - WARNING - 11608 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 11:51:30,629 - WARNING - 11608 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 11:51:30,630 - ERROR - 11608 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 693, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 11:51:31,710 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 11:51:31,711 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 11:51:32,057 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 11:51:32,058 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 11:51:32,115 - INFO - 33340 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:55:05,331 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 11:55:05,404 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 11:55:05,404 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 11:55:05,404 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 11:55:05,412 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34082.16 MB, 使用率 47.8%
2025-04-30 11:55:05,412 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 11:55:05,472 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 11:55:05,529 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 11:55:05,593 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 11:55:05,788 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 11:55:05,789 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 11:55:07,208 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 11:55:07,299 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 11:55:07,359 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:55:07,359 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:55:07,420 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 11:55:07,421 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 11:55:09,201 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 11:55:09,202 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 11:55:09,524 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 11:55:09,525 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 11:55:09,576 - INFO - 38928 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 11:59:31,853 - ERROR - 43660 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 437, in main
    feature_detector = FeatureDetectorMock()
                       ^^^^^^^^^^^^^^^^^^^
NameError: name 'FeatureDetectorMock' is not defined
2025-04-30 13:05:50,629 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 13:05:50,698 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 13:05:50,698 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 13:05:50,698 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 13:05:50,704 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34413.61 MB, 使用率 47.3%
2025-04-30 13:05:50,704 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 13:05:50,774 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 13:05:50,830 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 13:05:50,894 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 13:05:51,085 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 13:05:51,086 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 13:05:52,572 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 13:05:52,662 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 13:05:52,718 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:05:52,719 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:05:52,790 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:05:52,790 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:05:54,514 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 13:05:54,515 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 13:05:54,834 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 13:05:54,835 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 13:05:54,886 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 13:10:46,125 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 13:10:46,197 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 13:10:46,197 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 13:10:46,198 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 13:10:46,204 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33917.94 MB, 使用率 48.1%
2025-04-30 13:10:46,204 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 13:10:46,267 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 13:10:46,326 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 13:10:46,389 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 13:10:46,619 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 13:10:46,620 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 13:10:48,276 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 13:10:48,364 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 13:10:48,447 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:10:48,447 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:10:48,508 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:10:48,508 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:10:50,275 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 13:10:50,276 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 13:10:50,612 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 13:10:50,613 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 13:10:50,669 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 13:11:18,280 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-04-30 13:11:18,289 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 13:11:18,289 - WARNING - 36396 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 13:11:18,290 - INFO - 36396 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 13:11:18,294 - WARNING - 36396 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-04-30 13:11:18,295 - WARNING - 36396 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-04-30 13:11:18,295 - ERROR - 36396 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 13:11:53,746 - WARNING - 43736 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: can't invoke "winfo" command: application has been destroyed，使用直接初始化
2025-04-30 13:11:53,747 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-04-30 13:11:53,747 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-04-30 13:11:53,747 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-04-30 13:11:53,748 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-04-30 13:11:53,758 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33927.93 MB, 使用率 48.1%
2025-04-30 13:11:53,759 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.60 GB, 使用率 95.4%
2025-04-30 13:11:53,760 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-04-30 13:11:53,760 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-04-30 13:11:53,760 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-04-30 13:11:53,881 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: D:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-04-30 13:11:53,882 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-04-30 13:11:54,142 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-04-30 13:11:54,149 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-04-30 13:11:54,149 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:11:54,150 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:11:54,150 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-04-30 13:11:54,150 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-04-30 13:11:55,850 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-04-30 13:11:55,851 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-04-30 13:11:56,155 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-04-30 13:11:56,157 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-04-30 13:11:56,157 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-04-30 13:11:56,167 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-04-30 13:11:56,167 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-04-30 13:11:56,168 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-04-30 13:11:56,169 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-04-30 13:11:56,169 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-04-30 13:11:56,169 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-04-30 13:11:56,173 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-04-30 18:59:04,483 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:04,484 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:04,495 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:04,495 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:04,495 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:04,496 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-04-30 18:59:04,496 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:05,498 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:05,499 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已取消所有待處理的 after 調用
2025-04-30 18:59:05,504 - WARNING - 43736 - MainThread - app - enhanced_logger.py:245 - 關閉視窗時發生異常: can't delete Tcl command
2025-04-30 18:59:07,139 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:07,140 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:07,147 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:07,147 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:07,148 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:07,148 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:07,148 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:09,474 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:09,474 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:09,481 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:09,482 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:09,482 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:09,482 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:09,483 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:14,066 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:14,066 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:14,072 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:14,072 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:14,073 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:14,073 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:14,073 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:14,624 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:14,624 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:14,630 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:14,630 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:14,630 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:14,631 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:14,631 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:15,081 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:15,081 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:15,087 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:15,087 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:15,087 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:15,088 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:15,088 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-04-30 18:59:18,423 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-04-30 18:59:18,424 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-04-30 18:59:18,432 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:18,432 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-04-30 18:59:18,432 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-04-30 18:59:18,432 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-04-30 18:59:18,433 - INFO - 43736 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-02 10:42:44,149 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-02 10:42:44,223 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-02 10:42:44,224 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-02 10:42:44,224 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-02 10:42:44,230 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38584.62 MB, 使用率 40.9%
2025-05-02 10:42:44,230 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.65 GB, 使用率 95.4%
2025-05-02 10:42:44,291 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-02 10:42:44,349 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-02 10:42:44,413 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-02 10:42:44,591 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-02 10:42:44,592 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-02 10:42:46,484 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-02 10:42:46,574 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-02 10:42:46,637 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-02 10:42:46,637 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-02 10:42:46,696 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-02 10:42:46,696 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-02 10:42:48,478 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-02 10:42:48,479 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-02 10:42:48,743 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-02 10:42:48,744 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-02 10:42:48,794 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-02 18:57:13,350 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-02 18:57:13,375 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-02 18:57:13,377 - WARNING - 34356 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-02 18:57:13,377 - INFO - 34356 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-02 18:57:13,386 - WARNING - 34356 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-02 18:57:13,387 - WARNING - 34356 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-02 18:57:13,388 - ERROR - 34356 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-05 15:19:53,369 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-05 15:19:53,459 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-05 15:19:53,459 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-05 15:19:53,460 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-05 15:19:53,470 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 31852.24 MB, 使用率 51.2%
2025-05-05 15:19:53,470 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.65 GB, 使用率 95.4%
2025-05-05 15:19:53,545 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-05 15:19:53,599 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-05 15:19:53,665 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-05 15:19:53,906 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-05 15:19:53,907 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-05 15:19:56,516 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-05 15:19:56,611 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-05 15:19:56,677 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-05 15:19:56,677 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-05 15:19:56,734 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-05 15:19:56,734 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-05 15:19:59,010 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-05 15:19:59,012 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-05 15:19:59,375 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-05 15:19:59,377 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-05 15:19:59,431 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-07 14:54:45,219 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-07 14:54:45,262 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-07 14:54:45,263 - WARNING - 43132 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-07 14:54:45,263 - INFO - 43132 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-07 14:54:45,366 - WARNING - 43132 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-07 14:54:45,366 - WARNING - 43132 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-07 14:54:45,367 - ERROR - 43132 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-07 14:55:11,706 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-07 14:55:11,785 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-07 14:55:11,785 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-07 14:55:11,786 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-07 14:55:11,794 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 28233.55 MB, 使用率 56.8%
2025-05-07 14:55:11,794 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.64 GB, 使用率 95.4%
2025-05-07 14:55:11,855 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-07 14:55:11,909 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-07 14:55:11,974 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-07 14:55:12,135 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-07 14:55:12,136 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-07 14:55:14,304 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-07 14:55:14,400 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-07 14:55:14,452 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-07 14:55:14,452 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-07 14:55:14,515 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-07 14:55:14,516 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-07 14:55:16,445 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-07 14:55:16,446 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-07 14:55:16,700 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-07 14:55:16,700 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-07 14:55:16,759 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-07 16:01:30,763 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-07 16:01:30,773 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-07 16:01:30,773 - WARNING - 55908 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-07 16:01:30,774 - INFO - 55908 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-07 16:01:30,784 - WARNING - 55908 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-07 16:01:30,784 - WARNING - 55908 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-07 16:01:30,785 - ERROR - 55908 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-08 11:00:57,826 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-08 11:00:57,899 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-08 11:00:57,900 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-08 11:00:57,900 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-08 11:00:57,905 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 43040.86 MB, 使用率 34.1%
2025-05-08 11:00:57,906 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.42 GB, 使用率 95.4%
2025-05-08 11:00:57,967 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-08 11:00:58,025 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-08 11:00:58,087 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-08 11:00:58,252 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-08 11:00:58,252 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-08 11:01:00,095 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-08 11:01:00,198 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-08 11:01:00,249 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:01:00,249 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:01:00,304 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:01:00,304 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:01:02,173 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-08 11:01:02,174 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-08 11:01:02,459 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-08 11:01:02,460 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-08 11:01:02,510 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-08 11:36:11,813 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-08 11:36:11,827 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-08 11:36:11,827 - WARNING - 32904 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:36:11,828 - INFO - 32904 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-08 11:36:11,851 - WARNING - 32904 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-08 11:36:11,851 - WARNING - 32904 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:36:11,851 - ERROR - 32904 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-08 11:43:52,269 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-08 11:43:52,362 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-08 11:43:52,362 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-08 11:43:52,362 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-08 11:43:52,367 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 37018.60 MB, 使用率 43.3%
2025-05-08 11:43:52,368 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.65 GB, 使用率 95.4%
2025-05-08 11:43:52,427 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-08 11:43:52,485 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-08 11:43:52,547 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-08 11:43:52,714 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-08 11:43:52,719 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-08 11:43:54,233 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-08 11:43:54,324 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-08 11:43:54,375 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:43:54,376 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:43:54,463 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:43:54,464 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:43:56,208 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-08 11:43:56,209 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-08 11:43:56,446 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-08 11:43:56,447 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-08 11:43:56,505 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-08 11:53:32,464 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-08 11:53:32,477 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-08 11:53:32,477 - WARNING - 41808 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:32,477 - INFO - 41808 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-08 11:53:32,482 - WARNING - 41808 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-08 11:53:32,482 - WARNING - 41808 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:32,483 - ERROR - 41808 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 719, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:54,286 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-08 11:53:54,357 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-08 11:53:54,357 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-08 11:53:54,358 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-08 11:53:54,364 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36901.80 MB, 使用率 43.5%
2025-05-08 11:53:54,364 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 42.65 GB, 使用率 95.4%
2025-05-08 11:53:54,427 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-08 11:53:54,484 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-08 11:53:54,550 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-08 11:53:54,778 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-08 11:53:54,778 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-08 11:53:56,231 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-08 11:53:56,322 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-08 11:53:56,376 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:53:56,376 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:53:56,431 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-08 11:53:56,431 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-08 11:53:58,251 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-08 11:53:58,252 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-08 11:53:58,510 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-08 11:53:58,510 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-08 11:53:58,646 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-08 11:53:58,886 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-08 11:53:58,901 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-08 11:53:58,902 - WARNING - 41180 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:58,902 - INFO - 41180 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-08 11:53:58,902 - WARNING - 41180 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-08 11:53:58,903 - WARNING - 41180 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:58,903 - ERROR - 41180 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 719, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-13 10:04:56,426 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 10:04:56,526 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 10:04:56,527 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 10:04:56,527 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 10:04:56,531 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 44287.21 MB, 使用率 32.2%
2025-05-13 10:04:56,532 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.27 GB, 使用率 95.7%
2025-05-13 10:04:56,596 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 10:04:56,656 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 10:04:56,721 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 10:04:56,884 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 10:04:56,885 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 10:04:59,139 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 10:04:59,255 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 10:04:59,322 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 10:04:59,323 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 10:04:59,374 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 10:04:59,374 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 10:05:00,858 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 10:05:00,860 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 10:05:01,193 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 10:05:01,194 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 10:05:01,249 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:19:23,764 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:19:23,776 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:19:23,777 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:23,777 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:19:23,801 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-13 14:19:23,802 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:23,802 - ERROR - 34544 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:37,301 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:19:37,396 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38692.13 MB, 使用率 40.8%
2025-05-13 14:19:37,396 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:19:37,459 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:19:37,517 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:19:37,579 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:19:37,772 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:19:37,772 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:19:39,164 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:19:39,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:19:39,304 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:39,305 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:39,370 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:39,370 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:41,246 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:19:41,246 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:19:41,524 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:19:41,524 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:19:41,577 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:19:58,961 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:19:58,961 - WARNING - 15052 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: list index out of range，使用直接初始化
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:19:58,967 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38749.76 MB, 使用率 40.7%
2025-05-13 14:19:58,967 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:19:59,138 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:19:59,139 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:19:59,380 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:19:59,385 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:19:59,386 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:59,386 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:59,387 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:59,387 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:20:01,161 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:20:01,161 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:20:01,412 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:20:01,413 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:20:01,413 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:20:01,426 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:20:01,426 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F1> - 顯示快捷鍵說明
2025-05-13 14:20:01,426 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-s> - 儲存
2025-05-13 14:20:01,426 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-o> - 開啟設定
2025-05-13 14:20:01,426 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-1> - 切換到資源調整工具
2025-05-13 14:20:01,427 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-2> - 切換到遊戲卡片工具
2025-05-13 14:20:01,427 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-3> - 切換到帳號產生器
2025-05-13 14:20:01,427 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-4> - 切換到 Slot Set RNG
2025-05-13 14:20:01,427 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F5> - 重新整理
2025-05-13 14:20:01,427 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F2> - 顯示網絡狀態
2025-05-13 14:20:01,428 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F3> - 顯示內存狀態
2025-05-13 14:20:01,428 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F4> - 導出日誌
2025-05-13 14:20:01,428 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F6> - 顯示功能報告
2025-05-13 14:20:01,428 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <F7> - 檢查更新
2025-05-13 14:20:01,428 - utils.keyboard_shortcuts - INFO - 已註冊快捷鍵: <Control-5> - 切換到功能檢測
2025-05-13 14:20:01,428 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-13 14:20:01,428 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:20:01,431 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-13 14:25:03,291 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:03,291 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:03,299 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:03,299 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:03,300 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:03,300 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-13 14:25:04,311 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:05,321 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:05,322 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已取消所有待處理的 after 調用和閃爍效果
2025-05-13 14:25:05,350 - WARNING - 15052 - MainThread - app - enhanced_logger.py:245 - 關閉視窗時發生異常: can't delete Tcl command
2025-05-13 14:25:05,358 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:05,358 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:05,366 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:05,366 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:06,308 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:06,309 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:06,315 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:06,956 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:06,956 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:06,963 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:06,963 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,236 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,237 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,242 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,244 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,469 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,469 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,477 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,477 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,662 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,663 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,669 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:10,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:10,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:10,258 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:10,258 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:14,882 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:14,884 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:14,889 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:14,890 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:14,890 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:14,891 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:14,892 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:30,658 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:25:30,754 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38448.60 MB, 使用率 41.1%
2025-05-13 14:25:30,754 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:25:30,818 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:25:30,873 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:25:30,938 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:25:31,147 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:25:31,148 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:25:32,775 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:25:32,863 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:25:32,917 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:25:32,918 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:25:32,972 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:25:32,972 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:25:34,775 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:25:34,776 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:25:35,047 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:25:35,048 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:25:35,103 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:28:46,616 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:28:46,729 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:28:46,729 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:28:46,730 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:28:46,735 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38386.61 MB, 使用率 41.2%
2025-05-13 14:28:46,736 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:28:46,801 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:28:46,857 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:28:46,921 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:28:47,114 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:28:47,114 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:28:48,612 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:28:48,702 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:28:48,766 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:28:48,767 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:28:48,834 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:28:48,834 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:28:50,649 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:28:50,650 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:28:50,921 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:28:50,922 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:28:50,973 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:32:51,872 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:32:51,960 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:32:51,961 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:32:51,961 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:32:51,967 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38322.00 MB, 使用率 41.3%
2025-05-13 14:32:51,968 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:32:52,029 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:32:52,086 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:32:52,150 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:32:52,340 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:32:52,341 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:32:53,829 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:32:53,918 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:32:53,970 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:32:53,970 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:32:54,026 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:32:54,027 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:32:55,804 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:32:55,805 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:32:56,072 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:32:56,072 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:32:56,124 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:43:00,027 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:43:00,037 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:43:00,037 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:43:00,037 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:43:00,038 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-13 14:43:00,038 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:43:00,038 - ERROR - 22000 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-14 15:41:58,965 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-14 15:41:59,061 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-14 15:41:59,061 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-14 15:41:59,061 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-14 15:41:59,067 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 34635.32 MB, 使用率 47.0%
2025-05-14 15:41:59,067 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.37 GB, 使用率 95.7%
2025-05-14 15:41:59,139 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-14 15:41:59,194 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-14 15:41:59,258 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-14 15:41:59,420 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-14 15:41:59,421 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-14 15:42:01,676 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-14 15:42:01,872 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-14 15:42:01,952 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-14 15:42:01,971 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-14 15:42:02,031 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-14 15:42:02,031 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-14 15:42:03,789 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-14 15:42:03,792 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-14 15:42:04,134 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-14 15:42:04,136 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-14 15:42:04,188 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-14 16:33:09,507 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-14 16:33:09,528 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-14 16:33:09,529 - WARNING - 23184 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-14 16:33:09,529 - INFO - 23184 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-14 16:33:09,558 - WARNING - 23184 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-14 16:33:09,560 - WARNING - 23184 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-14 16:33:09,561 - ERROR - 23184 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-14 17:18:50,743 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-14 17:18:50,834 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-14 17:18:50,835 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-14 17:18:50,835 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-14 17:18:50,840 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 35201.52 MB, 使用率 46.1%
2025-05-14 17:18:50,840 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.37 GB, 使用率 95.7%
2025-05-14 17:18:50,899 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-14 17:18:50,955 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-14 17:18:51,018 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-14 17:18:51,191 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-14 17:18:51,191 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-14 17:18:54,214 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-14 17:18:54,340 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-14 17:18:54,443 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-14 17:18:54,444 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-14 17:18:54,496 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-14 17:18:54,497 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-14 17:18:56,113 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-14 17:18:56,114 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-14 17:18:56,392 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-14 17:18:56,393 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-14 17:18:56,444 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-14 17:55:42,366 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-14 17:55:42,381 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-14 17:55:42,381 - WARNING - 9976 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-14 17:55:42,381 - INFO - 9976 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-14 17:55:42,386 - WARNING - 9976 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-14 17:55:42,386 - WARNING - 9976 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-14 17:55:42,386 - ERROR - 9976 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 09:59:58,307 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 09:59:58,378 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 09:59:58,379 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 09:59:58,380 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 09:59:58,386 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33691.60 MB, 使用率 48.4%
2025-05-16 09:59:58,386 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.95 GB, 使用率 95.7%
2025-05-16 09:59:58,452 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 09:59:58,507 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 09:59:58,572 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 09:59:58,747 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 09:59:58,753 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 10:00:00,931 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 10:00:01,017 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 10:00:01,081 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 10:00:01,081 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 10:00:01,150 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 10:00:01,150 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 10:00:02,563 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 10:00:02,564 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 10:00:02,873 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 10:00:02,874 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 10:00:02,933 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 11:19:36,070 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 11:19:36,094 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 11:19:36,094 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:19:36,095 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 11:19:36,101 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 11:19:36,102 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:19:36,102 - ERROR - 12728 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:52:23,693 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 11:52:23,779 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36743.54 MB, 使用率 43.7%
2025-05-16 11:52:23,780 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 11:52:23,855 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 11:52:23,908 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 11:52:23,973 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 11:52:24,201 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 11:52:24,201 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 11:52:25,893 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 11:52:25,983 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 11:52:26,047 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:52:26,047 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:52:26,110 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:52:26,110 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:58:11,119 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 11:58:11,186 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 11:58:11,186 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 11:58:11,187 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 11:58:11,192 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 40121.91 MB, 使用率 38.6%
2025-05-16 11:58:11,192 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 11:58:11,260 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 11:58:11,318 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 11:58:11,382 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 11:58:11,603 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 11:58:11,603 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 11:58:13,220 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 11:58:13,314 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 11:58:13,367 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:58:13,367 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:58:13,430 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:58:13,430 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:30,665 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:04:30,666 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:04:30,934 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:04:30,935 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:04:30,996 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 12:04:43,549 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 12:04:43,624 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 12:04:43,624 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 12:04:43,625 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 12:04:43,630 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 41230.38 MB, 使用率 36.9%
2025-05-16 12:04:43,630 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 12:04:43,693 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 12:04:43,749 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 12:04:43,814 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 12:04:44,065 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 12:04:44,066 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 12:04:45,717 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 12:04:45,802 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 12:04:45,860 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:04:45,860 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:45,932 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:04:45,932 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:47,715 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:04:47,715 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:04:47,980 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:04:47,981 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:04:48,038 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 12:05:34,181 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 12:05:34,192 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 12:05:34,192 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:34,193 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 12:05:34,198 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 12:05:34,198 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:34,199 - ERROR - 42920 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:42,740 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 12:05:42,812 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 12:05:42,812 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 12:05:42,813 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 12:05:42,819 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 41224.61 MB, 使用率 36.9%
2025-05-16 12:05:42,820 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 12:05:42,883 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 12:05:42,940 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 12:05:43,003 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 12:05:43,165 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 12:05:43,168 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 12:05:44,619 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 12:05:44,706 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 12:05:44,766 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:05:44,766 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:05:44,824 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:05:44,824 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:05:46,185 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:05:46,185 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:05:46,439 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:05:46,439 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:05:46,500 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 18:14:19,695 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 18:14:19,722 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 18:14:19,724 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 18:14:19,725 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 18:14:19,733 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 18:14:19,734 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 18:14:19,735 - ERROR - 35736 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-28 13:30:25,043 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-28 13:30:25,133 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-28 13:30:25,134 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-28 13:30:25,134 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-28 13:30:25,139 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36123.23 MB, 使用率 44.7%
2025-05-28 13:30:25,140 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.95 GB, 使用率 95.7%
2025-05-28 13:30:25,199 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-28 13:30:25,259 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-28 13:30:25,320 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-28 13:30:25,485 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-28 13:30:25,486 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-28 13:30:27,253 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-28 13:30:27,357 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-28 13:30:27,412 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 13:30:27,412 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 13:30:27,468 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 13:30:27,468 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 13:30:28,811 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-28 13:30:28,812 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-28 13:30:29,125 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-28 13:30:29,125 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-28 13:30:29,132 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化環境管理控制器
2025-05-28 13:30:29,147 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 成功初始化 IP 切換控制器
2025-05-28 13:30:29,205 - INFO - 29936 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
