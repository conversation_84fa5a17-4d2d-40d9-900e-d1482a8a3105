# IP 管理系統使用指南

## 概述

VP Test Tool 的 IP 管理系統提供了一個靈活且易於維護的方式來管理不同環境的 API 伺服器配置。系統支援多環境配置、快速 IP 切換、配置模板和歷史記錄等功能。

## 系統架構

### 核心組件

1. **環境配置管理器** (`utils/environment_config.py`)
   - 管理多個環境配置
   - 支援環境切換
   - 提供配置驗證和備份功能

2. **快速 IP 切換工具** (`utils/ip_switcher.py`)
   - 提供 IP 模板管理
   - 支援快速 IP 替換
   - 維護操作歷史記錄

3. **動態 API URLs** (`utils/constants.py`)
   - 根據當前環境動態生成 API URLs
   - 支援熱重載

## 功能特色

### 1. 多環境管理

系統預設提供四種環境：

- **default**: 預設環境（當前使用的配置）
- **development**: 開發環境（本地開發用）
- **testing**: 測試環境（舊版配置）
- **production**: 生產環境（正式環境配置）

### 2. 快速 IP 切換

支援以下切換方式：

- **批量 IP 替換**: 將所有包含舊 IP 的配置一次性替換為新 IP
- **模板應用**: 使用預設模板快速切換整套配置
- **歷史恢復**: 從操作歷史中恢復之前的配置

### 3. 配置模板

系統提供多種預設模板：

- **常用開發環境**: 當前主要使用的配置
- **舊版測試環境**: 舊版本的測試配置
- **本地開發環境**: 本地開發用配置
- **生產環境**: 正式環境配置

## 使用方法

### 環境管理

#### 1. 查看當前環境

```python
from utils.environment_config import env_config

# 取得當前環境名稱
current_env = env_config.get_current_environment()
print(f"當前環境: {current_env}")

# 取得環境詳細資訊
env_info = env_config.get_environment_info()
print(f"環境資訊: {env_info}")
```

#### 2. 切換環境

```python
# 切換到測試環境
success = env_config.switch_environment("testing")
if success:
    print("環境切換成功")
```

#### 3. 新增環境

```python
# 新增自訂環境
env_config_data = {
    "name": "我的測試環境",
    "description": "自訂的測試環境配置",
    "api_servers": {
        "mysql_operator": "http://*************:5000",
        "gamebridge": "http://*************:8080",
        # ... 其他伺服器配置
    }
}

success = env_config.add_environment("my_test", env_config_data)
```

### 快速 IP 切換

#### 1. 批量 IP 替換

```python
from utils.ip_switcher import ip_switcher

# 將所有 ************ 替換為 ************
success = ip_switcher.quick_switch_ip("************", "************")
```

#### 2. 應用模板

```python
# 應用常用開發環境模板
success = ip_switcher.apply_template("常用開發環境")
```

#### 3. 新增自訂模板

```python
# 新增自訂模板
template_data = {
    "mysql_operator": "http://************:5000",
    "gamebridge": "http://gamebridge:8080",
    # ... 其他配置
}

success = ip_switcher.add_template(
    "我的模板", 
    template_data, 
    "自訂的伺服器配置模板"
)
```

### 配置備份與恢復

#### 1. 備份配置

```python
# 備份當前所有環境配置
backup_path = env_config.backup_environments()
print(f"配置已備份到: {backup_path}")

# 備份到指定路徑
backup_path = env_config.backup_environments("my_backup.json")
```

#### 2. 恢復配置

```python
# 從備份檔案恢復配置
success = env_config.restore_environments("my_backup.json")
```

### 配置驗證

```python
# 驗證當前環境配置
validation_result = env_config.validate_environment()

if validation_result["valid"]:
    print("配置驗證通過")
else:
    print("配置驗證失敗:")
    for error in validation_result["errors"]:
        print(f"  錯誤: {error}")
    for warning in validation_result["warnings"]:
        print(f"  警告: {warning}")
```

## 檔案結構

```
VP_Test_Tool/
├── utils/
│   ├── environment_config.py      # 環境配置管理器
│   ├── ip_switcher.py             # 快速 IP 切換工具
│   └── constants.py               # 動態 API URLs
├── views/
│   ├── environment_panel.py       # 環境管理界面
│   └── ip_switcher_panel.py       # IP 切換界面
├── controllers/
│   ├── environment_controller.py  # 環境管理控制器
│   └── ip_switcher_controller.py  # IP 切換控制器
├── environments.json              # 環境配置檔案
├── ip_templates.json              # IP 模板檔案
└── ip_history.json                # IP 切換歷史
```

## 配置檔案格式

### environments.json

```json
{
  "current_environment": "default",
  "environments": {
    "default": {
      "name": "預設環境",
      "description": "預設的開發環境配置",
      "api_servers": {
        "mysql_operator": "http://************:5000",
        "gamebridge": "http://gamebridge:8080",
        "tokenguard": "https://gp001-qa1-tokenguard.xwautc.online",
        "lottery": "http://lottery:8080",
        "simulation": "http://simulationweb-go:8080"
      },
      "created_at": "2024-01-01T00:00:00",
      "last_used": "2024-01-01T12:00:00"
    }
  },
  "last_updated": "2024-01-01T12:00:00"
}
```

### ip_templates.json

```json
{
  "常用開發環境": {
    "description": "常用的開發環境 IP 配置",
    "mysql_operator": "http://************:5000",
    "gamebridge": "http://gamebridge:8080",
    "tokenguard": "https://gp001-qa1-tokenguard.xwautc.online",
    "lottery": "http://lottery:8080",
    "simulation": "http://simulationweb-go:8080"
  }
}
```

## 最佳實踐

### 1. 環境命名規範

- 使用有意義的環境名稱，如 `dev`, `test`, `staging`, `prod`
- 避免使用特殊字元，建議使用英文字母、數字和底線

### 2. 配置管理

- 定期備份環境配置
- 在進行重大變更前先備份
- 使用描述性的模板名稱和說明

### 3. IP 切換

- 在切換前先驗證新 IP 的可用性
- 保留重要配置的模板
- 定期清理不需要的歷史記錄

### 4. 安全考量

- 不要在配置檔案中儲存敏感資訊（如密碼、金鑰）
- 定期檢查和更新伺服器配置
- 使用 HTTPS 連線確保資料傳輸安全

## 故障排除

### 常見問題

1. **環境切換後 API 請求失敗**
   - 檢查新環境的伺服器配置是否正確
   - 驗證伺服器是否可達
   - 重新啟動應用程式以確保配置生效

2. **配置檔案損壞**
   - 從備份檔案恢復配置
   - 刪除損壞的配置檔案，系統會自動重建預設配置

3. **IP 切換沒有生效**
   - 檢查是否有程式碼中的硬編碼 URL
   - 確認 API_URLS 已重新載入
   - 重新啟動應用程式

### 日誌檢查

系統會記錄所有重要操作的日誌，可以通過以下方式查看：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 更新歷史

- **v1.0.0**: 初始版本，支援基本的環境管理和 IP 切換
- **v1.1.0**: 新增模板管理和歷史記錄功能
- **v1.2.0**: 新增配置驗證和備份恢復功能

## 技術支援

如果遇到問題或需要新功能，請聯繫開發團隊或提交 Issue。
