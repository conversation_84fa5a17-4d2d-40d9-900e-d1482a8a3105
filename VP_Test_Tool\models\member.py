"""會員相關的資料模型"""
from dataclasses import dataclass
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
import logging
import random
import string
from utils.http_client import HttpClient
from utils.constants import API_URLS

logger = logging.getLogger(__name__)

@dataclass
class Member:
    """會員資料模型"""
    member_id: str
    account: str
    agent_code: str
    sub_agent_code: Optional[str] = None
    currency: str = "PHP"
    status: str = "Active"
    created_at: Optional[datetime] = None
    vip_level: int = 0
    db_source: str = "OCMS"  # 新增資料來源欄位

    @property
    def is_valid(self) -> bool:
        """檢查會員資料是否有效"""
        return bool(self.member_id and self.account and self.agent_code)

class MemberService:
    """會員服務類"""
    def __init__(self, http_client: HttpClient):
        self.http_client = http_client

    def query_member(self, account: str = "", member_id: str = "", db_source: str = "OCMS") -> <PERSON><PERSON>[Optional[Member], Dict[str, Any]]:
        """查詢會員資訊

        Args:
            account (str): 平台帳號
            member_id (str): 會員ID
            db_source (str): 資料庫來源 (OCMS 或 OCIntegrator)

        Returns:
            Tuple[Optional[Member], Dict[str, Any]]: 會員物件和詳細資訊字典
        """
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        if not account and not member_id:
            details["error"] = "未提供帳號或會員ID"
            return None, details

        # 根據資料庫來源構建查詢 SQL
        table_name = f"{db_source}.Member"

        if account:
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": f"SELECT * FROM {table_name} WHERE Account='{account}'"
            }
        else:
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": f"SELECT * FROM {table_name} WHERE MemberId='{member_id}'"
            }

        # 記錄請求資料
        details["payload"] = query_payload

        try:
            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=query_payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code
            details["success"] = response.get("success", False) if response else False

            if response and response.get("success") and response.get("data"):
                data = response["data"][0]
                try:
                    # 創建會員物件
                    member = Member(
                        member_id=str(data.get("MemberId", "")),
                        account=data.get("Account", ""),
                        agent_code=data.get("AgentCode", ""),
                        sub_agent_code=data.get("SubAgentCode"),
                        currency=data.get("Currency", "CNY"),
                        status=data.get("Status", "Active"),
                        created_at=datetime.fromisoformat(data["CreateTime"]) if "CreateTime" in data else None,
                        vip_level=data.get("VipLv", 0),  # 新增 VIP 等級屬性
                        db_source=db_source  # 設定資料庫來源
                    )
                    return member, details
                except Exception as e:
                    details["error"] = f"創建會員物件失敗: {e}"
                    logger.error(f"創建會員物件失敗: {e}, 資料: {data}")
                    return None, details
            else:
                details["error"] = "查無此會員或回應格式錯誤"
                return None, details

        except Exception as e:
            details["error"] = f"請求失敗: {str(e)}"
            logger.error(f"查詢會員失敗: {e}")
            return None, details

    def generate_random_account(self, length: int = 12) -> str:
        """生成隨機帳號"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))

    def create_member(self, account: str, agent_code: str,
                     sub_agent_code: str, currency: str) -> Optional[str]:
        """建立新會員，返回會員ID"""
        payload = {
            "accountInfo": {
                "account": account,
                "agentCode": agent_code,
                "subAgentCode": sub_agent_code,
                "currency": currency
            },
            "gameId": "230001"
        }

        response = self.http_client.post(
            API_URLS["CREATE_MEMBER"],
            json_data=payload
        )

        if response and "memberId" in response:
            return response["memberId"]
        return None

    def generate_batch_accounts(self, count: int, length: int = 12) -> List[str]:
        """批次生成隨機帳號"""
        accounts = []
        for _ in range(count):
            account = self.generate_random_account(length)
            while account in accounts:  # 確保不重複
                account = self.generate_random_account(length)
            accounts.append(account)
        return accounts

    def create_member_batch(self, accounts: List[str],
                          agent_code: str,
                          sub_agent_code: str,
                          currency: str) -> List[Tuple[str, str]]:
        """批次建立會員,返回 (account, member_id) 列表"""
        results = []
        for account in accounts:
            try:
                member_id = self.create_member(account, agent_code,
                                             sub_agent_code, currency)
                results.append((account, member_id if member_id else "建立失敗"))
            except Exception as e:
                logger.error(f"建立會員失敗: {e}")
                results.append((account, f"錯誤: {str(e)}"))
        return results

    def get_games(self) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """獲取遊戲列表

        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, Any]]: 遊戲列表和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            # 使用指定的 API 查詢遊戲列表
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": "SELECT GameId, Name FROM Platform.Games;"
            }

            # 記錄請求資料
            details["payload"] = query_payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=query_payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code
            details["success"] = response.get("success", False) if response else False

            if response and response.get("success") and response.get("data"):
                games = [
                    {
                        "id": str(game["GameId"]),
                        "name": f"{game['GameId']}-{game['Name']}"
                    } for game in response["data"]
                ]
                return games, details

            details["error"] = "無法載入遊戲列表"
            return [], details

        except Exception as e:
            details["error"] = f"請求失敗: {str(e)}"
            logger.error(f"獲取遊戲列表失敗: {e}")
            return [], details

    def get_bet_modes(self) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """獲取所有投注模式

        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, Any]]: 投注模式列表和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            # 直接使用 API 查詢所有的 BetModeId
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": "SELECT DISTINCT BetModeId FROM Platform.BetModeDetail ORDER BY BetModeId;"
            }

            # 記錄請求資料
            details["payload"] = query_payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=query_payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code
            details["success"] = response.get("success", False) if response else False

            # 如果請求成功，則從回應中提取所有的 BetModeId
            if response and response.get("success") and response.get("data"):
                bet_modes = []
                for item in response["data"]:
                    bet_mode_id = item.get("BetModeId")
                    if bet_mode_id is not None:
                        bet_modes.append({
                            "id": bet_mode_id,
                            "name": f"BetModeId: {bet_mode_id}"
                        })
                return bet_modes, details

            # 如果請求失敗，則返回空列表
            logger.warning("無法獲取投注模式")
            details["error"] = "無法獲取投注模式"
            return [], details

        except Exception as e:
            logger.error(f"獲取投注模式失敗: {e}")
            # 如果發生異常，則返回空列表
            details["error"] = f"請求失敗: {str(e)}"
            return [], details

    def get_bet_lines_with_details(self, bet_mode_id: int = None) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """獲取投注額列表和詳細資訊

        Args:
            bet_mode_id: 投注模式 ID，如果提供則只返回該模式的投注額

        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, Any]]: 投注額列表和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            # 使用指定的 API 查詢投注額列表
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": "SELECT BetModeId, TotalBet FROM Platform.BetModeDetail;"
            }

            # 記錄請求資料
            details["payload"] = query_payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=query_payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code
            details["success"] = response.get("success", False) if response else False

            if response and response.get("success") and response.get("data"):
                # 將回應資料按照 BetModeId 分組
                bet_mode_groups = {}
                for line in response["data"]:
                    current_bet_mode_id = line.get("BetModeId", 0)
                    total_bet_str = line.get("TotalBet", "0")
                    try:
                        # 嘗試轉換為浮點數
                        total_bet = float(total_bet_str)
                    except ValueError:
                        total_bet = 0

                    if current_bet_mode_id not in bet_mode_groups:
                        bet_mode_groups[current_bet_mode_id] = []

                    bet_mode_groups[current_bet_mode_id].append({
                        "id": f"line{total_bet}",
                        "name": f"{total_bet_str}",
                        "value": total_bet,
                        "bet_mode_id": current_bet_mode_id
                    })

                # 如果指定了 bet_mode_id，則只返回該模式的投注額
                if bet_mode_id is not None and bet_mode_id in bet_mode_groups:
                    bet_lines = bet_mode_groups[bet_mode_id]
                    # 按投注額大小排序
                    bet_lines.sort(key=lambda x: x["value"])
                    logger.info(f"✅ 已載入 {len(bet_lines)} 個投注額 (使用 BetModeId: {bet_mode_id})")
                    return bet_lines, details
                # 如果沒有指定 bet_mode_id 或指定的 bet_mode_id 不存在，則返回第一個模式的投注額
                elif bet_mode_groups:
                    # 取得第一個 BetModeId 的投注額列表
                    first_bet_mode_id = list(bet_mode_groups.keys())[0]
                    bet_lines = bet_mode_groups[first_bet_mode_id]
                    # 按投注額大小排序
                    bet_lines.sort(key=lambda x: x["value"])
                    logger.info(f"✅ 已載入 {len(bet_lines)} 個投注額 (使用 BetModeId: {first_bet_mode_id})")
                    return bet_lines, details

            logger.warning("⚠️ 無法載入投注額列表")
            details["error"] = "無法載入投注額列表"
            return [], details

        except Exception as e:
            logger.error(f"獲取投注額列表失敗: {e}")
            details["error"] = f"請求失敗: {str(e)}"
            # 不使用模擬資料，返回空列表
            logger.warning("⚠️ 無法獲取投注額列表")
            return [], details

    def get_bet_lines(self, bet_mode_id: int = None) -> List[Dict[str, Any]]:
        """獲取投注額列表

        Args:
            bet_mode_id: 投注模式 ID，如果提供則只返回該模式的投注額

        Returns:
            List[Dict[str, Any]]: 投注額列表
        """
        # 使用 get_bet_lines_with_details 方法來獲取投注額列表
        bet_lines, _ = self.get_bet_lines_with_details(bet_mode_id)
        return bet_lines

    def get_game_item_ids(self, game_id: str, bet_line_value: float) -> Tuple[List[str], Dict[str, Any]]:
        """獲取遊戲道具 ID

        Args:
            game_id: 遊戲ID
            bet_line_value: 投注額值

        Returns:
            Tuple[List[str], Dict[str, Any]]: 道具ID列表和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            # 將投注額格式化為浮點數字串，例如 "0.100000"
            formatted_bet_value = f"{bet_line_value:.6f}"

            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": f"SELECT Id FROM Platform.GameItem WHERE TotalBet = '{formatted_bet_value}' AND GameId = {game_id};"
            }

            # 記錄請求資料
            details["payload"] = query_payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=query_payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code
            details["success"] = response.get("success", False) if response else False

            if response and response.get("success") and response.get("data"):
                item_ids = [item["Id"] for item in response["data"]]
                return item_ids, details

            details["error"] = "查無道具或回應格式錯誤"
            return [], details

        except Exception as e:
            details["error"] = f"請求失敗: {str(e)}"
            logger.error(f"獲取遊戲道具 ID 失敗: {e}")
            return [], details

    def add_cards(self, member: Member, game_item_id: int, card_count: int) -> Tuple[bool, Dict[str, Any]]:
        """新增卡片

        Args:
            member: 會員物件
            game_item_id: 遊戲道具ID
            card_count: 卡片數量

        Returns:
            Tuple[bool, Dict[str, Any]]: 是否成功和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["ADD_CARDS"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            payload = {
                "MemberId": member.member_id,
                "AccountInfo": {
                    "account": member.account,
                    "agentCode": member.agent_code,
                    "subAgentCode": member.sub_agent_code,
                    "currency": member.currency
                },
                "source": "GBAO",
                "description": "addItem",
                "rewardNodeList": [
                    {
                        "id": game_item_id,
                        "num": card_count,
                        "type": "Item"
                    }
                ]
            }

            # 記錄請求資料
            details["payload"] = payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["ADD_CARDS"],
                json_data=payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code

            # 檢查回應是否成功
            if response and response.get("retStatus", {}).get("StatusCode") == 10000:
                details["success"] = True
                return True, details
            else:
                details["success"] = False
                details["error"] = "新增卡片失敗"
                return False, details

        except Exception as e:
            details["error"] = f"請求失敗: {str(e)}"
            logger.error(f"新增卡片失敗: {e}")
            return False, details

    def delete_all_cards(self, member_id: str) -> Tuple[bool, Dict[str, Any]]:
        """刪除所有卡片

        Args:
            member_id: 會員ID

        Returns:
            Tuple[bool, Dict[str, Any]]: 是否成功和詳細資訊字典
        """
        # 初始化詳細資訊字典
        details = {
            "api_url": API_URLS["MYSQL_OPERATOR"],
            "payload": None,
            "response": None,
            "status_code": None,
            "success": False,
            "error": None
        }

        try:
            payload = {
                "action": "delete",
                "serverTag": "igaming-mysql-main",
                "sql": f"DELETE FROM OCMS.MemberOwnItems WHERE MemberId = {member_id}"
            }

            # 記錄請求資料
            details["payload"] = payload

            # 發送請求
            response, status_code = self.http_client.post_with_details(
                API_URLS["MYSQL_OPERATOR"],
                json_data=payload
            )

            # 記錄回應資料
            details["response"] = response
            details["status_code"] = status_code

            # 檢查回應是否成功
            if response and response.get("success", False):
                details["success"] = True
                return True, details
            else:
                details["success"] = False
                details["error"] = "刪除卡片失敗"
                return False, details

        except Exception as e:
            details["error"] = f"請求失敗: {str(e)}"
            logger.error(f"刪除所有卡片失敗: {e}")
            return False, details

    # 移除 get_game_version_list 方法

    # 移除 _get_mock_card_info 和 get_card_info 方法


