"""NotificationManager 單元測試"""
import unittest
import os
import sys
import tkinter as tk
from unittest.mock import patch, MagicMock

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from widgets.notification import Notification, NotificationManager

class TestNotification(unittest.TestCase):
    """Notification 單元測試類"""

    def setUp(self):
        """測試前準備"""
        self.root = tk.Tk()
        self.root.withdraw()  # 隱藏主窗口

    def tearDown(self):
        """測試後清理"""
        self.root.destroy()

    def test_notification_creation(self):
        """測試創建通知"""
        # 創建通知
        notification = Notification(
            self.root,
            message="Test notification",
            notification_type="info",
            duration=0
        )
        
        # 驗證通知已創建
        self.assertIsInstance(notification, Notification)
        
        # 清理
        notification.destroy()

    def test_notification_close(self):
        """測試關閉通知"""
        # 創建通知
        notification = Notification(
            self.root,
            message="Test notification",
            notification_type="info",
            duration=0
        )
        
        # 關閉通知
        notification.close()
        
        # 驗證通知已關閉
        self.assertFalse(notification.winfo_exists())

    def test_notification_auto_close(self):
        """測試通知自動關閉"""
        # 創建通知，設置 duration 為 100 毫秒
        notification = Notification(
            self.root,
            message="Test notification",
            notification_type="info",
            duration=100
        )
        
        # 等待通知自動關閉
        self.root.after(200, self.root.quit)
        self.root.mainloop()
        
        # 驗證通知已關閉
        self.assertFalse(notification.winfo_exists())

    def test_notification_on_close_callback(self):
        """測試通知關閉回調函數"""
        # 創建回調函數
        on_close_called = [False]
        def on_close():
            on_close_called[0] = True
        
        # 創建通知
        notification = Notification(
            self.root,
            message="Test notification",
            notification_type="info",
            duration=0,
            on_close=on_close
        )
        
        # 關閉通知
        notification.close()
        
        # 驗證回調函數已被調用
        self.assertTrue(on_close_called[0])

class TestNotificationManager(unittest.TestCase):
    """NotificationManager 單元測試類"""

    def setUp(self):
        """測試前準備"""
        self.root = tk.Tk()
        self.root.withdraw()  # 隱藏主窗口
        self.notification_manager = NotificationManager(self.root)

    def tearDown(self):
        """測試後清理"""
        self.notification_manager.clear()
        self.root.destroy()

    def test_show_notification(self):
        """測試顯示通知"""
        # 顯示通知
        notification = self.notification_manager.show(
            message="Test notification",
            notification_type="info",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertIsInstance(notification, Notification)
        self.assertIn(notification, self.notification_manager.notifications)
        
        # 清理
        notification.close()

    def test_show_multiple_notifications(self):
        """測試顯示多個通知"""
        # 顯示多個通知
        notification1 = self.notification_manager.show(
            message="Test notification 1",
            notification_type="info",
            duration=0
        )
        
        notification2 = self.notification_manager.show(
            message="Test notification 2",
            notification_type="warning",
            duration=0
        )
        
        notification3 = self.notification_manager.show(
            message="Test notification 3",
            notification_type="error",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertEqual(len(self.notification_manager.notifications), 3)
        self.assertIn(notification1, self.notification_manager.notifications)
        self.assertIn(notification2, self.notification_manager.notifications)
        self.assertIn(notification3, self.notification_manager.notifications)
        
        # 清理
        self.notification_manager.clear()

    def test_max_notifications(self):
        """測試最大通知數量"""
        # 設置最大通知數量為 2
        self.notification_manager.max_notifications = 2
        
        # 顯示 3 個通知
        notification1 = self.notification_manager.show(
            message="Test notification 1",
            notification_type="info",
            duration=0
        )
        
        notification2 = self.notification_manager.show(
            message="Test notification 2",
            notification_type="warning",
            duration=0
        )
        
        notification3 = self.notification_manager.show(
            message="Test notification 3",
            notification_type="error",
            duration=0
        )
        
        # 驗證只有 2 個通知
        self.assertEqual(len(self.notification_manager.notifications), 2)
        self.assertNotIn(notification1, self.notification_manager.notifications)
        self.assertIn(notification2, self.notification_manager.notifications)
        self.assertIn(notification3, self.notification_manager.notifications)
        
        # 清理
        self.notification_manager.clear()

    def test_clear_notifications(self):
        """測試清除所有通知"""
        # 顯示多個通知
        self.notification_manager.show(
            message="Test notification 1",
            notification_type="info",
            duration=0
        )
        
        self.notification_manager.show(
            message="Test notification 2",
            notification_type="warning",
            duration=0
        )
        
        # 清除所有通知
        self.notification_manager.clear()
        
        # 驗證所有通知已清除
        self.assertEqual(len(self.notification_manager.notifications), 0)

    def test_show_info(self):
        """測試顯示資訊通知"""
        # 顯示資訊通知
        notification = self.notification_manager.show_info(
            message="Test info notification",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertIsInstance(notification, Notification)
        self.assertEqual(notification.notification_type, "info")
        
        # 清理
        notification.close()

    def test_show_success(self):
        """測試顯示成功通知"""
        # 顯示成功通知
        notification = self.notification_manager.show_success(
            message="Test success notification",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertIsInstance(notification, Notification)
        self.assertEqual(notification.notification_type, "success")
        
        # 清理
        notification.close()

    def test_show_warning(self):
        """測試顯示警告通知"""
        # 顯示警告通知
        notification = self.notification_manager.show_warning(
            message="Test warning notification",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertIsInstance(notification, Notification)
        self.assertEqual(notification.notification_type, "warning")
        
        # 清理
        notification.close()

    def test_show_error(self):
        """測試顯示錯誤通知"""
        # 顯示錯誤通知
        notification = self.notification_manager.show_error(
            message="Test error notification",
            duration=0
        )
        
        # 驗證通知已顯示
        self.assertIsInstance(notification, Notification)
        self.assertEqual(notification.notification_type, "error")
        
        # 清理
        notification.close()

if __name__ == '__main__':
    unittest.main()
