"""動畫進度條元件

此模組提供動畫進度條元件，用於顯示進度和狀態。
"""
import tkinter as tk
from tkinter import ttk
import time
import threading
from typing import Optional, Callable, Dict, Any, List, Tuple
from utils.theme import ThemeManager

class AnimatedProgressBar(ttk.Frame):
    """動畫進度條元件
    
    提供動畫進度條元件，用於顯示進度和狀態。
    
    Args:
        parent: 父元件
        width: 寬度
        height: 高度
        mode: 模式，可以是 'determinate' 或 'indeterminate'
        animation_speed: 動畫速度
        show_percentage: 是否顯示百分比
        show_text: 是否顯示文字
        text: 文字
        **kwargs: 其他參數
    """
    
    def __init__(
        self,
        parent,
        width: int = 300,
        height: int = 20,
        mode: str = "determinate",
        animation_speed: int = 10,
        show_percentage: bool = True,
        show_text: bool = True,
        text: str = "",
        **kwargs
    ):
        super().__init__(parent, **kwargs)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.width = width
        self.height = height
        self.mode = mode
        self.animation_speed = animation_speed
        self.show_percentage = show_percentage
        self.show_text = show_text
        self.text = text
        
        self.value = 0
        self.max_value = 100
        self.is_animating = False
        self.animation_thread = None
        self.animation_lock = threading.RLock()
        
        # 初始化 UI
        self._init_ui()
        
    def _init_ui(self):
        """初始化 UI"""
        # 設定進度條樣式
        style = ttk.Style()
        style.configure(
            "Animated.Horizontal.TProgressbar",
            background=self.theme_manager.get_color("accent"),
            troughcolor=self.theme_manager.get_color("surface"),
            thickness=self.height
        )
        
        # 建立進度條容器
        self.progress_container = ttk.Frame(self)
        self.progress_container.pack(fill=tk.X, expand=True)
        
        # 建立進度條
        self.progress_bar = ttk.Progressbar(
            self.progress_container,
            orient="horizontal",
            length=self.width,
            mode=self.mode,
            style="Animated.Horizontal.TProgressbar"
        )
        self.progress_bar.pack(fill=tk.X, expand=True, side=tk.TOP)
        
        # 建立標籤容器
        self.label_container = ttk.Frame(self)
        self.label_container.pack(fill=tk.X, expand=True)
        
        # 建立百分比標籤
        if self.show_percentage:
            self.percentage_label = ttk.Label(
                self.label_container,
                text="0%",
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.percentage_label.pack(side=tk.RIGHT, padx=5)
            
        # 建立文字標籤
        if self.show_text:
            self.text_label = ttk.Label(
                self.label_container,
                text=self.text,
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.text_label.pack(side=tk.LEFT, padx=5)
            
    def set_value(self, value: float):
        """設定進度值
        
        Args:
            value: 進度值 (0-100)
        """
        self.value = max(0, min(value, self.max_value))
        self.progress_bar["value"] = self.value
        
        if self.show_percentage:
            self.percentage_label.config(text=f"{int(self.value)}%")
            
    def set_text(self, text: str):
        """設定文字
        
        Args:
            text: 文字
        """
        self.text = text
        if self.show_text:
            self.text_label.config(text=text)
            
    def start_animation(self):
        """開始動畫"""
        with self.animation_lock:
            if self.is_animating:
                return
                
            self.is_animating = True
            
            if self.mode == "indeterminate":
                self.progress_bar.start(self.animation_speed)
            else:
                self.animation_thread = threading.Thread(
                    target=self._animation_thread,
                    daemon=True
                )
                self.animation_thread.start()
                
    def stop_animation(self):
        """停止動畫"""
        with self.animation_lock:
            if not self.is_animating:
                return
                
            self.is_animating = False
            
            if self.mode == "indeterminate":
                self.progress_bar.stop()
                
    def _animation_thread(self):
        """動畫線程"""
        try:
            while self.is_animating:
                # 計算動畫值
                current_value = self.progress_bar["value"]
                
                if current_value >= 100:
                    # 重置動畫
                    self.progress_bar["value"] = 0
                else:
                    # 增加動畫值
                    self.progress_bar["value"] = current_value + 1
                    
                # 更新百分比標籤
                if self.show_percentage:
                    self.percentage_label.config(text=f"{int(self.progress_bar['value'])}%")
                    
                # 等待下一幀
                time.sleep(0.05)
                
        except Exception as e:
            print(f"動畫線程異常: {e}")
            
    def set_mode(self, mode: str):
        """設定模式
        
        Args:
            mode: 模式，可以是 'determinate' 或 'indeterminate'
        """
        if mode not in ["determinate", "indeterminate"]:
            return
            
        # 停止動畫
        self.stop_animation()
        
        # 設定模式
        self.mode = mode
        self.progress_bar.config(mode=mode)
        
    def set_color(self, color: str):
        """設定顏色
        
        Args:
            color: 顏色
        """
        style = ttk.Style()
        style.configure(
            "Animated.Horizontal.TProgressbar",
            background=color
        )
        
    def pulse(self):
        """脈衝效果"""
        if self.mode == "indeterminate":
            self.progress_bar.step()
            
    def reset(self):
        """重置進度條"""
        self.stop_animation()
        self.set_value(0)
        self.set_text("")
        
    def set_busy(self, text: str = "處理中..."):
        """設定為忙碌狀態
        
        Args:
            text: 文字
        """
        self.set_mode("indeterminate")
        self.set_text(text)
        self.start_animation()
        
    def set_progress(self, value: float, text: str = ""):
        """設定為進度狀態
        
        Args:
            value: 進度值 (0-100)
            text: 文字
        """
        self.set_mode("determinate")
        self.set_value(value)
        if text:
            self.set_text(text)
            
    def set_complete(self, text: str = "完成"):
        """設定為完成狀態
        
        Args:
            text: 文字
        """
        self.stop_animation()
        self.set_mode("determinate")
        self.set_value(100)
        self.set_text(text)
        self.set_color(self.theme_manager.get_color("success"))
        
    def set_error(self, text: str = "錯誤"):
        """設定為錯誤狀態
        
        Args:
            text: 文字
        """
        self.stop_animation()
        self.set_mode("determinate")
        self.set_value(100)
        self.set_text(text)
        self.set_color(self.theme_manager.get_color("error"))
        
    def set_warning(self, text: str = "警告"):
        """設定為警告狀態
        
        Args:
            text: 文字
        """
        self.stop_animation()
        self.set_mode("determinate")
        self.set_value(100)
        self.set_text(text)
        self.set_color(self.theme_manager.get_color("warning"))
        
class StatusIndicator(ttk.Frame):
    """狀態指示器元件
    
    提供狀態指示器元件，用於顯示狀態。
    
    Args:
        parent: 父元件
        size: 大小
        status: 狀態，可以是 'normal', 'success', 'warning', 'error', 'busy'
        text: 文字
        **kwargs: 其他參數
    """
    
    def __init__(
        self,
        parent,
        size: int = 10,
        status: str = "normal",
        text: str = "",
        **kwargs
    ):
        super().__init__(parent, **kwargs)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.size = size
        self.status = status
        self.text = text
        
        self.is_animating = False
        self.animation_thread = None
        self.animation_lock = threading.RLock()
        
        # 狀態顏色對應
        self.status_colors = {
            "normal": self.theme_manager.get_color("text_secondary"),
            "success": self.theme_manager.get_color("success"),
            "warning": self.theme_manager.get_color("warning"),
            "error": self.theme_manager.get_color("error"),
            "busy": self.theme_manager.get_color("primary")
        }
        
        # 初始化 UI
        self._init_ui()
        
    def _init_ui(self):
        """初始化 UI"""
        # 建立容器
        self.container = ttk.Frame(self)
        self.container.pack(fill=tk.BOTH, expand=True)
        
        # 建立狀態指示器
        self.indicator = tk.Canvas(
            self.container,
            width=self.size,
            height=self.size,
            bg=self["background"] if self["background"] else self.theme_manager.get_color("surface"),
            highlightthickness=0
        )
        self.indicator.pack(side=tk.LEFT, padx=5)
        
        # 繪製狀態圓點
        self.indicator_id = self.indicator.create_oval(
            0, 0, self.size, self.size,
            fill=self.status_colors.get(self.status, self.status_colors["normal"]),
            outline=""
        )
        
        # 建立文字標籤
        self.text_label = ttk.Label(
            self.container,
            text=self.text,
            font=self.theme_manager.get_font("small"),
            foreground=self.theme_manager.get_color("text_primary")
        )
        self.text_label.pack(side=tk.LEFT, padx=5)
        
    def set_status(self, status: str):
        """設定狀態
        
        Args:
            status: 狀態，可以是 'normal', 'success', 'warning', 'error', 'busy'
        """
        if status not in self.status_colors:
            return
            
        self.status = status
        
        # 停止動畫
        self.stop_animation()
        
        # 更新狀態圓點顏色
        self.indicator.itemconfig(
            self.indicator_id,
            fill=self.status_colors.get(self.status, self.status_colors["normal"])
        )
        
        # 如果是忙碌狀態，啟動動畫
        if self.status == "busy":
            self.start_animation()
            
    def set_text(self, text: str):
        """設定文字
        
        Args:
            text: 文字
        """
        self.text = text
        self.text_label.config(text=text)
        
    def start_animation(self):
        """開始動畫"""
        with self.animation_lock:
            if self.is_animating:
                return
                
            self.is_animating = True
            self.animation_thread = threading.Thread(
                target=self._animation_thread,
                daemon=True
            )
            self.animation_thread.start()
            
    def stop_animation(self):
        """停止動畫"""
        with self.animation_lock:
            if not self.is_animating:
                return
                
            self.is_animating = False
            
    def _animation_thread(self):
        """動畫線程"""
        try:
            alpha = 0
            direction = 1
            
            while self.is_animating:
                # 計算透明度
                alpha += direction * 0.05
                
                if alpha >= 1:
                    alpha = 1
                    direction = -1
                elif alpha <= 0.3:
                    alpha = 0.3
                    direction = 1
                    
                # 計算顏色
                color = self.status_colors.get(self.status, self.status_colors["normal"])
                
                # 更新狀態圓點顏色
                self.indicator.itemconfig(
                    self.indicator_id,
                    fill=color
                )
                
                # 等待下一幀
                time.sleep(0.05)
                
        except Exception as e:
            print(f"動畫線程異常: {e}")
            
    def set_normal(self, text: str = "正常"):
        """設定為正常狀態
        
        Args:
            text: 文字
        """
        self.set_status("normal")
        self.set_text(text)
        
    def set_success(self, text: str = "成功"):
        """設定為成功狀態
        
        Args:
            text: 文字
        """
        self.set_status("success")
        self.set_text(text)
        
    def set_warning(self, text: str = "警告"):
        """設定為警告狀態
        
        Args:
            text: 文字
        """
        self.set_status("warning")
        self.set_text(text)
        
    def set_error(self, text: str = "錯誤"):
        """設定為錯誤狀態
        
        Args:
            text: 文字
        """
        self.set_status("error")
        self.set_text(text)
        
    def set_busy(self, text: str = "處理中..."):
        """設定為忙碌狀態
        
        Args:
            text: 文字
        """
        self.set_status("busy")
        self.set_text(text)
