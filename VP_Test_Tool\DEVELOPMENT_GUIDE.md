# VP Test Tool 開發指南

## 目錄

1. [簡介](#簡介)
2. [系統架構](#系統架構)
3. [主要功能模組](#主要功能模組)

   - [資源調整工具](#資源調整工具)
   - [遊戲卡片工具](#遊戲卡片工具)
   - [帳號產生器](#帳號產生器)
   - [Slot Set RNG](#slot-set-rng)

4. [啟動模式](#啟動模式)
5. [核心元件](#核心元件)

   - [HTTP 客戶端](#http-客戶端)
   - [設定管理](#設定管理)
   - [日誌系統](#日誌系統)
   - [主題管理](#主題管理)

6. [API 整合](#api-整合)
7. [打包與部署](#打包與部署)
8. [錯誤處理機制](#錯誤處理機制)
9. [開發規範](#開發規範)

## 簡介

VP Test Tool 是一個用於測試和管理遊戲平台的工具，提供會員資源調整、遊戲卡片管理、帳號產生和 RNG 設置等功能。本工具使用 Python 和 Tkinter 開發，採用 MVC 架構設計，具有良好的可擴展性和可維護性。

## 系統架構

VP Test Tool 採用 MVC (Model-View-Controller) 架構設計：

- **Model**: 位於 `models/` 目錄，包含數據模型和業務邏輯
- **View**: 位於 `views/` 目錄，包含用戶界面元素
- **Controller**: 位於 `controllers/` 目錄，負責連接模型和視圖

此外，還有以下重要組件：

- **Utils**: 位於 `utils/` 目錄，包含各種工具類和輔助函數
- **Widgets**: 位於 `widgets/` 目錄，包含自定義的 UI 元件

## 主要功能模組

### 資源調整工具

資源調整工具用於調整會員的各種資源，包括金幣、VIP 等級、寶石和抽獎券。

**主要功能**:

- 查詢會員資訊
- 更新會員金幣
- 更新會員 VIP 等級
- 更新會員寶石
- 更新會員抽獎券
- 批量導入和更新會員資源

**相關檔案**:

- `controllers/resource_controller.py`: 資源調整控制器
- `views/resource_panel.py`: 資源調整界面

**API 整合**:

- 金幣更新: `http://gamebridge:8080/private/modifyBalance`
- VIP 更新: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/ToolService/DebugUpdateMemberVip`
- 寶石更新: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/RewardService/RewardOperateRequest/RedeemRewardNode`
- 抽獎券更新: `http://lottery:8080/private/redeem`

**使用流程**:

**單一會員資源調整**:

1. 在會員資訊區域輸入會員帳號或會員 ID
2. 選擇資料庫來源 (OCMS 或 OCIntegrator)
3. 點擊「查詢會員」按鈕獲取會員資訊
4. 在資源設定區域輸入要更新的資源值:
   - 金幣: 支援小數點輸入，例如 1000.50
   - VIP 等級: 輸入整數值，範圍 0-6
   - 寶石: 輸入整數值
   - 抽獎券: 輸入整數值
5. 點擊「更新資源」按鈕執行資源更新
6. 查看右側日誌區域的操作結果

**批量更新會員資源**:

1. 點擊「匯入帳號」按鈕，選擇包含會員帳號的 CSV 文件
   - CSV 格式: 每行一個帳號，可選包含會員 ID
2. 在批次處理區域設定要更新的資源值
3. 選擇資料庫來源 (OCMS 或 OCIntegrator)
4. 點擊「批次更新」按鈕執行批量更新
5. 查看右側日誌區域的批次處理結果
6. 操作完成後可點擊「清除參數」重置所有輸入欄位

### 遊戲卡片工具

遊戲卡片工具用於管理會員的遊戲卡片，包括新增、刪除和修改。

**主要功能**:

- 查詢會員資訊
- 查詢遊戲列表
- 查詢投注額列表
- 新增遊戲卡片
- 刪除遊戲卡片
- 批量處理遊戲卡片

**相關檔案**:

- `controllers/member_controller.py`: 遊戲卡片控制器
- `views/member_panel.py`: 遊戲卡片界面

**使用流程**:

**查詢會員遊戲卡片**:

1. 在會員資訊區域輸入會員帳號或會員 ID
2. 選擇資料庫來源 (OCMS 或 OCIntegrator)
3. 點擊「查詢會員」按鈕獲取會員資訊
4. 會員的遊戲卡片列表將顯示在右側的卡片列表區域

**新增遊戲卡片**:

1. 完成會員查詢後，在遊戲設定區域選擇要新增的遊戲
2. 選擇投注額
3. 點擊「新增卡片」按鈕執行新增操作
4. 查看右側日誌區域的操作結果和卡片列表的更新

**刪除遊戲卡片**:

1. 完成會員查詢後，在右側卡片列表中選擇要刪除的卡片
2. 點擊「刪除卡片」按鈕執行刪除操作
3. 查看右側日誌區域的操作結果和卡片列表的更新

**批量處理**:

1. 點擊「匯入帳號」按鈕，選擇包含會員帳號的 CSV 文件
2. 選擇資料庫來源 (OCMS 或 OCIntegrator)
3. 選擇要執行的批量操作類型 (新增或刪除)
4. 選擇遊戲和投注額
5. 點擊「批量處理」按鈕執行批量操作
6. 查看右側日誌區域的批次處理結果

### 帳號產生器

帳號產生器用於批量產生會員帳號，支援不同代理商和幣別。

**主要功能**:

- 選擇代理商和子代理商
- 設定帳號數量和前綴
- 批量產生帳號
- 導出帳號列表到 CSV 文件

**相關檔案**:

- `controllers/account_controller.py`: 帳號產生器控制器
- `views/account_panel.py`: 帳號產生器界面

**API 整合**:

- 建立會員: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/LoginService/OperateRequest/Login`

**使用流程**:

**設定產生參數**:

1. 從下拉選單中選擇代理商 (Agent Code)
2. 選擇對應的子代理商 (Sub Agent Code)，會自動顯示對應的幣別
3. 設定要產生的帳號數量 (1-100)
4. 可選設定帳號前綴，例如 "test_"
5. 可選設定密碼，若不設定則使用預設密碼

**產生帳號**:

1. 點擊「產生帳號」按鈕執行帳號產生
2. 產生過程中可查看進度條顯示的完成百分比
3. 產生完成後，帳號列表會顯示在右側的結果區域
4. 點擊「導出 CSV」按鈕可將產生的帳號列表保存為 CSV 文件

**查看結果**:

1. 右側日誌區域會顯示每個帳號的產生結果和會員 ID
2. 若有帳號產生失敗，會顯示錯誤原因
3. 產生完成後會顯示成功/失敗的統計資訊

### Slot Set RNG

Slot Set RNG 用於設定老虎機的隨機數生成器參數。

**主要功能**:

- 導入 RNG 設定文件
- 從 Git 下載 RNG 設定
- 選擇遊戲和 RNG 設定
- 設定會員的 RNG 參數

**相關檔案**:

- `controllers/rng_controller.py`: RNG 控制器
- `views/rng_panel.py`: RNG 界面

**API 整合**:

- 設定 RNG: `https://gp001-qa1-tokenguard.xwautc.online/debug/slotgame/DeveloperService/setOnceRng`
- Git API: `https://git-qa.yile808.com/api/v4`

**使用流程**:

**導入 RNG 設定**:

1. 點擊「Git 下載」按鈕從 Git 倉庫下載最新的 RNG 設定文件
   - 或點擊「匯入 RNG 檔案」按鈕從本地選擇 Excel 格式的 RNG 設定文件
2. 導入成功後，遊戲下拉選單會自動填充可用的遊戲列表
3. 選擇遊戲後，RNG 下拉選單會自動填充該遊戲可用的 RNG 設定

**設定會員 RNG**:

1. 在會員資訊區域輸入會員帳號或會員 ID
2. 選擇資料庫來源 (OCMS 或 OCIntegrator)
3. 點擊「查詢會員」按鈕獲取會員資訊
4. 在遊戲設定區域選擇遊戲和 RNG 設定
5. 確認遊戲 ID 欄位已正確填寫
6. 點擊「執行 RNG」按鈕應用設定
7. 查看右側日誌區域的操作結果

**查看 RNG 設定詳情**:

1. 選擇遊戲和 RNG 設定後，右側描述區域會顯示詳細的 RNG 設定資訊
2. 描述區域包含兩部分:
   - Description: RNG 設定的描述文字
   - RNG: 實際的 RNG 數值，通常是以逗號分隔的數字序列

## 啟動模式

VP Test Tool 提供多種啟動模式，以適應不同的使用場景：

1. **正常模式** (`main.py`): 完整功能模式，包含所有功能模組
2. **診斷模式** (`debug_startup.py`): 用於診斷程式問題，逐步載入各個模組並檢查
3. **簡單模式** (`simple_startup.py`): 簡化版本，不進行任何 API 調用，只顯示基本界面
4. **網絡測試模式** (`network_test.py`): 用於測試網絡連接是否正常

可以通過 `run_vp_test_tool.bat` 批處理文件選擇啟動模式。

## 核心元件

### HTTP 客戶端

HTTP 客戶端 (`utils/http_client.py`) 負責處理所有的 API 請求，提供以下功能：

- GET/POST 請求支援
- 錯誤處理和重試機制
- 請求快取
- 詳細的日誌記錄
- 可配置的超時設定

**主要方法**:

- `get(url, params, headers, use_cache)`: 發送 GET 請求
- `post(url, data, json_data, headers, use_cache)`: 發送 POST 請求
- `get_with_details(url, params, headers)`: 發送 GET 請求並返回詳細資訊
- `post_with_details(url, data, json_data, headers)`: 發送 POST 請求並返回詳細資訊

**錯誤處理**:

- 網絡連接錯誤時，可選擇顯示錯誤對話框或僅記錄日誌
- 支援重試機制，可配置重試次數和間隔

### 設定管理

設定管理 (`utils/config.py`) 負責管理程式的設定，包括 API 設定、資料庫設定、日誌設定和 UI 設定等。

**主要功能**:

- 讀取和保存設定文件 (`config.json`)
- 提供預設設定
- 支援動態更新設定

**主要方法**:

- `get(section, key, default)`: 獲取設定值
- `set(section, value)`: 設定值
- `save()`: 保存設定到文件

### 日誌系統

日誌系統 (`utils/logger.py`) 負責記錄程式的運行日誌，支援多種日誌級別和輸出方式。

**主要功能**:

- 設定日誌格式和級別
- 支援輸出到文件和控制台
- 提供便捷的日誌記錄方法

**主要方法**:

- `setup_logger(name, level)`: 設定並返回 Logger 實例
- `get_logger(name)`: 獲取 Logger 實例

### 主題管理

主題管理 (`utils/theme.py`) 負責管理程式的視覺主題，包括顏色、字體和樣式等。

**主要功能**:

- 提供預設主題
- 支援自定義主題
- 應用主題到 UI 元素

**主要方法**:

- `get_color(name)`: 獲取顏色
- `get_font(name)`: 獲取字體
- `get_button_style(name)`: 獲取按鈕樣式
- `apply_theme_to_widgets(root)`: 應用主題到所有 UI 元素

## API 整合

VP Test Tool 整合了多個 API，用於實現各種功能：

1. **會員查詢**: `http://************:5000/tools/db/mysql-operator`
2. **金幣更新**: `http://gamebridge:8080/private/modifyBalance`
3. **VIP 更新**: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/ToolService/DebugUpdateMemberVip`
4. **寶石更新**: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/RewardService/RewardOperateRequest/RedeemRewardNode`
5. **抽獎券更新**: `http://lottery:8080/private/redeem`
6. **RNG 設定**: `https://gp001-qa1-tokenguard.xwautc.online/debug/slotgame/DeveloperService/setOnceRng`
7. **會員建立**: `https://gp001-qa1-tokenguard.xwautc.online/debug/common/LoginService/OperateRequest/Login`
8. **Git API**: `https://git-qa.yile808.com/api/v4`

所有 API URL 都定義在 `utils/constants.py` 中的 `API_URLS` 字典中，方便集中管理和更新。

## 打包與部署

VP Test Tool 提供兩種打包方式，用於生成可執行文件：

1. **優化版** (`setup_cx_freeze_optimized.py`): 使用 cx_Freeze 打包，進行優化以減小文件大小
2. **完整版** (`setup_cx_freeze_full.py`): 使用 cx_Freeze 打包，包含所有依賴，確保最大兼容性

**打包命令**:

```bash
python setup_cx_freeze_optimized.py build
```

**打包依賴**:

- cx_Freeze
- pandas
- numpy
- openpyxl
- xlrd
- requests
- PIL

**輸出文件**:

- `dist/cx_freeze_optimized/VP_Test_Tool.exe`: 主程式可執行文件
- `dist/cx_freeze_optimized/VP_Test_Tool_Diagnostic.exe`: 診斷模式可執行文件
- `dist/cx_freeze_optimized/VP_Test_Tool_NetworkTest.exe`: 網絡測試模式可執行文件
- `dist/cx_freeze_optimized/VP_Test_Tool_Simple.exe`: 簡單模式可執行文件

## 錯誤處理機制

VP Test Tool 實現了完善的錯誤處理機制，確保程式在各種情況下都能正常運行：

1. **網絡錯誤處理**:
   - 批次處理時不再顯示彈出錯誤對話框，而是將錯誤訊息記錄到日誌中，並在批次處理結果中顯示
   - HTTP 客戶端支援重試機制，可配置重試次數和間隔

2. **輸入驗證**:
   - 所有用戶輸入都進行驗證，確保數據格式正確
   - 提供清晰的錯誤訊息，幫助用戶理解和修正錯誤

3. **異常捕獲**:
   - 使用 try-except 捕獲可能的異常
   - 記錄詳細的錯誤信息到日誌
   - 在適當的情況下向用戶顯示錯誤訊息

4. **診斷模式**:
   - 提供診斷模式，逐步載入各個模組並檢查
   - 顯示詳細的診斷信息，幫助開發者定位問題

## 開發規範

### 代碼風格

- 使用 PEP 8 代碼風格
- 使用 4 空格縮進
- 使用 snake_case 命名變數和函數
- 使用 CamelCase 命名類
- 使用 UPPER_CASE 命名常數

### 文檔規範

- 所有模組、類和函數都應有文檔字符串
- 使用 Google 風格的文檔字符串
- 包含參數、返回值和異常說明

### 日誌規範

- 使用適當的日誌級別 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- 記錄關鍵操作和錯誤信息
- 不要記錄敏感信息 (如密碼、令牌等)

### 錯誤處理規範

- 使用具體的異常類型，而不是通用的 Exception
- 提供清晰的錯誤訊息
- 在適當的層級處理異常，不要讓異常傳播到用戶界面

### UI 設計規範

- 使用一致的間距和對齊
- 提供清晰的標籤和提示
- 使用適當的顏色和字體
- 確保 UI 元素的可訪問性
