"""自動更新模塊

此模塊提供自動檢查和下載程序更新的功能。
"""
import os
import sys
import json
import time
import logging
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tempfile
import shutil
import subprocess
from typing import Dict, Any, Optional, List, Tuple, Callable
import requests
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

# 導入版本信息
from .version import VERSION as CURRENT_VERSION

# 更新服務器 URL
UPDATE_SERVER_URL = "https://api.example.com/vp-test-tool/updates"

# 更新檢查間隔（秒）
UPDATE_CHECK_INTERVAL = 86400  # 24 小時

# 最後檢查時間文件
LAST_CHECK_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "last_update_check.json")

class AutoUpdater:
    """自動更新器

    提供自動檢查和下載程序更新的功能。

    Args:
        parent: 父窗口
        current_version: 當前版本
        server_url: 更新服務器 URL
        check_interval: 更新檢查間隔（秒）
        on_update_available: 更新可用回調函數
        on_update_not_available: 更新不可用回調函數
        on_update_error: 更新錯誤回調函數
        on_download_progress: 下載進度回調函數
        on_download_complete: 下載完成回調函數
        on_download_error: 下載錯誤回調函數
    """

    def __init__(
        self,
        parent: Optional[tk.Tk] = None,
        current_version: str = CURRENT_VERSION,
        server_url: str = UPDATE_SERVER_URL,
        check_interval: int = UPDATE_CHECK_INTERVAL,
        on_update_available: Optional[Callable[[Dict[str, Any]], None]] = None,
        on_update_not_available: Optional[Callable[[], None]] = None,
        on_update_error: Optional[Callable[[Exception], None]] = None,
        on_download_progress: Optional[Callable[[float], None]] = None,
        on_download_complete: Optional[Callable[[str], None]] = None,
        on_download_error: Optional[Callable[[Exception], None]] = None
    ):
        self.parent = parent
        self.current_version = current_version
        self.server_url = server_url
        self.check_interval = check_interval
        self.on_update_available = on_update_available
        self.on_update_not_available = on_update_not_available
        self.on_update_error = on_update_error
        self.on_download_progress = on_download_progress
        self.on_download_complete = on_download_complete
        self.on_download_error = on_download_error

        # 創建數據目錄
        self._create_data_dir()

    def _create_data_dir(self):
        """創建數據目錄"""
        data_dir = os.path.dirname(LAST_CHECK_FILE)
        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir)
            except Exception as e:
                logger.warning(f"創建數據目錄失敗: {e}")

    def check_for_updates(self, force: bool = False) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """檢查更新

        Args:
            force: 是否強制檢查更新，忽略檢查間隔

        Returns:
            Tuple[bool, Optional[Dict[str, Any]], Optional[str]]: 是否有更新、更新信息、錯誤信息
        """
        try:
            # 檢查是否需要檢查更新
            if not force and not self._should_check_update():
                logger.info("跳過更新檢查，上次檢查時間未超過檢查間隔")
                return False, None, "跳過更新檢查，上次檢查時間未超過檢查間隔"

            # 記錄檢查時間
            self._record_check_time()

            # 發送請求
            logger.info(f"正在檢查更新: {self.server_url}")
            response = requests.get(
                self.server_url,
                params={"version": self.current_version},
                timeout=10
            )

            # 檢查狀態碼
            response.raise_for_status()

            # 解析回應
            data = response.json()

            # 檢查是否有更新
            if data.get("has_update", False):
                logger.info(f"發現新版本: {data.get('version')}")

                # 調用回調函數
                if self.on_update_available:
                    self.on_update_available(data)

                return True, data, None
            else:
                logger.info("沒有可用的更新")

                # 調用回調函數
                if self.on_update_not_available:
                    self.on_update_not_available()

                return False, None, None

        except Exception as e:
            logger.error(f"檢查更新失敗: {e}")

            # 調用回調函數
            if self.on_update_error:
                self.on_update_error(e)

            return False, None, str(e)

    def _should_check_update(self) -> bool:
        """檢查是否需要檢查更新

        Returns:
            bool: 是否需要檢查更新
        """
        try:
            # 檢查最後檢查時間文件是否存在
            if not os.path.exists(LAST_CHECK_FILE):
                return True

            # 讀取最後檢查時間
            with open(LAST_CHECK_FILE, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 獲取最後檢查時間
            last_check_time = data.get("last_check_time", 0)

            # 檢查是否超過檢查間隔
            return time.time() - last_check_time > self.check_interval

        except Exception as e:
            logger.warning(f"檢查更新時間失敗: {e}")
            return True

    def _record_check_time(self):
        """記錄檢查時間"""
        try:
            # 創建數據
            data = {
                "last_check_time": time.time(),
                "version": self.current_version
            }

            # 寫入文件
            with open(LAST_CHECK_FILE, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.warning(f"記錄檢查時間失敗: {e}")

    def download_update(self, update_info: Dict[str, Any], target_dir: Optional[str] = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """下載更新

        Args:
            update_info: 更新信息
            target_dir: 目標目錄，如果為 None，則使用臨時目錄

        Returns:
            Tuple[bool, Optional[str], Optional[str]]: 是否成功、下載路徑、錯誤信息
        """
        try:
            # 獲取下載 URL
            download_url = update_info.get("download_url")
            if not download_url:
                error_msg = "更新信息中沒有下載 URL"
                logger.error(error_msg)

                # 調用回調函數
                if self.on_download_error:
                    self.on_download_error(Exception(error_msg))

                return False, None, error_msg

            # 獲取文件名
            file_name = os.path.basename(urlparse(download_url).path)
            if not file_name:
                file_name = f"vp_test_tool_update_{update_info.get('version', 'unknown')}.zip"

            # 獲取目標目錄
            if target_dir is None:
                target_dir = tempfile.gettempdir()

            # 創建目標目錄
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)

            # 構建目標路徑
            target_path = os.path.join(target_dir, file_name)

            # 下載文件
            logger.info(f"正在下載更新: {download_url} -> {target_path}")

            # 發送請求
            response = requests.get(download_url, stream=True, timeout=60)

            # 檢查狀態碼
            response.raise_for_status()

            # 獲取文件大小
            total_size = int(response.headers.get("content-length", 0))

            # 下載文件
            with open(target_path, "wb") as f:
                downloaded_size = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # 計算進度
                        if total_size > 0:
                            progress = downloaded_size / total_size
                        else:
                            progress = 0

                        # 調用回調函數
                        if self.on_download_progress:
                            self.on_download_progress(progress)

            logger.info(f"更新下載完成: {target_path}")

            # 調用回調函數
            if self.on_download_complete:
                self.on_download_complete(target_path)

            return True, target_path, None

        except Exception as e:
            logger.error(f"下載更新失敗: {e}")

            # 調用回調函數
            if self.on_download_error:
                self.on_download_error(e)

            return False, None, str(e)

    def install_update(self, update_path: str) -> Tuple[bool, Optional[str]]:
        """安裝更新

        Args:
            update_path: 更新文件路徑

        Returns:
            Tuple[bool, Optional[str]]: 是否成功、錯誤信息
        """
        try:
            # 檢查文件是否存在
            if not os.path.exists(update_path):
                error_msg = f"更新文件不存在: {update_path}"
                logger.error(error_msg)
                return False, error_msg

            # 獲取文件擴展名
            _, ext = os.path.splitext(update_path)

            # 根據文件類型安裝更新
            if ext.lower() == ".zip":
                # 解壓縮 ZIP 文件
                return self._install_from_zip(update_path)
            elif ext.lower() == ".exe":
                # 運行 EXE 文件
                return self._install_from_exe(update_path)
            else:
                error_msg = f"不支持的更新文件類型: {ext}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            logger.error(f"安裝更新失敗: {e}")
            return False, str(e)

    def _install_from_zip(self, zip_path: str) -> Tuple[bool, Optional[str]]:
        """從 ZIP 文件安裝更新

        Args:
            zip_path: ZIP 文件路徑

        Returns:
            Tuple[bool, Optional[str]]: 是否成功、錯誤信息
        """
        try:
            import zipfile

            # 獲取應用程序目錄
            app_dir = os.path.dirname(os.path.dirname(__file__))

            # 創建臨時目錄
            temp_dir = tempfile.mkdtemp()

            # 解壓縮 ZIP 文件
            logger.info(f"正在解壓縮更新文件: {zip_path} -> {temp_dir}")
            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                zip_ref.extractall(temp_dir)

            # 備份當前版本
            backup_dir = os.path.join(tempfile.gettempdir(), f"vp_test_tool_backup_{int(time.time())}")
            logger.info(f"正在備份當前版本: {app_dir} -> {backup_dir}")
            shutil.copytree(app_dir, backup_dir)

            # 複製更新文件
            logger.info(f"正在安裝更新: {temp_dir} -> {app_dir}")
            for root, dirs, files in os.walk(temp_dir):
                # 獲取相對路徑
                rel_path = os.path.relpath(root, temp_dir)
                if rel_path == ".":
                    rel_path = ""

                # 創建目錄
                for dir_name in dirs:
                    target_dir = os.path.join(app_dir, rel_path, dir_name)
                    if not os.path.exists(target_dir):
                        os.makedirs(target_dir)

                # 複製文件
                for file_name in files:
                    source_file = os.path.join(root, file_name)
                    target_file = os.path.join(app_dir, rel_path, file_name)
                    shutil.copy2(source_file, target_file)

            # 清理臨時目錄
            logger.info(f"正在清理臨時目錄: {temp_dir}")
            shutil.rmtree(temp_dir)

            logger.info("更新安裝完成")
            return True, None

        except Exception as e:
            logger.error(f"從 ZIP 文件安裝更新失敗: {e}")
            return False, str(e)

    def _install_from_exe(self, exe_path: str) -> Tuple[bool, Optional[str]]:
        """從 EXE 文件安裝更新

        Args:
            exe_path: EXE 文件路徑

        Returns:
            Tuple[bool, Optional[str]]: 是否成功、錯誤信息
        """
        try:
            # 運行 EXE 文件
            logger.info(f"正在運行更新安裝程序: {exe_path}")
            subprocess.Popen([exe_path], shell=True)

            # 退出當前程序
            logger.info("更新安裝程序已啟動，正在退出當前程序")
            sys.exit(0)

        except Exception as e:
            logger.error(f"從 EXE 文件安裝更新失敗: {e}")
            return False, str(e)

    def show_update_dialog(self, update_info: Dict[str, Any]):
        """顯示更新對話框

        Args:
            update_info: 更新信息
        """
        try:
            # 檢查父窗口
            if self.parent is None:
                root = tk.Tk()
                root.withdraw()
                parent = root
            else:
                parent = self.parent

            # 創建對話框
            dialog = tk.Toplevel(parent)
            dialog.title("發現新版本")
            dialog.geometry("500x400")
            dialog.minsize(400, 300)

            # 設置模態
            dialog.transient(parent)
            dialog.grab_set()

            # 創建內容框架
            content_frame = ttk.Frame(dialog, padding=10)
            content_frame.pack(fill=tk.BOTH, expand=True)

            # 創建標題標籤
            title_label = ttk.Label(
                content_frame,
                text=f"發現新版本: {update_info.get('version')}",
                font=("Microsoft JhengHei UI", 12, "bold")
            )
            title_label.pack(pady=(0, 10))

            # 創建描述標籤
            desc_label = ttk.Label(
                content_frame,
                text=f"當前版本: {self.current_version}",
                font=("Microsoft JhengHei UI", 10)
            )
            desc_label.pack(pady=(0, 5))

            # 創建更新日期標籤
            date_label = ttk.Label(
                content_frame,
                text=f"發布日期: {update_info.get('release_date', '未知')}",
                font=("Microsoft JhengHei UI", 10)
            )
            date_label.pack(pady=(0, 10))

            # 創建更新說明框架
            notes_frame = ttk.LabelFrame(content_frame, text="更新說明")
            notes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 創建更新說明文本框
            notes_text = tk.Text(notes_frame, wrap=tk.WORD, font=("Microsoft JhengHei UI", 10))
            notes_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 添加滾動條
            notes_scrollbar = ttk.Scrollbar(notes_text)
            notes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            notes_text.config(yscrollcommand=notes_scrollbar.set)
            notes_scrollbar.config(command=notes_text.yview)

            # 插入更新說明
            notes_text.insert(tk.END, update_info.get("release_notes", "無更新說明"))
            notes_text.config(state=tk.DISABLED)

            # 創建按鈕框架
            button_frame = ttk.Frame(content_frame)
            button_frame.pack(fill=tk.X, pady=(0, 5))

            # 創建下載進度條
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                content_frame,
                variable=progress_var,
                maximum=100,
                mode="determinate"
            )
            progress_bar.pack(fill=tk.X, pady=(0, 10))
            progress_bar.grid_remove()  # 隱藏進度條

            # 創建狀態標籤
            status_var = tk.StringVar()
            status_label = ttk.Label(
                content_frame,
                textvariable=status_var,
                font=("Microsoft JhengHei UI", 10)
            )
            status_label.pack(pady=(0, 5))
            status_label.grid_remove()  # 隱藏狀態標籤

            # 下載回調函數
            def on_download_progress(progress):
                progress_var.set(progress * 100)
                dialog.update_idletasks()

            # 下載完成回調函數
            def on_download_complete(download_path):
                status_var.set(f"下載完成: {download_path}")

                # 詢問是否安裝更新
                if messagebox.askyesno("安裝更新", "更新下載完成，是否立即安裝？"):
                    # 安裝更新
                    status_var.set("正在安裝更新...")
                    success, error = self.install_update(download_path)

                    if success:
                        messagebox.showinfo("安裝完成", "更新已成功安裝，請重新啟動程序。")
                        dialog.destroy()
                        sys.exit(0)
                    else:
                        messagebox.showerror("安裝失敗", f"安裝更新失敗: {error}")
                        status_var.set(f"安裝失敗: {error}")

                # 啟用下載按鈕
                download_button.config(state=tk.NORMAL)

            # 下載錯誤回調函數
            def on_download_error(error):
                status_var.set(f"下載失敗: {error}")
                messagebox.showerror("下載失敗", f"下載更新失敗: {error}")

                # 啟用下載按鈕
                download_button.config(state=tk.NORMAL)

            # 下載按鈕點擊事件
            def on_download_click():
                # 禁用下載按鈕
                download_button.config(state=tk.DISABLED)

                # 顯示進度條和狀態標籤
                progress_bar.grid()
                status_label.grid()

                # 設置狀態
                status_var.set("正在下載更新...")

                # 設置回調函數
                self.on_download_progress = on_download_progress
                self.on_download_complete = on_download_complete
                self.on_download_error = on_download_error

                # 啟動下載線程
                threading.Thread(
                    target=self.download_update,
                    args=(update_info,),
                    daemon=True
                ).start()

            # 創建下載按鈕
            download_button = ttk.Button(
                button_frame,
                text="下載更新",
                command=on_download_click
            )
            download_button.pack(side=tk.RIGHT, padx=5)

            # 創建取消按鈕
            cancel_button = ttk.Button(
                button_frame,
                text="稍後再說",
                command=dialog.destroy
            )
            cancel_button.pack(side=tk.RIGHT, padx=5)

            # 創建查看詳情按鈕
            details_button = ttk.Button(
                button_frame,
                text="查看詳情",
                command=lambda: self._open_url(update_info.get("details_url", ""))
            )
            details_button.pack(side=tk.LEFT, padx=5)

            # 如果沒有詳情 URL，禁用按鈕
            if not update_info.get("details_url"):
                details_button.config(state=tk.DISABLED)

            # 居中顯示
            dialog.update_idletasks()
            width = dialog.winfo_width()
            height = dialog.winfo_height()
            x = (dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (dialog.winfo_screenheight() // 2) - (height // 2)
            dialog.geometry(f"{width}x{height}+{x}+{y}")

            # 等待對話框關閉
            parent.wait_window(dialog)

        except Exception as e:
            logger.error(f"顯示更新對話框失敗: {e}")
            messagebox.showerror("更新錯誤", f"顯示更新對話框失敗: {e}")

    def _open_url(self, url: str):
        """打開 URL

        Args:
            url: URL
        """
        try:
            import webbrowser
            webbrowser.open(url)
        except Exception as e:
            logger.error(f"打開 URL 失敗: {e}")
            messagebox.showerror("錯誤", f"打開 URL 失敗: {e}")

# 創建全局自動更新器實例
auto_updater = AutoUpdater()

def check_for_updates(parent=None, force=False):
    """檢查更新

    Args:
        parent: 父窗口
        force: 是否強制檢查更新，忽略檢查間隔
    """
    try:
        # 創建自動更新器
        updater = AutoUpdater(parent)

        # 檢查更新
        has_update, update_info, error = updater.check_for_updates(force)

        if error:
            if force:
                messagebox.showerror("檢查更新失敗", f"檢查更新失敗: {error}")
            return

        if has_update:
            # 顯示更新對話框
            updater.show_update_dialog(update_info)
        else:
            if force:
                messagebox.showinfo("檢查更新", "您的程序已是最新版本。")

    except Exception as e:
        logger.error(f"檢查更新失敗: {e}")
        if force:
            messagebox.showerror("檢查更新失敗", f"檢查更新失敗: {e}")

def check_for_updates_on_startup():
    """在啟動時檢查更新"""
    try:
        # 創建自動更新器
        updater = AutoUpdater()

        # 檢查更新
        has_update, update_info, error = updater.check_for_updates()

        if has_update:
            # 創建根窗口
            root = tk.Tk()
            root.withdraw()

            # 顯示更新對話框
            updater.parent = root
            updater.show_update_dialog(update_info)

            # 等待對話框關閉
            root.mainloop()

    except Exception as e:
        logger.error(f"啟動時檢查更新失敗: {e}")

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)

    # 創建模擬更新信息
    mock_update_info = {
        "version": "2.5.0",
        "release_date": "2025-05-01",
        "release_notes": "1. 添加自動更新功能\n2. 優化錯誤處理\n3. 添加功能檢測\n4. 修復已知問題",
        "download_url": "https://example.com/vp-test-tool/updates/vp_test_tool_2.5.0.zip",
        "details_url": "https://example.com/vp-test-tool/updates/details/2.5.0"
    }

    # 創建根窗口
    root = tk.Tk()
    root.title("自動更新測試")
    root.geometry("300x200")

    # 創建按鈕
    check_button = ttk.Button(
        root,
        text="檢查更新",
        command=lambda: check_for_updates(root, True)
    )
    check_button.pack(pady=20)

    # 創建模擬更新按鈕
    mock_button = ttk.Button(
        root,
        text="模擬更新",
        command=lambda: AutoUpdater(root).show_update_dialog(mock_update_info)
    )
    mock_button.pack(pady=20)

    # 啟動主循環
    root.mainloop()
