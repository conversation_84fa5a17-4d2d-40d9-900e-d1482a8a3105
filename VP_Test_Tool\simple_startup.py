"""
VP Test Tool V2.3.4 - 簡單模式
簡單的啟動腳本，用於測試程式是否能正常啟動
不進行任何 API 調用，只顯示基本界面
"""
import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import traceback

def main():
    """主函數"""
    try:
        # 建立根視窗
        root = tk.Tk()
        root.title("VP Test Tool - 簡單模式")
        root.geometry("800x600")

        # 設定視窗圖示
        try:
            icon_path = os.path.join("assets", "icons", "vp_test_tool.ico")
            if os.path.exists(icon_path):
                root.iconbitmap(icon_path)
        except Exception as e:
            print(f"無法載入應用程式圖示: {e}")

        # 建立標籤
        title_label = ttk.Label(
            root,
            text="VP Test Tool - 簡單模式",
            font=("Microsoft JhengHei UI", 16, "bold")
        )
        title_label.pack(pady=20)

        # 建立說明標籤
        desc_label = ttk.Label(
            root,
            text="這是一個簡單的啟動模式，不進行任何 API 調用，只顯示基本界面。",
            font=("Microsoft JhengHei UI", 10)
        )
        desc_label.pack(pady=10)

        # 建立狀態標籤
        status_label = ttk.Label(
            root,
            text="程式已成功啟動",
            font=("Microsoft JhengHei UI", 10),
            foreground="green"
        )
        status_label.pack(pady=10)

        # 建立按鈕框架
        button_frame = ttk.Frame(root)
        button_frame.pack(pady=20)

        # 建立啟動主程式按鈕
        def start_main_app():
            try:
                root.destroy()
                import main
                main.main()
            except Exception as e:
                show_error("啟動失敗", f"主程式啟動失敗: {e}\n\n{traceback.format_exc()}")

        start_button = ttk.Button(
            button_frame,
            text="啟動主程式",
            command=start_main_app
        )
        start_button.pack(side=tk.LEFT, padx=10)

        # 建立啟動診斷模式按鈕
        def start_diagnostic_mode():
            try:
                root.destroy()
                import debug_startup
                debug_startup.main()
            except Exception as e:
                show_error("啟動失敗", f"診斷模式啟動失敗: {e}\n\n{traceback.format_exc()}")

        diagnostic_button = ttk.Button(
            button_frame,
            text="啟動診斷模式",
            command=start_diagnostic_mode
        )
        diagnostic_button.pack(side=tk.LEFT, padx=10)

        # 建立退出按鈕
        exit_button = ttk.Button(
            button_frame,
            text="退出",
            command=root.destroy
        )
        exit_button.pack(side=tk.LEFT, padx=10)

        # 啟動主循環
        root.mainloop()

    except Exception as e:
        error_message = f"程式啟動失敗: {e}\n\n{traceback.format_exc()}"
        print(error_message)
        show_error("啟動錯誤", error_message)

def show_error(title, message):
    """顯示錯誤對話框"""
    root = tk.Tk()
    root.withdraw()  # 隱藏主窗口
    messagebox.showerror(title, message)
    root.destroy()

if __name__ == "__main__":
    main()
