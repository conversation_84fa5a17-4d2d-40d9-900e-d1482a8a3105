"""
使用 Nuitka 打包 VP Test Tool 為 exe 文件
Nuitka 將 Python 代碼編譯為 C++，然後編譯為原生可執行文件
這可能會減少被防毒軟體誤判為病毒的機率
"""
import os
import sys
import subprocess
import shutil

def main():
    """主函數"""
    print("開始使用 Nuitka 打包 VP Test Tool 為 exe 文件...")

    # 檢查 Nuitka 是否已安裝
    try:
        subprocess.run([sys.executable, "-m", "nuitka", "--version"], check=True, capture_output=True)
        print("已安裝 Nuitka")
    except (subprocess.SubprocessError, FileNotFoundError):
        print("Nuitka 未安裝，正在安裝...")
        subprocess.run([sys.executable, "-m", "pip", "install", "nuitka"], check=True)
        print("Nuitka 安裝完成")

    # 檢查圖示文件是否存在
    icon_path = os.path.join("assets", "icons", "vp_test_tool.ico")
    if not os.path.exists(icon_path):
        print(f"錯誤: 圖示文件 {icon_path} 不存在")
        return

    # 創建 dist 目錄（如果不存在）
    if not os.path.exists("dist"):
        os.makedirs("dist")

    # 打包命令
    cmd = [
        sys.executable,
        "-m", "nuitka",
        "--standalone",                  # 創建獨立的可執行文件
        "--windows-disable-console",     # 禁用控制台窗口
        f"--windows-icon-from-ico={icon_path}",  # 設置圖示
        "--follow-imports",              # 跟踪所有導入
        "--include-package=PIL",         # 包含 PIL 包
        "--include-package=tkinter",     # 包含 tkinter 包
        "--include-package=utils",       # 包含 utils 包
        "--include-package=views",       # 包含 views 包
        "--include-package=models",      # 包含 models 包
        "--include-package=controllers", # 包含 controllers 包
        "--include-package=widgets",     # 包含 widgets 包
        "--include-data-dir=assets=assets",  # 包含資源文件
        "--output-dir=dist",             # 輸出目錄
        "--output-filename=VP_Test_Tool.exe",  # 輸出文件名
        "--assume-yes-for-downloads",    # 自動下載依賴
        "--jobs=4",                      # 使用 4 個線程編譯
        "--lto=yes",                     # 啟用鏈接時優化
        "--windows-company-name=VP_Test_Tool",  # 公司名稱
        "--windows-product-name=VP_Test_Tool",  # 產品名稱
        "--windows-file-version=*******",       # 文件版本
        "--windows-product-version=*******",    # 產品版本
        "--windows-file-description=VP Test Tool",  # 文件描述
        "main.py"                        # 主程序文件
    ]

    # 執行打包命令
    print("執行打包命令...")
    print(" ".join(cmd))
    subprocess.run(cmd, check=True)

    print("打包完成！")
    print(f"exe 文件位於: {os.path.abspath(os.path.join('dist', 'VP_Test_Tool.exe'))}")

if __name__ == "__main__":
    main()
