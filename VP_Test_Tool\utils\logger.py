"""日誌管理"""
import logging
import sys
from pathlib import Path
from typing import Optional

def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """設定並返回 Logger 實例"""
    logger = logging.getLogger(name)

    # 如果已經有 handler，直接返回
    if logger.handlers:
        return logger

    # 設定日誌格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 檔案處理器
    log_path = Path("logs")
    log_path.mkdir(exist_ok=True)
    file_handler = logging.FileHandler(
        log_path / "app.log",
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)

    # 控制台處理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 設定日誌等級
    logger.setLevel(level)
    file_handler.setLevel(level)
    console_handler.setLevel(level)

    # 添加處理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def get_logger(name: str) -> logging.Logger:
    """取得 Logger 實例"""
    return setup_logger(name)
