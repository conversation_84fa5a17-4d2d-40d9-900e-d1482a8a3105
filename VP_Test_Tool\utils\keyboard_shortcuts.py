"""鍵盤快捷鍵管理器"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, Callable, Optional, List
from utils.logger import setup_logger

# 設定日誌
logger = setup_logger(__name__)

class KeyboardShortcuts:
    """鍵盤快捷鍵管理器

    負責管理應用程式的鍵盤快捷鍵，提供統一的快捷鍵註冊和管理介面。
    """

    def __init__(self, root: tk.Tk):
        """初始化鍵盤快捷鍵管理器

        Args:
            root: 根視窗
        """
        self.root = root
        self.shortcuts = {}

    def add_shortcut(self, key_combo: str, callback: Callable, description: str = ""):
        """添加快捷鍵

        Args:
            key_combo: 快捷鍵組合，例如 "<Control-s>"
            callback: 快捷鍵觸發時執行的函數
            description: 快捷鍵描述
        """
        self.shortcuts[key_combo] = {
            "callback": callback,
            "description": description
        }

        # 綁定快捷鍵
        try:
            self.root.bind(key_combo, lambda e: self._execute_callback(key_combo))
            logger.info(f"已註冊快捷鍵: {key_combo} - {description}")
        except Exception as e:
            logger.error(f"註冊快捷鍵失敗: {key_combo} - {e}")

    def remove_shortcut(self, key_combo: str):
        """移除快捷鍵

        Args:
            key_combo: 快捷鍵組合
        """
        if key_combo in self.shortcuts:
            # 解除綁定快捷鍵
            try:
                self.root.unbind(key_combo)
                del self.shortcuts[key_combo]
                logger.info(f"已移除快捷鍵: {key_combo}")
            except Exception as e:
                logger.error(f"移除快捷鍵失敗: {key_combo} - {e}")

    def _execute_callback(self, key_combo: str):
        """執行快捷鍵回調函數

        Args:
            key_combo: 快捷鍵組合
        """
        if key_combo in self.shortcuts:
            try:
                self.shortcuts[key_combo]["callback"]()
            except Exception as e:
                logger.error(f"執行快捷鍵回調函數失敗: {key_combo} - {e}")

    def get_shortcuts(self) -> Dict[str, Dict]:
        """取得所有快捷鍵

        Returns:
            Dict[str, Dict]: 所有快捷鍵的字典
        """
        return self.shortcuts.copy()

    def get_shortcut_descriptions(self) -> List[str]:
        """取得所有快捷鍵描述

        Returns:
            List[str]: 所有快捷鍵描述的列表
        """
        descriptions = []
        for key_combo, data in self.shortcuts.items():
            descriptions.append(f"{key_combo}: {data['description']}")
        return descriptions

    def show_help(self, parent: Optional[tk.Widget] = None):
        """顯示快捷鍵幫助

        Args:
            parent: 父元件
        """
        if not self.shortcuts:
            return

        # 建立幫助視窗
        help_window = tk.Toplevel(parent or self.root)
        help_window.title("鍵盤快捷鍵")
        help_window.transient(parent or self.root)
        help_window.resizable(False, False)

        # 建立標題標籤
        title_label = tk.Label(
            help_window,
            text="鍵盤快捷鍵",
            font=("Microsoft JhengHei UI", 12, "bold")
        )
        title_label.pack(pady=(10, 5))

        # 建立分隔線
        separator = ttk.Separator(help_window, orient="horizontal")
        separator.pack(fill="x", padx=10, pady=5)

        # 建立快捷鍵列表
        frame = ttk.Frame(help_window, padding=10)
        frame.pack(fill="both", expand=True)

        # 建立標題
        ttk.Label(
            frame,
            text="快捷鍵",
            font=("Microsoft JhengHei UI", 10, "bold")
        ).grid(row=0, column=0, sticky="w", padx=5, pady=5)

        ttk.Label(
            frame,
            text="描述",
            font=("Microsoft JhengHei UI", 10, "bold")
        ).grid(row=0, column=1, sticky="w", padx=5, pady=5)

        # 建立分隔線
        separator = ttk.Separator(frame, orient="horizontal")
        separator.grid(row=1, column=0, columnspan=2, sticky="ew", pady=5)

        # 顯示快捷鍵
        for i, (key_combo, data) in enumerate(self.shortcuts.items()):
            ttk.Label(
                frame,
                text=key_combo,
                font=("Microsoft JhengHei UI", 10)
            ).grid(row=i+2, column=0, sticky="w", padx=5, pady=2)

            ttk.Label(
                frame,
                text=data["description"],
                font=("Microsoft JhengHei UI", 10)
            ).grid(row=i+2, column=1, sticky="w", padx=5, pady=2)

        # 建立關閉按鈕
        ttk.Button(
            help_window,
            text="關閉",
            command=help_window.destroy
        ).pack(pady=10)

        # 設定視窗位置
        help_window.update_idletasks()
        width = help_window.winfo_width()
        height = help_window.winfo_height()
        x = (help_window.winfo_screenwidth() // 2) - (width // 2)
        y = (help_window.winfo_screenheight() // 2) - (height // 2)
        help_window.geometry(f"{width}x{height}+{x}+{y}")

        # 設定為模態對話框
        help_window.grab_set()
        help_window.focus_set()
        help_window.wait_window()
