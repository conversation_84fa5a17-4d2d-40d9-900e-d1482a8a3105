"""ThreadManager 單元測試"""
import unittest
import os
import sys
import time
import threading
from unittest.mock import patch, MagicMock

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.thread_manager import ThreadManager
from utils.exceptions import ThreadCreateError, ThreadTerminateError

class TestThreadManager(unittest.TestCase):
    """ThreadManager 單元測試類"""

    def setUp(self):
        """測試前準備"""
        self.thread_manager = ThreadManager()

    def tearDown(self):
        """測試後清理"""
        self.thread_manager.shutdown(wait=True)

    def test_create_thread(self):
        """測試創建線程"""
        # 創建一個簡單的線程函數
        result = {"value": None}
        
        def thread_func():
            result["value"] = "success"
            time.sleep(0.1)  # 短暫延遲
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="test_thread",
            target=thread_func
        )
        
        # 等待線程完成
        self.thread_manager.wait_for_thread(thread_id, timeout=1)
        
        # 驗證結果
        self.assertEqual(result["value"], "success")
        self.assertFalse(self.thread_manager.is_thread_alive(thread_id))

    def test_create_thread_with_args(self):
        """測試創建帶參數的線程"""
        # 創建一個帶參數的線程函數
        result = {"value": None}
        
        def thread_func(arg1, arg2, kwarg1=None):
            result["value"] = f"{arg1}_{arg2}_{kwarg1}"
            time.sleep(0.1)  # 短暫延遲
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="test_thread_args",
            target=thread_func,
            args=("arg1", "arg2"),
            kwargs={"kwarg1": "kwarg1"}
        )
        
        # 等待線程完成
        self.thread_manager.wait_for_thread(thread_id, timeout=1)
        
        # 驗證結果
        self.assertEqual(result["value"], "arg1_arg2_kwarg1")
        self.assertFalse(self.thread_manager.is_thread_alive(thread_id))

    def test_thread_status(self):
        """測試線程狀態"""
        # 創建一個長時間運行的線程
        def long_running_thread():
            time.sleep(0.5)
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="long_running_thread",
            target=long_running_thread
        )
        
        # 檢查線程狀態
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "running")
        self.assertTrue(status["is_alive"])
        
        # 等待線程完成
        self.thread_manager.wait_for_thread(thread_id, timeout=1)
        
        # 再次檢查線程狀態
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "completed")
        self.assertFalse(status["is_alive"])

    def test_thread_error(self):
        """測試線程錯誤"""
        # 創建一個會拋出異常的線程
        def error_thread():
            raise ValueError("Test error")
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="error_thread",
            target=error_thread
        )
        
        # 等待線程完成
        self.thread_manager.wait_for_thread(thread_id, timeout=1)
        
        # 檢查線程狀態
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "error")
        self.assertIn("Test error", status["error"])
        self.assertFalse(status["is_alive"])

    def test_wait_for_thread(self):
        """測試等待線程"""
        # 創建一個長時間運行的線程
        def long_running_thread():
            time.sleep(0.5)
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="wait_thread",
            target=long_running_thread
        )
        
        # 等待線程完成，應該返回 True
        result = self.thread_manager.wait_for_thread(thread_id, timeout=1)
        self.assertTrue(result)
        
        # 檢查線程狀態
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "completed")
        self.assertFalse(status["is_alive"])

    def test_wait_for_thread_timeout(self):
        """測試等待線程超時"""
        # 創建一個非常長時間運行的線程
        def very_long_running_thread():
            time.sleep(2)
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="timeout_thread",
            target=very_long_running_thread
        )
        
        # 等待線程完成，但超時，應該返回 False
        result = self.thread_manager.wait_for_thread(thread_id, timeout=0.1)
        self.assertFalse(result)
        
        # 檢查線程狀態，應該仍在運行
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "running")
        self.assertTrue(status["is_alive"])

    def test_terminate_thread(self):
        """測試終止線程"""
        # 創建一個長時間運行的線程
        def long_running_thread():
            time.sleep(1)
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="terminate_thread",
            target=long_running_thread
        )
        
        # 終止線程
        result = self.thread_manager.terminate_thread(thread_id)
        self.assertTrue(result)
        
        # 檢查線程狀態
        status = self.thread_manager.get_thread_status(thread_id)
        self.assertEqual(status["status"], "terminated")
        
        # 注意：Python 不支持直接終止線程，所以線程實際上仍在運行
        # 這裡我們只是標記了線程狀態

    def test_get_all_threads(self):
        """測試獲取所有線程"""
        # 創建多個線程
        def thread_func():
            time.sleep(0.2)
        
        thread_id1 = self.thread_manager.create_thread(
            name="thread1",
            target=thread_func
        )
        
        thread_id2 = self.thread_manager.create_thread(
            name="thread2",
            target=thread_func
        )
        
        # 獲取所有線程
        threads = self.thread_manager.get_all_threads()
        
        # 驗證結果
        self.assertEqual(len(threads), 2)
        thread_names = [t["name"] for t in threads]
        self.assertIn("thread1", thread_names)
        self.assertIn("thread2", thread_names)

    def test_cleanup(self):
        """測試清理已完成的線程"""
        # 創建一個快速完成的線程
        def quick_thread():
            pass
        
        # 創建線程
        thread_id = self.thread_manager.create_thread(
            name="quick_thread",
            target=quick_thread
        )
        
        # 等待線程完成
        time.sleep(0.1)
        
        # 清理已完成的線程
        self.thread_manager.cleanup()
        
        # 驗證線程已被清理
        with self.assertRaises(ValueError):
            self.thread_manager.wait_for_thread(thread_id)

    def test_shutdown(self):
        """測試關閉線程管理器"""
        # 創建多個線程
        def thread_func():
            time.sleep(0.2)
        
        self.thread_manager.create_thread(
            name="shutdown_thread1",
            target=thread_func
        )
        
        self.thread_manager.create_thread(
            name="shutdown_thread2",
            target=thread_func
        )
        
        # 關閉線程管理器
        self.thread_manager.shutdown(wait=True)
        
        # 驗證所有線程已被清理
        threads = self.thread_manager.get_all_threads()
        self.assertEqual(len(threads), 0)

if __name__ == '__main__':
    unittest.main()
