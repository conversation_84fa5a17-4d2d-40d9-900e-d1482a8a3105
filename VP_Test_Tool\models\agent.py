"""Agent 相關的資料模型"""
from dataclasses import dataclass
from typing import Optional, List, Dict
from datetime import datetime
import logging
from utils.http_client import HttpClient
from utils.constants import API_URLS

logger = logging.getLogger(__name__)

@dataclass
class Agent:
    """代理商資料模型"""
    id: int
    agent_code: str  # 對應 API 中的 AgentCode
    nickname: Optional[str] = None  # 對應 API 中的 NickName
    site_account_mode: str = "Disable"  # 對應 API 中的 SiteAccountMode
    created_at: Optional[datetime] = None

@dataclass
class SubAgent:
    """子代理商資料模型"""
    code: str  # 對應 API 中的 SubAgentCode
    agent_code: str
    currency: str  # 幣別
    id: Optional[int] = None  # 對應 API 中的 Id
    name: Optional[str] = None
    status: str = "active"
    created_at: Optional[datetime] = None

    @property
    def display_name(self) -> str:
        """取得顯示名稱，結合 SubAgentCode 和 Currency"""
        return f"{self.code} - {self.currency}"

class AgentService:
    """代理商服務類"""
    def __init__(self, http_client: HttpClient):
        self.http_client = http_client

    def query_all_agents(self) -> List[Agent]:
        """查詢所有代理商"""
        try:
            # 使用新的API獲取代理商列表
            response = self.http_client.post(
                API_URLS["QUERY_AGENTS"],
                json_data={}
            )

            # 記錄完整的 API 回應
            logger.debug(f"API 回應: {response}")

            # 確保回應是正確的格式
            if response and isinstance(response, dict):
                # 檢查是否有 data 欄位
                if "data" in response and isinstance(response["data"], list):
                    agents = response["data"]

                    return [
                        Agent(
                            id=row.get("Id", 0),
                            agent_code=row.get("AgentCode", ""),
                            nickname=row.get("NickName", ""),
                            site_account_mode=row.get("SiteAccountMode", "Disable")
                        ) for row in agents
                    ]
            # 不使用模擬數據
            logger.warning("API 回應格式不正確")
        except Exception as e:
            logger.error(f"查詢代理商失敗: {e}")
        return []

    def query_all_sub_agents(self) -> List[SubAgent]:
        """查詢所有子代理商"""
        try:
            payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": "SELECT SubAgentCode, AgentCode, Name, Status FROM Platform.SubAgent ORDER BY AgentCode, SubAgentCode ASC;"
            }

            response = self.http_client.post(
                API_URLS["MYSQL_OPERATOR"],
                json_data=payload
            )

            if response and response.get("success") and response.get("data"):
                return [
                    SubAgent(
                        code=row["SubAgentCode"],
                        agent_code=row["AgentCode"],
                        name=row["Name"],
                        status=row["Status"]
                    ) for row in response["data"]
                ]
        except Exception as e:
            logger.error(f"查詢子代理商失敗: {e}")
        return []

    # 移除舊的 query_sub_agents_by_agent 方法，使用新的 query_sub_agents_by_agent_code 方法替代
    def query_sub_agents_by_agent_code(self, agent_code: str) -> List[SubAgent]:
        """根據代理商代碼查詢子代理商"""
        try:
            # 使用新的API獲取子代理商列表
            payload = {
                "AgentCode": agent_code
            }

            response = self.http_client.post(
                API_URLS["QUERY_SUB_AGENTS"],
                json_data=payload
            )

            # 記錄完整的 API 回應
            logger.debug(f"API 回應: {response}")

            # 確保回應是正確的格式
            if response and isinstance(response, dict):
                # 檢查是否有 data 欄位
                if "data" in response and isinstance(response["data"], list):
                    sub_agents = response["data"]

                    return [
                        SubAgent(
                            id=row.get("Id", 0),
                            code=row.get("SubAgentCode", ""),
                            agent_code=agent_code,
                            name=row.get("Name", ""),
                            status=row.get("Status", "active"),
                            currency=row.get("Currency", "PHP")
                        ) for row in sub_agents
                    ]
            # 不使用模擬數據
            logger.warning("API 回應格式不正確")
        except Exception as e:
            logger.error(f"根據代理商ID查詢子代理商失敗: {e}")
        return []

    def validate_agent_code(self, code: str) -> bool:
        """驗證代理商代碼"""
        return bool(code and code.strip())

    def query_all_currencies(self) -> List[Dict[str, str]]:
        """查詢所有幣別"""
        try:
            payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": "SELECT Currency, Name FROM Platform.Currency;"
            }

            response = self.http_client.post(
                API_URLS["MYSQL_OPERATOR"],
                json_data=payload
            )

            if response and response.get("success") and response.get("data"):
                return response["data"]
        except Exception as e:
            logger.error(f"查詢幣別失敗: {e}")
        # 不使用模擬數據
        logger.warning("無法獲取幣別列表")
        return []

    def get_agent_by_code(self, code: str) -> Optional[Agent]:
        """根據代理商代碼獲取代理商"""
        agents = self.query_all_agents()
        for agent in agents:
            if agent.agent_code == code:
                return agent
        return None
