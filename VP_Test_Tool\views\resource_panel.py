"""資源調整面板"""
import tkinter as tk
from tkinter import ttk, messagebox
from tkinter.scrolledtext import ScrolledText
import locale
import threading
import queue
from utils.constants import PADDING
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from widgets.modern_button import ModernButton

class ResourcePanel(ttk.Frame):
    """資源調整面板"""
    def __init__(self, parent, main_window=None):
        super().__init__(parent)
        # 初始化主題管理器
        self.theme_manager = ThemeManager()
        # 儲存主視窗引用，用於顯示通知
        self.main_window = main_window
        # 設定區域設定為台灣，以使用正確的千分位格式
        locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')

        # 創建 UI 更新隊列和啟動處理器
        self.ui_queue = queue.Queue()
        self.is_ui_processor_running = False

        self._init_ui()

    def _init_ui(self):
        """初始化 UI"""
        # 啟動 UI 更新處理器
        self._start_ui_processor()

        # 建立主容器框架
        container_frame = tk.Frame(self, bg=self.theme_manager.get_color("surface"))
        container_frame.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 初始化閃爍狀態變數
        self.blink_state = False
        self.blink_job = None

        # 左側設定區域 - 佔空間的 50%
        left_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING/2))

        # 右側日誌區域 - 佔空間的 50%
        right_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING/2, 0))

        # 從卡片框架模組導入 CardFrame
        from widgets.card_frame import CardFrame
        # 從幫助按鈕模組導入 HelpButton
        from widgets.help_button import HelpButton

        # 添加幫助按鈕
        help_text = """資源調整工具可以幫助您管理會員的資源，包括金幣、VIP、寶石和抽獎券。

使用步驟：
1. 在「會員資訊」區域輸入平台帳號或 VP Member ID
2. 點擊「查詢會員」按鈕獲取會員資訊
3. 在「資源設定」區域輸入要調整的資源數量
4. 點擊「更新資源」按鈕完成調整

批次處理：
1. 在「批次處理」區域選擇檔案
2. 點擊「匯入檔案」按鈕
3. 點擊「開始處理」按鈕執行批次調整

所有操作日誌將顯示在右側的「操作日誌」區域。"""
        self.help_button = HelpButton(left_frame, help_text=help_text, title="資源調整工具幫助")
        self.help_button.pack(side=tk.TOP, anchor=tk.NW, padx=PADDING, pady=PADDING)

        # 會員資訊區域 - 使用卡片式設計
        member_card = CardFrame(left_frame, title="會員資訊", icon=IconManager.get('user'))
        member_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        member_frame = member_card.get_content_frame()

        # 設定列的權重，使輸入欄位更寬
        member_frame.columnconfigure(0, weight=2)  # 標籤列
        member_frame.columnconfigure(1, weight=5)  # 輸入欄位列
        member_frame.columnconfigure(2, weight=2)  # 按鈕列

        # 資料庫來源選擇
        tk.Label(
            member_frame,
            text="資料庫來源",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=0, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.db_source = ttk.Combobox(
            member_frame,
            values=["OCMS", "OCIntegrator"],
            width=15,
            state="readonly",
            font=("Microsoft JhengHei UI", 10)
        )
        self.db_source.set("OCMS")  # 預設值
        self.db_source.grid(row=0, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # 平台帳號輸入
        tk.Label(
            member_frame,
            text="平台帳號",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=1, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_account = ttk.Entry(member_frame, width=25, font=("Microsoft JhengHei UI", 10))
        self.entry_account.grid(row=1, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # VP 會員 ID 輸入
        tk.Label(
            member_frame,
            text="VP Member ID",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).grid(row=2, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_member_id = ttk.Entry(member_frame, width=25, font=("Microsoft JhengHei UI", 10))
        self.entry_member_id.grid(row=2, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        # 按鈕容器 - 垂直居中
        button_container = tk.Frame(member_frame, bg=self.theme_manager.get_color("surface"))
        button_container.grid(row=0, column=2, rowspan=3, padx=PADDING, pady=PADDING, sticky="ns")

        # 查詢按鈕
        self.btn_query = ModernButton(
            button_container,
            text="查詢會員",
            icon=IconManager.get('search'),
            button_type="primary"
        )
        self.btn_query.pack(side=tk.TOP, pady=(0, PADDING))

        # 清除資料按鈕
        self.btn_clear_member = ModernButton(
            button_container,
            text="清除資料",
            icon=IconManager.get('delete'),
            button_type="danger"
        )
        self.btn_clear_member.pack(side=tk.TOP)

        # 資源設定區域 - 使用卡片式設計
        resource_card = CardFrame(left_frame, title="資源設定", icon=IconManager.get('money'))
        resource_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        resource_frame = resource_card.get_content_frame()

        # 設定列的權重，使輸入欄位更寬
        resource_frame.columnconfigure(0, weight=2)  # 標籤列
        resource_frame.columnconfigure(1, weight=5)  # 輸入欄位列
        resource_frame.columnconfigure(2, weight=2)  # 按鈕列

        # 金幣數量設定
        tk.Label(resource_frame, text="金幣數量", font=("Microsoft JhengHei UI", 11, "bold"), bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).grid(row=0, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_coin = ttk.Entry(resource_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.entry_coin.grid(row=0, column=1, padx=PADDING, pady=PADDING, sticky="ew")
        # 設定預設值
        self.entry_coin.insert(0, self.format_number(10000))

        # VIP 等級設定
        tk.Label(resource_frame, text="VIP 等級 (0-6)", font=("Microsoft JhengHei UI", 11, "bold"), bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).grid(row=1, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_vip = ttk.Entry(resource_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.entry_vip.grid(row=1, column=1, padx=PADDING, pady=PADDING, sticky="ew")
        # 設定預設值
        self.entry_vip.insert(0, "6")

        # 寶石數量設定
        tk.Label(resource_frame, text="寶石數量", font=("Microsoft JhengHei UI", 11, "bold"), bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).grid(row=2, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_gem = ttk.Entry(resource_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.entry_gem.grid(row=2, column=1, padx=PADDING, pady=PADDING, sticky="ew")
        # 設定預設值
        self.entry_gem.insert(0, self.format_number(1000))

        # 樂透數量設定
        tk.Label(resource_frame, text="樂透數量", font=("Microsoft JhengHei UI", 11, "bold"), bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).grid(row=3, column=0, sticky="w", padx=PADDING, pady=PADDING)
        self.entry_lottery = ttk.Entry(resource_frame, width=20, font=("Microsoft JhengHei UI", 10))
        self.entry_lottery.grid(row=3, column=1, padx=PADDING, pady=PADDING, sticky="ew")
        # 設定預設值
        self.entry_lottery.insert(0, self.format_number(100))

        # 按鈕容器 - 垂直居中
        button_container = tk.Frame(resource_frame, bg=self.theme_manager.get_color("surface"))
        button_container.grid(row=0, column=2, rowspan=4, padx=PADDING, pady=PADDING, sticky="ns")

        # 更新資源按鈕
        self.btn_update = ModernButton(
            button_container,
            text="更新資源",
            icon=IconManager.get('update'),
            button_type="primary"
        )
        self.btn_update.pack(side=tk.TOP, pady=(0, PADDING))

        # 清除參數按鈕
        self.btn_clear = ModernButton(
            button_container,
            text="清除參數",
            icon=IconManager.get('delete'),
            button_type="danger"
        )
        self.btn_clear.pack(side=tk.TOP)

        # 批次處理區域 - 使用卡片式設計
        batch_card = CardFrame(left_frame, title="批次處理", icon=IconManager.get('import'))
        batch_card.pack(fill=tk.X, padx=PADDING, pady=PADDING)
        batch_frame = batch_card.get_content_frame()

        # 主要操作框架 - 水平排列所有元素
        main_batch_frame = tk.Frame(batch_frame, bg=self.theme_manager.get_color("surface"))
        main_batch_frame.pack(fill=tk.X, padx=PADDING, pady=PADDING)

        # 資料庫來源選擇區域
        db_source_container = tk.Frame(main_batch_frame, bg=self.theme_manager.get_color("surface"))
        db_source_container.pack(side=tk.LEFT, padx=(0, PADDING*2))

        # 資料庫來源標籤
        tk.Label(
            db_source_container,
            text="資料庫來源",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        ).pack(side=tk.TOP, anchor="w", pady=(0, 2))

        # 資料庫來源下拉選單
        self.batch_db_source = ttk.Combobox(
            db_source_container,
            values=["OCMS", "OCIntegrator"],
            width=15,
            state="readonly",
            font=("Microsoft JhengHei UI", 10)
        )
        self.batch_db_source.set("OCMS")  # 預設值
        self.batch_db_source.pack(side=tk.TOP, fill=tk.X)

        # 批次匯入按鈕
        self.btn_import = ModernButton(
            main_batch_frame,
            text="批次匯入",
            icon=IconManager.get('import'),
            button_type="success"
        )
        self.btn_import.pack(side=tk.LEFT, padx=PADDING)

        # 批次更新按鈕
        self.btn_batch_update = ModernButton(
            main_batch_frame,
            text="批次更新",
            icon=IconManager.get('update'),
            button_type="primary"
        )
        self.btn_batch_update.pack(side=tk.LEFT, padx=PADDING)

        # 批次狀態區域
        batch_status_frame = tk.Frame(batch_frame, bg=self.theme_manager.get_color("surface"))
        batch_status_frame.pack(fill=tk.X, padx=PADDING, pady=PADDING)

        # 批次狀態標籤
        self.batch_status_label = tk.Label(
            batch_status_frame,
            text="尚未匯入帳號",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        )
        self.batch_status_label.pack(side=tk.LEFT, anchor="w")

        # 批次控制按鈕框架
        self.batch_control_frame = tk.Frame(batch_status_frame, bg=self.theme_manager.get_color("surface"))
        self.batch_control_frame.pack(side=tk.RIGHT, padx=(PADDING, 0))

        # 暫停按鈕 - 使用與批次更新按鈕相同的大小和樣式，移除圖標
        self.btn_pause = ModernButton(
            self.batch_control_frame,
            text="暫停",
            button_type="warning",
            width=10  # 與批次更新按鈕相同的寬度
        )

        # 恢復按鈕 - 使用與批次更新按鈕相同的大小和樣式，移除圖標
        self.btn_resume = ModernButton(
            self.batch_control_frame,
            text="恢復",
            button_type="success",
            width=10  # 與批次更新按鈕相同的寬度
        )

        # 取消按鈕 - 使用與批次更新按鈕相同的大小和樣式，移除圖標
        self.btn_cancel = ModernButton(
            self.batch_control_frame,
            text="取消",
            button_type="danger",
            width=10  # 與批次更新按鈕相同的寬度
        )

        # 默認隱藏批次控制按鈕
        self.hide_batch_control_buttons()

        # 進度條框架
        progress_frame = tk.Frame(batch_frame, bg=self.theme_manager.get_color("surface"))
        progress_frame.pack(fill=tk.X, padx=PADDING, pady=(0, PADDING))

        # 進度條
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            orient="horizontal",
            length=100,
            mode="determinate"
        )
        self.progress_bar.pack(fill=tk.X)

        # 操作說明與日誌整合在右側面板

        # 日誌區域 - 使用卡片式設計，整合操作說明
        log_card = CardFrame(right_frame, title="操作日誌與說明", icon=IconManager.get('comment'))
        log_card.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)
        log_frame = log_card.get_content_frame()

        self.debug_text = ScrolledText(log_frame, width=40, height=20, font=("Microsoft JhengHei UI", 10))
        self.debug_text.pack(fill=tk.BOTH, expand=True)

        # 初始日誌內容，包含操作說明
        self.debug_text.insert(tk.END, "👋 歡迎使用資源調整工具\n\n")
        self.debug_text.insert(tk.END, "📌 操作說明：\n")
        self.debug_text.insert(tk.END, "1. 會員查詢：\n")
        self.debug_text.insert(tk.END, "   - 選擇資料庫來源\n")
        self.debug_text.insert(tk.END, "   - 輸入平台帳號或 VP Member ID\n")
        self.debug_text.insert(tk.END, "   - 點擊「查詢會員」按鈕查詢會員資訊\n\n")
        self.debug_text.insert(tk.END, "2. 資源設定：\n")
        self.debug_text.insert(tk.END, "   - 設定要更新的金幣、VIP等級、寶石或樂透券數量\n")
        self.debug_text.insert(tk.END, "   - 點擊「更新資源」按鈕更新會員資源\n\n")
        self.debug_text.insert(tk.END, "3. 批次處理：\n")
        self.debug_text.insert(tk.END, "   - 選擇資料庫來源\n")
        self.debug_text.insert(tk.END, "   - 點擊「批次匯入」選擇帳號檔案\n")
        self.debug_text.insert(tk.END, "   - 設定資源數量後點擊「批次更新」\n\n")
        self.debug_text.insert(tk.END, "⚠️ 請先查詢會員資訊，再進行資源更新操作\n")
        self.debug_text.insert(tk.END, "📝 操作流程：1.查詢會員 → 2.設定資源 → 3.更新資源\n\n")
        self.debug_text.insert(tk.END, "--- 操作日誌 ---\n")

        # 清除日誌按鈕
        self.btn_clear_log = ModernButton(
            log_frame,
            text="清除日誌",
            icon=IconManager.get('delete'),
            button_type="secondary",
            command=lambda: self.debug_text.delete(1.0, tk.END)
        )
        self.btn_clear_log.pack(side=tk.RIGHT, padx=PADDING, pady=PADDING)

        # 設定按鈕樣式
        self._setup_button_styles()

        # 啟動查詢按鈕閃爍效果
        self._start_button_blink()

    def update_resource(self):
        """更新資源"""
        # 這個方法已由控制器接管，不需要實現
        pass

    def clear_resource_params(self):
        """清除資源參數"""
        # 清除所有資源欄位
        self.entry_coin.delete(0, tk.END)
        self.entry_vip.delete(0, tk.END)
        self.entry_gem.delete(0, tk.END)
        self.entry_lottery.delete(0, tk.END)

        # 記錄日誌
        self.log("✅ 已清除資源參數")

    def import_accounts(self):
        """匯入帳號"""
        # 這個方法將由控制器實現
        pass

    def batch_update_resource(self):
        """批次更新資源"""
        # 這個方法將由控制器實現
        pass

    def update_batch_status(self, status_text):
        """更新批次狀態標籤"""
        def _update_status():
            self.batch_status_label.config(text=status_text)
            self.update_idletasks()

        # 使用隊列處理 UI 更新
        self._add_ui_task(_update_status)

    def update_batch_progress(self, percentage):
        """更新批次進度條

        Args:
            percentage: 進度百分比 (0-100)
        """
        def _update_progress():
            self.progress_bar["value"] = percentage
            self.update_idletasks()

        # 使用隊列處理 UI 更新
        self._add_ui_task(_update_progress)

    def show_batch_control_buttons(self, pause_command=None, resume_command=None, cancel_command=None):
        """顯示批次控制按鈕

        Args:
            pause_command: 暫停命令
            resume_command: 恢復命令
            cancel_command: 取消命令
        """
        # 設置命令
        if pause_command:
            self.btn_pause.config(command=pause_command)
        if resume_command:
            self.btn_resume.config(command=resume_command)
        if cancel_command:
            self.btn_cancel.config(command=cancel_command)

        # 顯示按鈕
        self.btn_pause.pack(side=tk.LEFT, padx=(0, PADDING/2))
        self.btn_resume.pack(side=tk.LEFT, padx=(0, PADDING/2))
        self.btn_cancel.pack(side=tk.LEFT)

        # 初始狀態：顯示暫停和取消按鈕，隱藏恢復按鈕
        self.btn_pause.pack(side=tk.LEFT, padx=(0, PADDING/2))
        self.btn_resume.pack_forget()
        self.btn_cancel.pack(side=tk.LEFT)

    def hide_batch_control_buttons(self):
        """隱藏批次控制按鈕"""
        self.btn_pause.pack_forget()
        self.btn_resume.pack_forget()
        self.btn_cancel.pack_forget()

    def toggle_batch_pause_resume(self, is_paused):
        """切換暫停/恢復按鈕

        Args:
            is_paused: 是否已暫停
        """
        if is_paused:
            # 顯示恢復按鈕，隱藏暫停按鈕
            self.btn_pause.pack_forget()
            self.btn_resume.pack(side=tk.LEFT, padx=(0, PADDING/2))
        else:
            # 顯示暫停按鈕，隱藏恢復按鈕
            self.btn_resume.pack_forget()
            self.btn_pause.pack(side=tk.LEFT, padx=(0, PADDING/2))

    def clear_member_info(self):
        """清除會員資訊"""
        self.entry_account.delete(0, tk.END)
        self.entry_member_id.delete(0, tk.END)
        self.log("✅ 已清除會員資訊")

    def log(self, message):
        """記錄日誌"""
        def _update_log():
            self.debug_text.insert(tk.END, f"{message}\n")
            self.debug_text.see(tk.END)
            self.update_idletasks()

        # 使用隊列處理 UI 更新
        self._add_ui_task(_update_log)

    def log_batch(self, messages):
        """批量記錄日誌，提高效率"""
        if not messages:
            return

        def _update_log_batch():
            # 禁用文本框更新以提高效率
            self.debug_text.config(state=tk.DISABLED)

            try:
                # 一次性添加所有消息
                text = "\n".join(messages) + "\n"
                self.debug_text.config(state=tk.NORMAL)
                self.debug_text.insert(tk.END, text)
                self.debug_text.see(tk.END)
            finally:
                # 確保文本框恢復正常狀態
                self.debug_text.config(state=tk.NORMAL)

            # 只更新一次UI
            self.update_idletasks()

        # 使用隊列處理 UI 更新
        self._add_ui_task(_update_log_batch)

    def add_log(self, message):
        """添加日誌（為了兼容性）"""
        self.log(message)

    def show_error(self, title, message):
        """顯示錯誤訊息"""
        def _show_error_dialog():
            messagebox.showerror(title, message)
            self.log(f"❌ 錯誤: {message}")

        # 使用隊列處理 UI 更新
        self._add_ui_task(_show_error_dialog)

    def show_info(self, title, message):
        """顯示信息訊息"""
        def _show_info_dialog():
            messagebox.showinfo(title, message)
            self.log(f"ℹ️ 信息: {message}")

        # 使用隊列處理 UI 更新
        self._add_ui_task(_show_info_dialog)

    def show_success(self, title, message):
        """顯示成功訊息"""
        def _show_success_dialog():
            messagebox.showinfo(title, message)
            self.log(f"✅ 成功: {message}")

        # 使用隊列處理 UI 更新
        self._add_ui_task(_show_success_dialog)

    def show_confirm(self, title, message):
        """顯示確認對話框"""
        from widgets.dialog import Dialog

        # 對於需要返回值的對話框，我們需要使用一個事件來同步
        if threading.current_thread() is threading.main_thread():
            return Dialog.show_confirm(self, title, message)
        else:
            # 創建一個事件和結果變數
            event = threading.Event()
            result = [None]

            def _show_confirm_dialog():
                result[0] = Dialog.show_confirm(self, title, message)
                event.set()  # 設置事件，表示對話框已完成

            # 使用隊列處理 UI 更新
            self._add_ui_task(_show_confirm_dialog)

            # 等待對話框完成
            event.wait(timeout=30)  # 設置超時時間，避免無限等待
            return result[0]

    def format_number(self, number):
        """格式化數字，加上千分位分隔符

        Args:
            number: 要格式化的數字

        Returns:
            str: 格式化後的數字字串
        """
        try:
            return locale.format_string("%d", int(number), grouping=True)
        except (ValueError, TypeError):
            return str(number)

    def _setup_button_styles(self):
        """設定按鈕樣式"""
        style = ttk.Style()

        # 主要按鈕樣式
        style.configure("Accent.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 成功按鈕樣式
        style.configure("Success.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 信息按鈕樣式
        style.configure("Info.TButton",
                        background="#2196F3",
                        foreground="white",
                        padding=5)

        # 次要按鈕樣式
        style.configure("Secondary.TButton",
                        background="#9E9E9E",
                        foreground="white",
                        padding=5)

        # 危險按鈕樣式
        style.configure("Danger.TButton",
                        background="#F44336",
                        foreground="white",
                        padding=5)

    def _start_button_blink(self):
        """啟動按鈕閃爍效果"""
        # 檢查視窗是否仍然存在
        try:
            if not self.winfo_exists():
                return
        except Exception:
            return

        # 檢查是否已經查詢過會員或已輸入會員資訊
        try:
            account = self.entry_account.get().strip()
            member_id = self.entry_member_id.get().strip()

            # 如果已經輸入了會員資訊，停止閃爍
            if account or member_id:
                self._stop_button_blink()
                return

            # 切換閃爍狀態
            self.blink_state = not self.blink_state

            if self.blink_state:
                # 閃爍：亮 -> 暗
                self.btn_query.config(bg="#3498db")  # 原始藍色
            else:
                # 閃爍：暗 -> 亮
                self.btn_query.config(bg="#2ecc71")  # 綠色

            # 設定下一次閃爍
            self.blink_job = self.after(800, self._start_button_blink)  # 每 800 毫秒閃爍一次
        except Exception:
            # 如果發生異常，停止閃爍
            self._stop_button_blink()

    def _stop_button_blink(self):
        """停止按鈕閃爍效果"""
        try:
            if self.blink_job:
                self.after_cancel(self.blink_job)
                self.blink_job = None
                # 恢復按鈕原始樣式
                style = self.theme_manager.get_button_style("primary")
                self.btn_query.config(bg=style.get("bg", "#007bff"))
        except Exception:
            # 忽略異常
            self.blink_job = None

    def _start_ui_processor(self):
        """啟動 UI 更新處理器"""
        if not self.is_ui_processor_running:
            self.is_ui_processor_running = True
            self._process_ui_queue()

    def _process_ui_queue(self):
        """處理 UI 更新隊列"""
        # 檢查視窗是否仍然存在
        try:
            if not self.winfo_exists():
                self.is_ui_processor_running = False
                return
        except Exception:
            self.is_ui_processor_running = False
            return

        try:
            # 嘗試從隊列中獲取任務
            while not self.ui_queue.empty():
                task = self.ui_queue.get(block=False)
                if task:
                    func, args, kwargs = task
                    try:
                        func(*args, **kwargs)
                    except Exception:
                        # 忽略任務執行過程中的異常
                        pass
                self.ui_queue.task_done()
        except queue.Empty:
            pass
        except Exception:
            # 忽略其他異常
            pass
        finally:
            # 安排下一次處理
            if self.is_ui_processor_running:
                try:
                    self.after(100, self._process_ui_queue)
                except Exception:
                    # 如果無法安排下一次處理，停止處理器
                    self.is_ui_processor_running = False

    def _add_ui_task(self, func, *args, **kwargs):
        """添加 UI 更新任務到隊列

        Args:
            func: 要執行的函數
            *args: 函數參數
            **kwargs: 函數關鍵字參數
        """
        self.ui_queue.put((func, args, kwargs))
