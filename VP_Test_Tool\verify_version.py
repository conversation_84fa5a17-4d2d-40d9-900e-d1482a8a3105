#!/usr/bin/env python3
"""
版本驗證腳本

此腳本用於驗證所有相關文件中的版本號是否已正確更新到目標版本。
"""

import os
import re
import sys

def check_version_in_file(file_path, expected_version, pattern):
    """檢查文件中的版本號"""
    try:
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        match = re.search(pattern, content)
        if match:
            found_version = match.group(1)
            if found_version == expected_version:
                return True, f"✅ {file_path}: {found_version}"
            else:
                return False, f"❌ {file_path}: 期望 {expected_version}，實際 {found_version}"
        else:
            return False, f"❌ {file_path}: 找不到版本號"
    
    except Exception as e:
        return False, f"❌ {file_path}: 讀取失敗 - {e}"

def main():
    """主函數"""
    # 從 version.py 獲取目標版本號
    try:
        from utils.version import VERSION, MAJOR, MINOR, PATCH
        target_version = VERSION
        print(f"目標版本號: {target_version}")
        print("=" * 50)
    except ImportError:
        print("❌ 無法導入版本信息")
        return 1
    
    # 要檢查的文件和對應的版本號模式
    files_to_check = [
        {
            "file": "utils/version.py",
            "pattern": r'VERSION = f"{MAJOR}\.{MINOR}\.{PATCH}"',
            "description": "版本定義文件"
        },
        {
            "file": "setup_py2exe.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "py2exe 打包腳本"
        },
        {
            "file": "setup_cx_freeze.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "cx_Freeze 打包腳本"
        },
        {
            "file": "setup_cx_freeze_optimized.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "cx_Freeze 優化版打包腳本"
        },
        {
            "file": "setup_cx_freeze_full.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "cx_Freeze 完整版打包腳本"
        },
        {
            "file": "setup_cx_freeze_pandas.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "cx_Freeze pandas 版打包腳本"
        },
        {
            "file": "setup_cx_freeze_simple.py",
            "pattern": r'APP_VERSION = "([^"]+)"',
            "description": "cx_Freeze 簡化版打包腳本"
        }
    ]
    
    # 檢查特殊文件
    special_files = [
        {
            "file": "build_exe_nuitka.py",
            "pattern": r'--windows-file-version=([0-9.]+)',
            "expected": f"{MAJOR}.{MINOR}.{PATCH}.0",
            "description": "Nuitka 打包腳本 (文件版本)"
        },
        {
            "file": "build_exe_nuitka.py",
            "pattern": r'--windows-product-version=([0-9.]+)',
            "expected": f"{MAJOR}.{MINOR}.{PATCH}.0",
            "description": "Nuitka 打包腳本 (產品版本)"
        }
    ]
    
    all_passed = True
    
    # 檢查一般文件
    print("檢查一般版本號文件:")
    for file_info in files_to_check:
        file_path = file_info["file"]
        pattern = file_info["pattern"]
        description = file_info["description"]
        
        if file_path == "utils/version.py":
            # 特殊處理 version.py，直接檢查導入的版本號
            if target_version == f"{MAJOR}.{MINOR}.{PATCH}":
                print(f"✅ {description}: {target_version}")
            else:
                print(f"❌ {description}: 版本號不一致")
                all_passed = False
        else:
            passed, message = check_version_in_file(file_path, target_version, pattern)
            print(f"  {message}")
            if not passed:
                all_passed = False
    
    print("\n檢查特殊版本號文件:")
    # 檢查特殊文件
    for file_info in special_files:
        file_path = file_info["file"]
        pattern = file_info["pattern"]
        expected = file_info["expected"]
        description = file_info["description"]
        
        passed, message = check_version_in_file(file_path, expected, pattern)
        print(f"  {message}")
        if not passed:
            all_passed = False
    
    # 檢查 CHANGELOG.md
    print("\n檢查更新日誌:")
    changelog_pattern = r'## V([0-9.]+) \([0-9-]+\)'
    passed, message = check_version_in_file("CHANGELOG.md", target_version, changelog_pattern)
    print(f"  {message}")
    if not passed:
        all_passed = False
    
    # 總結
    print("\n" + "=" * 50)
    if all_passed:
        print(f"🎉 所有文件的版本號都已正確更新到 {target_version}！")
        
        # 顯示版本信息摘要
        print(f"\n版本信息摘要:")
        print(f"  版本號: {target_version}")
        print(f"  主版本: {MAJOR}")
        print(f"  次版本: {MINOR}")
        print(f"  修訂版本: {PATCH}")
        
        try:
            from utils.version import RELEASE_DATE, APP_TITLE
            print(f"  發布日期: {RELEASE_DATE}")
            print(f"  應用標題: {APP_TITLE}")
        except ImportError:
            pass
        
        return 0
    else:
        print(f"⚠️ 部分文件的版本號更新失敗，請檢查上述錯誤。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
