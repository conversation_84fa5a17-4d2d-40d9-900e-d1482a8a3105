#!/usr/bin/env python3
"""
打包測試腳本

此腳本用於測試 cx_Freeze 打包的 VP Test Tool 是否正常工作。
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_build_integrity():
    """測試打包文件的完整性"""
    print("🔍 檢查打包文件完整性...")
    
    build_dir = Path("dist/cx_freeze")
    if not build_dir.exists():
        print("❌ 打包目錄不存在: dist/cx_freeze")
        return False
    
    # 檢查主要文件
    required_files = [
        "VP_Test_Tool.exe",
        "python311.dll",
        "config.json",
        "CHANGELOG.md",
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = build_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
        else:
            print(f"✅ {file_name}: {file_path.stat().st_size:,} bytes")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 檢查目錄
    required_dirs = [
        "lib",
        "assets",
        "share",
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = build_dir / dir_name
        if not dir_path.exists():
            missing_dirs.append(dir_name)
        else:
            file_count = len(list(dir_path.rglob("*")))
            print(f"✅ {dir_name}/: {file_count} 個文件")
    
    if missing_dirs:
        print(f"❌ 缺少目錄: {', '.join(missing_dirs)}")
        return False
    
    print("✅ 打包文件完整性檢查通過")
    return True

def test_executable():
    """測試可執行文件"""
    print("\n🚀 測試可執行文件...")
    
    exe_path = Path("dist/cx_freeze/VP_Test_Tool.exe")
    if not exe_path.exists():
        print("❌ 可執行文件不存在")
        return False
    
    print(f"✅ 可執行文件存在: {exe_path.stat().st_size:,} bytes")
    
    # 檢查文件是否可執行
    if not os.access(exe_path, os.X_OK):
        print("❌ 文件沒有執行權限")
        return False
    
    print("✅ 文件具有執行權限")
    return True

def test_dependencies():
    """測試依賴庫"""
    print("\n📚 檢查依賴庫...")
    
    lib_dir = Path("dist/cx_freeze/lib")
    if not lib_dir.exists():
        print("❌ lib 目錄不存在")
        return False
    
    # 檢查重要的依賴庫
    required_libs = [
        "tkinter",
        "PIL",
        "requests",
        "pandas",
        "numpy",
        "openpyxl",
        "utils",
        "views",
        "controllers",
        "widgets",
        "models",
    ]
    
    missing_libs = []
    for lib_name in required_libs:
        lib_path = lib_dir / lib_name
        if not lib_path.exists():
            missing_libs.append(lib_name)
        else:
            if lib_path.is_dir():
                file_count = len(list(lib_path.rglob("*")))
                print(f"✅ {lib_name}: {file_count} 個文件")
            else:
                print(f"✅ {lib_name}: {lib_path.stat().st_size:,} bytes")
    
    if missing_libs:
        print(f"❌ 缺少依賴庫: {', '.join(missing_libs)}")
        return False
    
    print("✅ 依賴庫檢查通過")
    return True

def test_assets():
    """測試資源文件"""
    print("\n🎨 檢查資源文件...")
    
    assets_dir = Path("dist/cx_freeze/assets")
    if not assets_dir.exists():
        print("❌ assets 目錄不存在")
        return False
    
    # 檢查重要的資源文件
    required_assets = [
        "icons/vp_test_tool.ico",
        "app_icon.ico",
        "app_icon.png",
    ]
    
    missing_assets = []
    for asset_name in required_assets:
        asset_path = assets_dir / asset_name
        if not asset_path.exists():
            missing_assets.append(asset_name)
        else:
            print(f"✅ {asset_name}: {asset_path.stat().st_size:,} bytes")
    
    if missing_assets:
        print(f"❌ 缺少資源文件: {', '.join(missing_assets)}")
        return False
    
    print("✅ 資源文件檢查通過")
    return True

def get_build_statistics():
    """獲取打包統計信息"""
    print("\n📊 打包統計信息...")
    
    build_dir = Path("dist/cx_freeze")
    if not build_dir.exists():
        print("❌ 打包目錄不存在")
        return
    
    # 統計文件數量和大小
    total_files = 0
    total_size = 0
    
    for file_path in build_dir.rglob("*"):
        if file_path.is_file():
            total_files += 1
            total_size += file_path.stat().st_size
    
    print(f"📁 總文件數: {total_files:,}")
    print(f"💾 總大小: {total_size / (1024*1024):.1f} MB")
    
    # 主要文件大小
    exe_path = build_dir / "VP_Test_Tool.exe"
    if exe_path.exists():
        exe_size = exe_path.stat().st_size
        print(f"🎯 主程式大小: {exe_size / 1024:.1f} KB")
    
    # Python DLL 大小
    python_dll = build_dir / "python311.dll"
    if python_dll.exists():
        dll_size = python_dll.stat().st_size
        print(f"🐍 Python DLL: {dll_size / (1024*1024):.1f} MB")

def main():
    """主函數"""
    print("=" * 60)
    print("🧪 VP Test Tool V2.6.1 打包測試")
    print("=" * 60)
    
    # 檢查當前目錄
    if not Path("main.py").exists():
        print("❌ 請在 VP Test Tool 根目錄下運行此腳本")
        return 1
    
    # 執行測試
    tests = [
        ("打包文件完整性", test_build_integrity),
        ("可執行文件", test_executable),
        ("依賴庫", test_dependencies),
        ("資源文件", test_assets),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試出錯: {e}")
    
    # 顯示統計信息
    get_build_statistics()
    
    # 總結
    print("\n" + "=" * 60)
    print(f"📋 測試結果: {passed_tests}/{total_tests} 通過")
    
    if passed_tests == total_tests:
        print("🎉 所有測試通過！打包成功。")
        print("\n📦 可執行文件位置: dist/cx_freeze/VP_Test_Tool.exe")
        print("💡 建議: 可以將整個 dist/cx_freeze 目錄分發給用戶")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查打包配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
