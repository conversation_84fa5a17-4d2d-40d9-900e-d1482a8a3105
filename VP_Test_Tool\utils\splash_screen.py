"""啟動畫面模組

提供程式啟動時的啟動畫面，顯示載入進度和狀態。
"""
import tkinter as tk
from tkinter import ttk
import time
from typing import Callable, List
from .theme import ThemeManager
from .icon_manager import IconManager
from .version import VERSION, APP_TITLE

class SplashScreen:
    """啟動畫面類

    在程式初始化過程中顯示啟動畫面，並在主線程中執行初始化操作。

    Args:
        init_steps: 初始化步驟函數列表，每個函數將在主線程中依次執行
        width: 啟動畫面寬度
        height: 啟動畫面高度
        min_display_time: 最小顯示時間（秒）
    """

    def __init__(
        self,
        init_steps: List[Callable],
        width: int = 500,
        height: int = 300,
        min_display_time: float = 1.5
    ):
        self.init_steps = init_steps
        self.width = width
        self.height = height
        self.min_display_time = min_display_time

        # 初始化變數
        self.root = None
        self.start_time = 0
        self.status_label = None
        self.progress_bar = None
        self.progress_value = 0
        self.progress_max = 100
        self.theme_manager = ThemeManager()
        self.current_step = 0
        self.init_results = []
        self.init_error = None

    def show(self):
        """顯示啟動畫面並開始初始化"""
        # 記錄開始時間
        self.start_time = time.time()

        try:
            # 創建根視窗
            self.root = tk.Tk()
            self.root.overrideredirect(True)  # 移除視窗邊框
            self.root.geometry(f"{self.width}x{self.height}")
            self.root.configure(bg=self.theme_manager.get_color("background"))

            # 置中視窗
            self._center_window()

            # 創建啟動畫面內容
            self._create_content()

            # 註冊鍵盤中斷處理函數
            self.root.bind_all("<Control-c>", self._handle_keyboard_interrupt)
            self.root.bind_all("<Control-C>", self._handle_keyboard_interrupt)
            self.root.bind_all("<Escape>", self._handle_keyboard_interrupt)

            # 啟動初始化過程
            self.root.after(100, self._run_next_step)

            # 啟動主循環
            self.root.mainloop()

            # 返回初始化結果
            if self.init_error:
                raise self.init_error
            return self.init_results

        except KeyboardInterrupt:
            # 處理鍵盤中斷
            print("\n程式已被使用者中斷")
            if self.root:
                self._safe_destroy()
            return []

    def _center_window(self):
        """置中視窗"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _create_content(self):
        """創建啟動畫面內容"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=self.theme_manager.get_color("background"))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 標題
        title_frame = tk.Frame(main_frame, bg=self.theme_manager.get_color("background"))
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text=APP_TITLE,
            font=("Microsoft JhengHei UI", 18, "bold"),
            fg=self.theme_manager.get_color("primary"),
            bg=self.theme_manager.get_color("background")
        )
        title_label.pack(side=tk.LEFT)

        version_label = tk.Label(
            title_frame,
            text=f"v{VERSION}",
            font=("Microsoft JhengHei UI", 12),
            fg=self.theme_manager.get_color("text_secondary"),
            bg=self.theme_manager.get_color("background")
        )
        version_label.pack(side=tk.LEFT, padx=(10, 0), pady=(5, 0))

        # 載入圖示
        loading_frame = tk.Frame(main_frame, bg=self.theme_manager.get_color("background"))
        loading_frame.pack(fill=tk.BOTH, expand=True)

        loading_label = tk.Label(
            loading_frame,
            text=IconManager.get("loading"),
            font=("Microsoft JhengHei UI", 48),
            fg=self.theme_manager.get_color("primary"),
            bg=self.theme_manager.get_color("background")
        )
        loading_label.pack(pady=20)

        # 狀態標籤
        self.status_label = tk.Label(
            main_frame,
            text="正在初始化應用程式...",
            font=("Microsoft JhengHei UI", 10),
            fg=self.theme_manager.get_color("text_primary"),
            bg=self.theme_manager.get_color("background"),
            anchor="w"
        )
        self.status_label.pack(fill=tk.X, pady=(0, 10))

        # 進度條
        self.progress_bar = ttk.Progressbar(
            main_frame,
            orient="horizontal",
            length=self.width - 40,
            mode="determinate",
            maximum=self.progress_max,
            value=0
        )
        self.progress_bar.pack(fill=tk.X)

        # 版權信息
        copyright_label = tk.Label(
            main_frame,
            text="© VP Test Tool Team",
            font=("Microsoft JhengHei UI", 8),
            fg=self.theme_manager.get_color("text_secondary"),
            bg=self.theme_manager.get_color("background")
        )
        copyright_label.pack(side=tk.RIGHT, pady=(10, 0))

    def _run_next_step(self):
        """執行下一個初始化步驟"""
        # 檢查是否所有步驟都已完成
        if self.current_step >= len(self.init_steps):
            self._finish_initialization()
            return

        # 更新進度
        step_progress = self.progress_max / len(self.init_steps)
        self.progress_value = self.current_step * step_progress
        self.progress_bar["value"] = self.progress_value

        # 更新狀態
        self.status_label["text"] = f"正在初始化... ({self.current_step + 1}/{len(self.init_steps)})"

        # 執行當前步驟
        try:
            step_func = self.init_steps[self.current_step]
            result = step_func()
            self.init_results.append(result)
            self.current_step += 1

            # 安排下一個步驟
            self.root.after(50, self._run_next_step)
        except Exception as e:
            self.init_error = e
            self.status_label["text"] = f"初始化失敗: {e}"
            self.status_label["fg"] = "red"

            # 等待一段時間後關閉
            self.root.after(2000, lambda: self._safe_destroy())

    def _finish_initialization(self):
        """完成初始化過程"""
        # 更新進度為 100%
        self.progress_bar["value"] = self.progress_max
        self.status_label["text"] = "初始化完成，正在啟動..."

        # 檢查是否達到最小顯示時間
        elapsed_time = time.time() - self.start_time
        if elapsed_time >= self.min_display_time:
            self._safe_destroy()
        else:
            # 如果未達到最小顯示時間，設置定時器
            remaining_time = int((self.min_display_time - elapsed_time) * 1000)
            self.root.after(remaining_time, lambda: self._safe_destroy())

    def _safe_destroy(self):
        """安全地銷毀視窗"""
        try:
            if self.root and self.root.winfo_exists():
                self.root.destroy()
        except Exception as e:
            print(f"安全銷毀視窗時發生錯誤: {e}")
            # 如果銷毀失敗，嘗試強制銷毀
            try:
                if self.root:
                    self.root.quit()
            except:
                pass

    def _handle_keyboard_interrupt(self, event=None):
        """處理鍵盤中斷事件"""
        # 更新狀態
        try:
            self.status_label["text"] = "使用者中斷，正在關閉..."
            self.status_label["fg"] = "red"

            # 立即關閉視窗
            self.root.after(500, lambda: self._safe_destroy())
        except Exception:
            # 如果更新狀態失敗，直接嘗試關閉
            self._safe_destroy()

        # 返回 "break" 以阻止事件繼續傳播
        return "break"
