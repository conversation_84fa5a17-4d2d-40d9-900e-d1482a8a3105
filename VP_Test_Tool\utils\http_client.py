"""HTTP 客戶端"""
import json
import logging
from typing import Dict, Any, Optional, Tuple
import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry
import urllib3
import tkinter as tk
from tkinter import messagebox
from .constants import API_URLS
from .api_error_handler import ApiErrorHandler
from .exceptions import (
    APIConnectionError, APITimeoutError, APIResponseError,
    ResourceUpdateError, ResourceQueryError, ResourceNotFoundError
)

# 抑制 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)

class HttpClient:
    """HTTP 客戶端類
    
    可以使用 with 語句確保資源正確關閉：
    
    ```python
    with HttpClient() as client:
        response = client.get(url)
    ```
    """

    def __init__(self, base_url: str = "", timeout: int = 10, show_error_dialog: bool = True):
        self.base_url = base_url
        self.timeout = timeout
        self.show_error_dialog = show_error_dialog  # 是否顯示錯誤對話框
        self.session = self._create_session()
        # 快取常用的 URL 回應
        self._cache = {}
        logger.info(f"HTTP 客戶端初始化完成，超時設定為 {timeout} 秒")
        
    def __enter__(self):
        """支持 with 語句"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出 with 語句時關閉 session"""
        self.close()
        return False  # 不抑制異常
        
    def close(self):
        """關閉 session"""
        if hasattr(self, 'session') and self.session:
            try:
                self.session.close()
                logger.debug("HTTP session 已關閉")
            except Exception as e:
                logger.warning(f"關閉 HTTP session 時發生錯誤: {e}")
                
    def __del__(self):
        """析構函數，確保資源被釋放"""
        self.close()

    def _show_error_message(self, title: str, message: str):
        """顯示錯誤訊息對話框"""
        if not self.show_error_dialog:
            return

        try:
            # 創建一個臨時的 Tk 根窗口
            root = tk.Tk()
            root.withdraw()  # 隱藏主窗口

            # 顯示錯誤訊息
            messagebox.showerror(title, message)

            # 銷毀臨時窗口
            root.destroy()
        except Exception as e:
            logger.error(f"顯示錯誤訊息對話框失敗: {e}")

    def _create_session(self) -> requests.Session:
        """建立 Session 物件"""
        session = requests.Session()
        retry_strategy = Retry(
            total=2,  # 減少重試次數
            backoff_factor=0.3,  # 減少重試間隔
            status_forcelist=[500, 502, 503, 504],
            connect=2,  # 連接錯誤時的重試次數
            read=2,     # 讀取錯誤時的重試次數
            redirect=2  # 重定向錯誤時的重試次數
        )
        # 增加連接池大小以處理更多並發請求
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=20, pool_maxsize=20)  # 減少連接池大小
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        session.verify = False  # 關閉 SSL 驗證
        logger.info("HTTP Session 已創建，已設定重試策略和連接池")
        return session

    def _get_cache_key(self, url: str, params: Optional[Dict[str, Any]] = None, data: Optional[Dict[str, Any]] = None) -> str:
        """生成快取鍵"""
        key = url
        if params:
            key += str(sorted(params.items()))
        if data:
            key += str(sorted(data.items()))
        return key

    def get(self, url: str, params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """發送 GET 請求"""
        try:
            # 設定預設標頭
            if headers is None:
                headers = {}

            # 檢查快取
            if use_cache:
                cache_key = self._get_cache_key(url, params)
                if cache_key in self._cache:
                    logger.debug(f"使用快取回應: {url}")
                    return self._cache[cache_key]

            # 記錄請求詳情
            logger.debug(f"GET 請求: {url}")
            if params:
                logger.debug(f"請求參數: {params}")

            # 發送請求
            response = self.session.get(
                url,
                params=params,
                headers=headers,
                timeout=self.timeout
            )

            # 檢查回應狀態
            response.raise_for_status()

            # 嘗試解析 JSON 回應
            try:
                result = response.json()
                logger.debug(f"回應: {json.dumps(result, ensure_ascii=False)}")

                # 儲存到快取
                if use_cache:
                    self._cache[cache_key] = result

                return result
            except json.JSONDecodeError:
                logger.warning(f"回應不是有效的 JSON: {response.text}")
                # 如果不是 JSON，返回文本回應
                text_response = {"success": True, "text": response.text}

                # 儲存到快取
                if use_cache:
                    self._cache[cache_key] = text_response

                return text_response

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)
            api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}

    def post(self, url: str, data: Optional[Dict[str, Any]] = None,
             json_data: Optional[Dict[str, Any]] = None,
             headers: Optional[Dict[str, str]] = None,
             use_cache: bool = False) -> Optional[Dict[str, Any]]:
        """發送 POST 請求"""
        try:
            # 設定預設標頭
            if headers is None:
                headers = {}
            if "Content-Type" not in headers:
                headers["Content-Type"] = "application/json"

            # 檢查快取 (只對於讀取操作使用快取，寫入操作通常不使用)
            if use_cache:
                cache_key = self._get_cache_key(url, None, data or json_data)
                if cache_key in self._cache:
                    logger.debug(f"使用快取回應: {url}")
                    return self._cache[cache_key]

            # 記錄請求詳情
            logger.debug(f"POST 請求: {url}")
            if json_data:
                logger.debug(f"請求資料: {json.dumps(json_data, ensure_ascii=False)}")
            elif data:
                logger.debug(f"請求資料: {data}")

            # 發送請求
            response = self.session.post(
                url,
                json=json_data,
                data=data,
                headers=headers,
                timeout=self.timeout
            )

            # 檢查回應狀態
            response.raise_for_status()

            # 嘗試解析 JSON 回應
            try:
                result = response.json()
                logger.debug(f"回應: {json.dumps(result, ensure_ascii=False)}")

                # 儲存到快取 (只對於讀取操作)
                if use_cache:
                    self._cache[cache_key] = result

                return result
            except json.JSONDecodeError:
                logger.warning(f"回應不是有效的 JSON: {response.text}")
                # 如果不是 JSON，返回文本回應
                text_response = {"success": True, "text": response.text}

                # 儲存到快取 (只對於讀取操作)
                if use_cache:
                    self._cache[cache_key] = text_response

                return text_response

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)
            api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            return {"success": False, "error": str(api_error)}
        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}

    def post_with_details(self, url: str, data: Optional[Dict[str, Any]] = None,
                         json_data: Optional[Dict[str, Any]] = None,
                         headers: Optional[Dict[str, str]] = None) -> Tuple[Optional[Dict[str, Any]], int]:
        """發送 POST 請求並返回狀態碼和回應內容

        Returns:
            Tuple[Optional[Dict[str, Any]], int]: 回應內容和狀態碼
        """
        try:
            # 設定預設標頭
            if headers is None:
                headers = {}
            if "Content-Type" not in headers:
                headers["Content-Type"] = "application/json"

            # 記錄請求詳情
            logger.debug(f"POST 請求: {url}")
            if json_data:
                logger.debug(f"請求資料: {json.dumps(json_data, ensure_ascii=False)}")
            elif data:
                logger.debug(f"請求資料: {data}")

            # 發送請求
            response = self.session.post(
                url,
                json=json_data,
                data=data,
                headers=headers,
                timeout=self.timeout
            )

            # 記錄狀態碼
            status_code = response.status_code

            # 檢查回應狀態
            response.raise_for_status()

            # 嘗試解析 JSON 回應
            try:
                result = response.json()
                logger.debug(f"回應: {json.dumps(result, ensure_ascii=False)}")
                return result, status_code
            except json.JSONDecodeError:
                logger.warning(f"回應不是有效的 JSON: {response.text}")
                # 如果不是 JSON，返回文本回應
                return {"success": True, "text": response.text}, status_code

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}, 408  # 返回超時狀態碼
        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 503  # 返回服務不可用狀態碼
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)
            api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            return {"success": False, "error": str(api_error)}, e.response.status_code
        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

    def get_with_details(self, url: str, params: Optional[Dict[str, Any]] = None,
                       headers: Optional[Dict[str, str]] = None) -> Tuple[Optional[Dict[str, Any]], int]:
        """發送 GET 請求並返回狀態碼和回應內容

        Returns:
            Tuple[Optional[Dict[str, Any]], int]: 回應內容和狀態碼
        """
        try:
            # 設定預設標頭
            if headers is None:
                headers = {}

            # 記錄請求詳情
            logger.debug(f"GET 請求: {url}")
            if params:
                logger.debug(f"請求參數: {params}")

            # 發送請求
            response = self.session.get(
                url,
                params=params,
                headers=headers,
                timeout=self.timeout
            )

            # 記錄狀態碼
            status_code = response.status_code

            # 檢查回應狀態
            response.raise_for_status()

            # 嘗試解析 JSON 回應
            try:
                result = response.json()
                logger.debug(f"回應: {json.dumps(result, ensure_ascii=False)}")
                return result, status_code
            except json.JSONDecodeError:
                logger.warning(f"回應不是有效的 JSON: {response.text}")
                # 如果不是 JSON，返回文本回應
                return {"success": True, "text": response.text}, status_code

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}, 408  # 返回超時狀態碼
        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 503  # 返回服務不可用狀態碼
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)
            api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            return {"success": False, "error": str(api_error)}, e.response.status_code
        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

    def clear_cache(self):
        """清除快取"""
        self._cache.clear()
        logger.debug("已清除 HTTP 快取")

    def get_cache_size(self) -> int:
        """獲取快取大小"""
        return len(self._cache)

    def handle_api_error(self, url: str, response: Dict[str, Any], resource_type: str = "") -> Tuple[bool, str]:
        """處理 API 特定錯誤

        Args:
            url: API URL
            response: API 回應
            resource_type: 資源類型 (用於資源更新 API)

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        # 根據 URL 判斷 API 類型
        if url == API_URLS["MYSQL_OPERATOR"] or url == API_URLS["QUERY_MEMBER"]:
            return ApiErrorHandler.handle_mysql_operator_error(response)

        elif url == API_URLS["UPDATE_COIN"]:
            return ApiErrorHandler.handle_resource_update_error(response, "金幣")

        elif url == API_URLS["UPDATE_VIP"]:
            return ApiErrorHandler.handle_resource_update_error(response, "VIP等級")

        elif url == API_URLS["UPDATE_GEM"]:
            return ApiErrorHandler.handle_resource_update_error(response, "寶石")

        elif url == API_URLS["UPDATE_LOTTERY"]:
            return ApiErrorHandler.handle_resource_update_error(response, "樂透券")

        elif url == API_URLS["SET_RNG"]:
            return ApiErrorHandler.handle_rng_set_error(response)

        elif url == API_URLS["ADD_CARDS"]:
            return ApiErrorHandler.handle_add_cards_error(response)

        elif url == API_URLS["QUERY_AGENTS"] or url == API_URLS["QUERY_SUB_AGENTS"]:
            success, error_msg, _ = ApiErrorHandler.handle_agent_query_error(response)
            return success, error_msg

        # 通用錯誤處理
        if "success" in response and not response["success"]:
            error_msg = response.get("error", "未知錯誤")
            return False, error_msg

        if "retStatus" in response:
            status_code = response["retStatus"].get("StatusCode")
            if status_code != 10000:
                status_msg = response["retStatus"].get("StatusMsg", "")
                return False, f"API 錯誤: 錯誤碼 {status_code} ({status_msg})"

        # 如果沒有明確的錯誤，則視為成功
        return True, ""
