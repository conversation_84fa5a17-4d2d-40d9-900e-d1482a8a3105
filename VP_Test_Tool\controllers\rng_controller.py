"""RNG 設置控制器"""
import logging
import tkinter as tk
from tkinter import messagebox, simpledialog
import pandas as pd
import json
import base64
import io
import os
import tempfile
import requests
import gzip
import queue
import threading
from pathlib import Path
from urllib.parse import quote
from typing import Dict, List, Any, Optional, Tuple
from models.member import MemberService
from views.rng_panel import RNGPanel
from utils.constants import COL_RNG_NAME, COL_DESCRIPTION, COL_RNG, API_URLS

logger = logging.getLogger(__name__)

class RNGController:
    """RNG 設置控制器"""
    def __init__(self, rng_panel: RNGPanel, member_service: MemberService):
        self.view = rng_panel
        self.member_service = member_service
        self.game_data_dict = {}  # 儲存遊戲資料
        self.root = rng_panel.master  # 獲取根窗口引用，用於 after 方法
        self.current_member = None  # 儲存當前查詢到的會員
        self._init_bindings()

    def _init_bindings(self):
        """初始化事件綁定"""
        # 會員資訊區域
        self.view.btn_query.config(command=self._handle_query)
        self.view.btn_clear_member.config(command=self._handle_clear_member)

        # 匯入按鈕事件
        self.view.btn_import.config(command=self._handle_import)
        # Git 下載按鈕事件
        self.view.btn_import_gitlab.config(command=self._handle_import_from_gitlab)
        # 更新按鈕事件
        self.view.btn_update.config(command=self._handle_update)
        # 清除按鈕事件
        self.view.btn_clear.config(command=self.view.clear_settings)
        # 遊戲選擇事件
        self.view.cb_game.bind('<<ComboboxSelected>>',
                              lambda e: self._handle_game_selected())
        # RNG 選擇事件
        self.view.cb_rng.bind('<<ComboboxSelected>>',
                             lambda e: self._handle_rng_selected())

    def _handle_import(self):
        """處理匯入事件"""
        try:
            file_paths = self.view.select_files()
            if not file_paths:
                return

            self.game_data_dict = {}
            game_names = []

            for file_path in file_paths:
                try:
                    with open(file_path, 'rb') as f:
                        df = pd.read_excel(f)
                    game_name = file_path.stem  # 使用檔案名稱作為遊戲名稱

                    if COL_RNG_NAME in df.columns:
                        valid_df = df.dropna(subset=[COL_RNG_NAME])
                        if not valid_df.empty:
                            rng_names = valid_df[COL_RNG_NAME].unique().tolist()
                            self.game_data_dict[game_name] = {
                                'dataframe': valid_df.reset_index(drop=True),
                                'rng_names': rng_names
                            }
                            game_names.append(game_name)
                        else:
                            logger.warning(f"遊戲 '{game_name}' 的 '{COL_RNG_NAME}' 欄位皆為空值，已忽略。")
                    else:
                        logger.warning(f"遊戲 '{game_name}' 缺少 '{COL_RNG_NAME}' 欄位，已忽略。")

                except (IOError, pd.errors.EmptyDataError, pd.errors.ParserError) as e:
                    logger.error(f"讀取檔案 {file_path} 失敗: {e}")
                except ValueError as e:
                    logger.error(f"檔案格式錯誤 {file_path}: {e}")
                except Exception as e:
                    logger.error(f"未預期的錯誤 {file_path}: {e}")

            if not self.game_data_dict:
                self.view.show_error("所有檔案都無法成功解析或缺少必要的 RNGName 資訊。")
                return

            # 更新遊戲下拉選單
            game_names = sorted(game_names)
            self.view.cb_game['values'] = game_names

            if game_names:
                first_game = game_names[0]
                self.view.cb_game.set(first_game)
                self._handle_game_selected()

            self.view.show_success(f"已成功匯入 {len(self.game_data_dict)} 個遊戲的 RNG 資料")

        except (IOError, FileNotFoundError) as e:
            logger.error(f"檔案存取錯誤: {e}")
            self.view.show_error(f"檔案存取錯誤: {str(e)}")
        except pd.errors.EmptyDataError as e:
            logger.error(f"Excel 檔案為空: {e}")
            self.view.show_error(f"Excel 檔案為空: {str(e)}")
        except pd.errors.ParserError as e:
            logger.error(f"Excel 解析錯誤: {e}")
            self.view.show_error(f"Excel 解析錯誤: {str(e)}")
        except ValueError as e:
            logger.error(f"資料格式錯誤: {e}")
            self.view.show_error(f"資料格式錯誤: {str(e)}")
        except Exception as e:
            logger.error(f"匯入失敗: {e}")
            self.view.show_error(f"匯入失敗: {str(e)}")

    def _handle_game_selected(self):
        """處理遊戲選擇事件"""
        game_name = self.view.cb_game.get()
        if not game_name or game_name not in self.game_data_dict:
            return

        # 更新 RNG 下拉選單
        rng_names = sorted(self.game_data_dict[game_name].get('rng_names', []))
        self.view.cb_rng['values'] = rng_names

        # 取得遊戲 ID
        game_id = self._get_game_id(game_name)
        if game_id:
            self.view.entry_game_id.delete(0, tk.END)
            self.view.entry_game_id.insert(0, game_id)

        if rng_names:
            self.view.cb_rng.set(rng_names[0])
            self._handle_rng_selected()

    def _handle_rng_selected(self):
        """處理 RNG 選擇事件"""
        game_name = self.view.cb_game.get()
        rng_name = self.view.cb_rng.get()

        if not game_name or not rng_name or game_name not in self.game_data_dict:
            return

        # 取得 RNG 資料
        df = self.game_data_dict[game_name]['dataframe']
        rng_data = df[df[COL_RNG_NAME] == rng_name]

        if not rng_data.empty:
            # 取得描述和 RNG 值
            description = rng_data.iloc[0].get(COL_DESCRIPTION, "")
            rng_value = rng_data.iloc[0].get(COL_RNG, "")

            # 格式化顯示內容，分行顯示 Description 和 RNG
            formatted_text = f"Description:\n{description}\n\nRNG:\n{rng_value}"

            # 更新描述區域
            self.view.update_description(formatted_text)

            # 取得遊戲 ID
            game_id = self._get_game_id(game_name)
            if game_id:
                self.view.entry_game_id.delete(0, tk.END)
                self.view.entry_game_id.insert(0, game_id)

    def _handle_update(self):
        """處理更新事件"""
        try:
            # 取得輸入值
            member_id = self.view.entry_member_id.get().strip()
            game_id = self.view.entry_game_id.get().strip()
            game_name = self.view.cb_game.get()
            rng_name = self.view.cb_rng.get()

            # 記錄操作日誌
            self.view.log_message(f"正在執行 RNG 設置，會員ID: {member_id}, 遊戲ID: {game_id}, 遊戲: {game_name}, RNG: {rng_name}", level="INFO")

            # 驗證輸入
            if not member_id:
                raise ValueError("請輸入會員ID")
            if not game_id:
                raise ValueError("請輸入遊戲ID")
            if not game_name or not rng_name:
                raise ValueError("請選擇遊戲和RNG")

            # 取得 RNG 值
            df = self.game_data_dict[game_name]['dataframe']
            rng_data = df[df[COL_RNG_NAME] == rng_name]

            if rng_data.empty:
                raise ValueError(f"找不到 RNG: {rng_name}")

            rng_value = rng_data.iloc[0].get(COL_RNG, "")
            if not rng_value:
                raise ValueError(f"RNG 值為空")

            # 解析 RNG 值
            try:
                rng_value_str = str(rng_value)
                rng_numbers = [int(x.strip()) for x in rng_value_str.split(',') if x.strip()]
                if not rng_numbers:
                    raise ValueError()
            except ValueError:
                raise ValueError("RNG 值格式錯誤，必須是以逗號分隔的數字")

            # 準備 JSON 資料
            rng_json_data = {
                "rngData": [rng_numbers],
                "cardIdx": -1
            }

            # 記錄 RNG 數據
            self.view.log_message(f"RNG 數據: {rng_json_data}", level="INFO")

            # 實際呼叫 API
            encoded_json = quote(json.dumps(rng_json_data))

            # 檢查是否有當前會員，並獲取 agentCode
            agent_code = ""
            if self.current_member and hasattr(self.current_member, 'agent_code') and self.current_member.agent_code:
                agent_code = self.current_member.agent_code
                self.view.log_message(f"使用會員代理商代碼: {agent_code}", level="INFO")
            else:
                self.view.log_message("未找到會員代理商代碼，請先查詢會員", level="WARNING")
                raise ValueError("未找到會員代理商代碼，請先查詢會員")

            # 使用新的 API URL 格式，包含 agentCode
            url = f"{API_URLS['SET_RNG']}/{agent_code}/{member_id}/{game_id}/{encoded_json}"

            # 記錄 API URL
            self.view.log_message(f"API URL: {url}", level="INFO")
            logger.info(f"正在更新 RNG 設置: {url}")

            # 使用 HTTP 客戶端發送請求
            try:
                # 使用 get_with_details 方法以取得更詳細的回應資訊
                response, status_code = self.member_service.http_client.get_with_details(url)

                # 記錄 API 回應
                self.view.log_message(f"API 狀態碼: {status_code}", level="INFO")
                self.view.log_message(f"API 回應: {response}", level="INFO")

                # 檢查不同的回應格式
                if response and response.get("code", -1) == 0:
                    # 標準 API 回應格式
                    logger.info(f"更新 RNG 設置成功: {response}")
                    self.view.log_message(f"更新 RNG 設置成功: {response}", level="SUCCESS")
                    self.view.show_success(f"已成功更新 RNG 設置: {rng_name}")
                elif response and response.get("success") == True and response.get("text") == "Success":
                    # 另一種成功回應格式
                    logger.info(f"更新 RNG 設置成功: {response}")
                    self.view.log_message(f"更新 RNG 設置成功: {response}", level="SUCCESS")
                    self.view.show_success(f"已成功更新 RNG 設置: {rng_name}")
                elif response and response.get("success") == True:
                    # 一般成功回應
                    logger.info(f"更新 RNG 設置成功: {response}")
                    self.view.log_message(f"更新 RNG 設置成功: {response}", level="SUCCESS")
                    self.view.show_success(f"已成功更新 RNG 設置: {rng_name}")
                else:
                    # 處理錯誤回應
                    if response:
                        if "message" in response:
                            error_msg = response.get("message")
                        elif "text" in response:
                            error_msg = response.get("text")
                        else:
                            error_msg = "未知錯誤"
                    else:
                        error_msg = "無回應"
                    self.view.log_message(f"API 請求失敗: {error_msg}", level="ERROR")
                    raise ValueError(f"API 請求失敗: {error_msg}")
            except requests.exceptions.RequestException as req_error:
                # 記錄網路錯誤
                self.view.log_message(f"網路請求失敗: {req_error}", level="ERROR")
                raise ValueError(f"網路請求失敗: {req_error}")

        except ValueError as e:
            logger.error(f"參數錯誤: {e}")
            self.view.log_message(f"參數錯誤: {str(e)}", level="ERROR")
            self.view.show_error(f"參數錯誤: {str(e)}")
        except requests.exceptions.RequestException as e:
            logger.error(f"網路請求失敗: {e}")
            self.view.log_message(f"網路請求失敗: {str(e)}", level="ERROR")
            self.view.show_error(f"網路請求失敗: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析錯誤: {e}")
            self.view.log_message(f"JSON 解析錯誤: {str(e)}", level="ERROR")
            self.view.show_error(f"JSON 解析錯誤: {str(e)}")
        except Exception as e:
            logger.error(f"更新失敗: {e}")
            self.view.log_message(f"更新失敗: {str(e)}", level="ERROR")
            self.view.show_error(f"更新失敗: {str(e)}")

    def _handle_query(self):
        """處理查詢會員事件"""
        try:
            account = self.view.entry_account.get().strip()
            member_id = self.view.entry_member_id.get().strip()
            db_source = self.view.db_source.get()  # 獲取選擇的資料庫來源

            if not account and not member_id:
                raise ValueError("請輸入帳號或會員ID")

            # 顯示查詢中訊息
            logger.info(f"🔍 正在查詢會員資訊... (資料庫: {db_source})")

            # 使用 MemberService 查詢會員，傳入資料庫來源
            member, details = self.member_service.query_member(account, member_id, db_source)

            if member:
                # 儲存當前查詢到的會員
                self.current_member = member

                logger.info(f"✅ 查詢成功: 會員ID={member.member_id}, 帳號={member.account}")
                logger.info(f"資料庫來源: {member.db_source}")
                logger.info(f"代理商: {member.agent_code}, 子代理商: {member.sub_agent_code}")
                logger.info(f"幣別: {member.currency}, 狀態: {member.status}, VIP等級: {member.vip_level}")

                # 更新會員ID輸入框
                self.view.entry_member_id.delete(0, tk.END)
                self.view.entry_member_id.insert(0, member.member_id)

                # 顯示成功訊息
                self.view.show_success(f"已查詢到會員: {member.account}")
            else:
                # 清除當前會員
                self.current_member = None

                # 顯示錯誤詳情
                if details and details.get("error"):
                    logger.error(f"查詢失敗: {details['error']}")
                    raise ValueError(f"查詢失敗: {details['error']}")
                else:
                    raise ValueError("查無此會員")

        except ValueError as e:
            logger.error(f"查詢參數錯誤: {e}")
            self.view.show_error(f"查詢參數錯誤: {str(e)}")
        except requests.exceptions.RequestException as e:
            logger.error(f"網路請求失敗: {e}")
            self.view.show_error(f"網路請求失敗: {str(e)}")
        except Exception as e:
            logger.error(f"查詢失敗: {e}")
            self.view.show_error(f"查詢失敗: {str(e)}")

    def _handle_clear_member(self):
        """處理清除會員資訊事件"""
        self.view.entry_account.delete(0, tk.END)
        self.view.entry_member_id.delete(0, tk.END)
        # 清除當前會員
        self.current_member = None
        logger.info("✅ 已清除會員資訊")



    def _handle_import_from_gitlab(self):
        """處理從 Git 下載事件"""
        try:
            # 從設定檔中讀取 Git 設定
            from utils.config import Config
            config = Config()
            git_config = config.get("git", {})

            # 確保將 API_URLS 中的預設值更新到 config 中，如果設定檔中沒有相應的值
            if "api_url" not in git_config and "GITLAB_API" in API_URLS:
                git_config["api_url"] = API_URLS["GITLAB_API"]
            if "api_url_alt" not in git_config and "GITLAB_API_ALT" in API_URLS:
                git_config["api_url_alt"] = API_URLS["GITLAB_API_ALT"]
            if "project_path" not in git_config and "GITLAB_PROJECT" in API_URLS:
                git_config["project_path"] = API_URLS["GITLAB_PROJECT"]
            if "branch" not in git_config and "GITLAB_BRANCH" in API_URLS:
                git_config["branch"] = API_URLS["GITLAB_BRANCH"]
            if "folder_path" not in git_config and "GITLAB_FOLDER" in API_URLS:
                git_config["folder_path"] = API_URLS["GITLAB_FOLDER"]
            if "token" not in git_config and "GITLAB_TOKEN" in API_URLS:
                git_config["token"] = API_URLS["GITLAB_TOKEN"]

            # 儲存更新後的設定
            config.set("git", git_config)

            # 使用設定檔中的 Git 資訊
            project_path = git_config.get("project_path")
            branch = git_config.get("branch")
            folder_path = git_config.get("folder_path")
            access_token = git_config.get("token")

            # 記錄 Git 資訊
            self.view.log_message(f"Git API URL: {git_config.get('api_url', API_URLS['GITLAB_API'])}", level="INFO")
            self.view.log_message(f"Git 備用 API URL: {git_config.get('api_url_alt', API_URLS['GITLAB_API_ALT'])}", level="INFO")
            self.view.log_message(f"Git 專案: {project_path}", level="INFO")
            self.view.log_message(f"Git 分支: {branch}", level="INFO")
            self.view.log_message(f"Git 資料夾: {folder_path}", level="INFO")

            # 顯示進度訊息
            self.view.log_message("正在從 Git 下載檔案，請稍候...", level="INFO")

            # 顯示進度條
            self.view.show_progress_bar()

            # 使用非同步方式下載檔案，避免 UI 凍結
            import threading
            import queue

            # 創建一個隊列用於線程間通信
            self.result_queue = queue.Queue()

            # 創建並啟動線程
            thread = threading.Thread(target=self._import_from_gitlab_thread,
                                     args=(project_path, branch, folder_path, access_token, self.result_queue))
            thread.daemon = True  # 設為守護執行緒，當主執行緒結束時自動結束
            thread.start()

            # 設置一個定時器來檢查結果
            self.root.after(100, self._check_gitlab_thread_result)

        except (FileNotFoundError, IOError) as e:
            error_message = f"檔案存取錯誤: {str(e)}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            self.view.show_error("檔案存取錯誤，請查看日誌了解詳情")
        except requests.exceptions.RequestException as e:
            error_message = f"網路請求失敗: {str(e)}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            self.view.show_error("網路請求失敗，請檢查網路連接")
        except Exception as e:
            error_message = f"從 Gitlab 匯入失敗: {str(e)}"
            logger.error(error_message)
            # 顯示更詳細的錯誤訊息
            self.view.log_message(error_message, level="ERROR")
            self.view.log_message(f"Gitlab API: {API_URLS['GITLAB_API']}", level="ERROR")
            self.view.log_message(f"Gitlab 專案: {API_URLS['GITLAB_PROJECT']}", level="ERROR")
            self.view.log_message(f"Gitlab 分支: {API_URLS['GITLAB_BRANCH']}", level="ERROR")
            self.view.log_message(f"Gitlab 資料夾: {API_URLS['GITLAB_FOLDER']}", level="ERROR")
            self.view.show_error("從 Gitlab 匯入失敗，請查看日誌了解詳情")

    def _check_gitlab_thread_result(self):
        """檢查 Git 下載線程的結果"""
        try:
            # 嘗試從隊列中獲取結果，不阻塞
            try:
                result = self.result_queue.get_nowait()
                # 處理結果
                if result.get('success'):
                    # 使用線程安全的方式記錄日誌和更新 UI
                    logger.info("Git 下載完成")
                    self.view.log_message("Git 下載完成", level="SUCCESS")
                    # 隱藏進度條
                    self.view.hide_progress_bar()
                else:
                    error = result.get('error', '未知錯誤')
                    # 使用線程安全的方式記錄日誌和更新 UI
                    logger.error(f"Git 下載失敗: {error}")
                    self.view.log_message(f"Git 下載失敗: {error}", level="ERROR")
                    self.view.hide_progress_bar()
                    self.view.show_error(f"Git 下載失敗: {error}")
            except queue.Empty:
                # 隊列為空，繼續等待
                self.root.after(100, self._check_gitlab_thread_result)
        except Exception as e:
            # 記錄錯誤
            logger.error(f"檢查 Git 下載結果時發生錯誤: {e}")
            # 使用線程安全的方式更新 UI
            try:
                self.view.hide_progress_bar()
                self.view.show_error(f"檢查 Git 下載結果時發生錯誤: {str(e)}")
            except Exception as ui_error:
                logger.error(f"更新 UI 時發生錯誤: {ui_error}")

    def _import_from_gitlab_thread(self, project_path, branch, folder_path, access_token, result_queue):
        """在子執行緒中從 Git 下載檔案"""
        temp_dir = None
        try:
            # 記錄日誌
            self.view.log_message(f"正在從 Git 專案 {project_path} 的 {branch} 分支中的 {folder_path} 資料夾下載檔案...", level="INFO")
            self.view.update_progress_label("正在從 Git 專案下載檔案...")

            # 創建臨時目錄
            temp_dir = tempfile.mkdtemp()
            self.view.log_message(f"創建臨時目錄: {temp_dir}", level="INFO")

            # 直接使用成功的 Git 命令格式
            self.view.update_progress_label("正在使用 Git 命令下載儲存庫...")
            # 使用 constants.py 中定義的 URL
            if 'GITLAB_HTTPS_URL' in API_URLS:
                https_url = API_URLS['GITLAB_HTTPS_URL']
            else:
                https_url = f"https://git-qa.yile808.com/igaming/igaming-web-tool.git"
            git_cmd = f"git clone --depth 1 {https_url} {temp_dir}/repo"
            self.view.log_message(f"Git 命令: {git_cmd}", level="INFO")

            # 執行 Git 命令，添加超時設置
            import subprocess
            import time

            # 設置超時時間（秒）
            timeout = 60

            # 顯示確定性進度條
            self.view.show_progress_bar(determinate=True)
            self.view.update_progress_value(0)

            try:
                # 使用 Popen 而不是 run，以便能夠設置超時
                self.view.log_message(f"開始執行 Git 命令，超時時間: {timeout} 秒", level="INFO")
                process = subprocess.Popen(git_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

                # 記錄開始時間
                start_time = time.time()

                # 等待進程完成或超時
                stdout, stderr = "", ""
                while process.poll() is None:
                    # 檢查是否超時
                    if time.time() - start_time > timeout:
                        # 超時，終止進程
                        process.terminate()
                        # 等待進程終止
                        try:
                            process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            # 如果進程仍未終止，強制終止
                            process.kill()

                        # 記錄超時錯誤
                        self.view.log_message(f"Git 命令執行超時 ({timeout} 秒)", level="ERROR")
                        # 使用 Exception 而不是 TimeoutError，因為 TimeoutError 在 Python 3.3 之前不是內置異常
                        raise Exception(f"Git 命令執行超時 ({timeout} 秒)")

                    # 計算已經過的時間佔總超時時間的百分比
                    elapsed_time = time.time() - start_time
                    progress = min(95, int(elapsed_time / timeout * 100))  # 最多到 95%，留 5% 給最後的處理

                    # 更新進度條
                    self.view.update_progress_value(progress)

                    # 更新進度標籤
                    remaining = max(0, timeout - elapsed_time)
                    self.view.update_progress_label(f"正在下載 Git 儲存庫... (剩餘時間約 {remaining:.0f} 秒)")

                    # 等待一小段時間
                    time.sleep(0.1)

                # 獲取輸出
                stdout, stderr = process.communicate()

                # 創建一個類似 subprocess.run 的結果對象
                class Result:
                    def __init__(self, returncode, stdout, stderr):
                        self.returncode = returncode
                        self.stdout = stdout
                        self.stderr = stderr

                result = Result(process.returncode, stdout, stderr)

                # 記錄執行時間
                execution_time = time.time() - start_time
                self.view.log_message(f"Git 命令執行完成，耗時: {execution_time:.2f} 秒", level="INFO")

                # 更新進度條到 100%
                if result.returncode == 0:
                    self.view.update_progress_value(100)
                    self.view.update_progress_label("Git 命令執行成功")

            except Exception as e:
                # 超時錯誤或其他錯誤
                self.view.log_message(f"執行 Git 命令時發生錯誤: {str(e)}", level="ERROR")
                result = Result(1, "", str(e))

            # 檢查結果
            if result.returncode == 0:
                self.view.log_message(f"Git 命令執行成功", level="INFO")
                self.view.update_progress_label("正在尋找 Excel 檔案...")

                # 尋找下載的檔案
                import glob
                excel_files = glob.glob(f"{temp_dir}/repo/{folder_path}/*.xlsx") + glob.glob(f"{temp_dir}/repo/{folder_path}/*.xls")

                if excel_files:
                    self.view.log_message(f"找到 {len(excel_files)} 個 Excel 檔案", level="INFO")
                    self.view.update_progress_label(f"正在處理 {len(excel_files)} 個 Excel 檔案...")
                    temp_files = [Path(file) for file in excel_files]
                    self._process_downloaded_files(temp_files)
                    # 將成功結果放入隊列
                    result_queue.put({'success': True})
                    return
                else:
                    self.view.log_message(f"在 {temp_dir}/repo/{folder_path} 中找不到任何 Excel 檔案", level="ERROR")
                    # 將失敗結果放入隊列
                    result_queue.put({'success': False, 'error': "在專案中找不到 RNG 檔案，請嘗試使用其他方式匯入"})
            else:
                error_msg = str(result.stderr)
                self.view.log_message(f"Git 命令執行失敗: {error_msg}", level="ERROR")

                # 嘗試使用備用方法：直接從 API 下載檔案
                self.view.log_message("嘗試使用備用方法：直接從 API 下載檔案", level="INFO")
                self.view.update_progress_label("正在使用 API 下載檔案...")
                self.view.update_progress_value(0)  # 重置進度條

                try:
                    # 使用 API 獲取檔案列表
                    self.view.update_progress_label("正在獲取檔案列表...")
                    self.view.update_progress_value(10)  # 設置進度為 10%
                    files = self._get_gitlab_files(project_path, branch, folder_path, access_token)

                    if files:
                        self.view.log_message(f"從 API 找到 {len(files)} 個檔案", level="INFO")
                        self.view.update_progress_label(f"正在下載 {len(files)} 個檔案...")
                        self.view.update_progress_value(20)  # 設置進度為 20%

                        # 創建臨時目錄來存儲下載的檔案
                        import os
                        api_temp_dir = os.path.join(temp_dir, "api_files")
                        os.makedirs(api_temp_dir, exist_ok=True)

                        # 下載每個檔案
                        downloaded_files = []
                        total_files = len(files)

                        for i, file_info in enumerate(files):
                            file_path = file_info.get('path')
                            file_name = file_info.get('name')

                            if file_path and file_name:
                                # 計算當前進度 (20% 到 80% 之間)
                                progress = 20 + int((i / total_files) * 60)
                                self.view.update_progress_value(progress)
                                self.view.update_progress_label(f"正在下載檔案 ({i+1}/{total_files}): {file_name}")
                                self.view.log_message(f"正在下載檔案: {file_path}", level="INFO")

                                # 下載檔案內容
                                content = self._get_gitlab_file_content(project_path, file_path, branch, access_token)

                                if content:
                                    # 保存檔案
                                    local_path = os.path.join(api_temp_dir, file_name)
                                    with open(local_path, 'wb') as f:
                                        f.write(content)

                                    downloaded_files.append(Path(local_path))
                                    self.view.log_message(f"成功下載檔案: {file_path} -> {local_path}", level="INFO")

                        if downloaded_files:
                            self.view.log_message(f"成功下載 {len(downloaded_files)} 個檔案", level="INFO")
                            self.view.update_progress_label(f"正在處理 {len(downloaded_files)} 個檔案...")
                            self.view.update_progress_value(90)  # 設置進度為 90%
                            self._process_downloaded_files(downloaded_files)
                            # 更新進度條到 100%
                            self.view.update_progress_value(100)
                            self.view.update_progress_label("檔案處理完成")
                            # 將成功結果放入隊列
                            result_queue.put({'success': True})
                            return
                        else:
                            self.view.log_message("沒有成功下載任何檔案", level="ERROR")
                            self.view.update_progress_value(100)  # 設置進度為 100%
                            self.view.update_progress_label("下載失敗：沒有成功下載任何檔案")
                    else:
                        self.view.log_message("從 API 找不到任何檔案", level="ERROR")
                        self.view.update_progress_value(100)  # 設置進度為 100%
                        self.view.update_progress_label("下載失敗：從 API 找不到任何檔案")

                except Exception as api_error:
                    self.view.log_message(f"使用 API 下載檔案失敗: {str(api_error)}", level="ERROR")
                    self.view.update_progress_value(100)  # 設置進度為 100%
                    self.view.update_progress_label("下載失敗：API 錯誤")

                # 如果備用方法也失敗，返回原始錯誤
                # 將失敗結果放入隊列
                result_queue.put({'success': False, 'error': f"Git 命令執行失敗: {error_msg}"})
        except Exception as e:
            error_message = f"從 Git 下載失敗: {str(e)}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            # 將失敗結果放入隊列
            result_queue.put({'success': False, 'error': f"從 Git 下載失敗: {str(e)}"})
        finally:
            # 清理臨時目錄
            if temp_dir and os.path.exists(temp_dir):
                import shutil
                try:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    self.view.log_message(f"已清理臨時目錄: {temp_dir}", level="INFO")
                except Exception as cleanup_error:
                    self.view.log_message(f"清理臨時目錄失敗: {cleanup_error}", level="WARNING")

    def _get_gitlab_files_alt(self, project_path: str, branch: str, folder_path: str, access_token: str) -> List[Dict]:
        """使用備用 API URL 取得 Gitlab 專案中的檔案列表"""
        try:
            # 建立 API URL
            # 使用備用 API URL
            encoded_project_path = quote(project_path, safe='')
            url = f"{API_URLS['GITLAB_API_ALT']}/{project_path}/-/tree/{branch}/{folder_path}"

            # 記錄 API 請求
            self.view.log_message(f"API 請求: {url}", level="INFO")

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "text/html,application/xhtml+xml,application/xml",  # 要求 HTML 回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 發送請求
            response = self.member_service.http_client.get(
                url,
                headers=headers
            )

            # 記錄 API 回應
            self.view.log_message(f"API 回應: {response}", level="INFO")

            # 嘗試解析 HTML 回應中的檔案列表
            # 注意：這只是一個簡單的示例，實際上需要更完善的 HTML 解析
            if isinstance(response, dict) and 'text' in response:
                html = response['text']
                # 尋找 Excel 檔案
                import re
                excel_files = re.findall(r'href="[^"]*\.xlsx?"', html)

                result = []
                for file in excel_files:
                    file_path = file.split('href="')[1].split('"')[0]
                    file_name = file_path.split('/')[-1]
                    result.append({
                        'type': 'blob',
                        'name': file_name,
                        'path': f"{folder_path}/{file_name}"
                    })

                return result

            return []

        except Exception as e:
            error_message = f"使用備用 API URL 取得 Gitlab 檔案列表失敗: {e}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            self.view.log_message(f"API URL: {url}", level="ERROR")
            return []

    def _get_gitlab_files(self, project_path: str, branch: str, folder_path: str, access_token: str) -> List[Dict]:
        """取得 Gitlab 專案中的檔案列表"""
        try:
            # 建立 API URL
            # 使用不同的專案路徑格式
            project_path_formats = [
                project_path,  # 原始格式
                quote(project_path, safe=''),  # URL 編碼
                project_path.replace('/', '%2F'),  # 手動 URL 編碼
                "igaming%2Figaming-web-tool",  # 硬編碼
                "igaming/igaming-web-tool",  # 原始格式
                "igaming",  # 只使用第一部分
                "igaming-web-tool"  # 只使用第二部分
            ]

            # 嘗試使用不同的專案路徑格式
            for path_format in project_path_formats:
                # 建立 API URL
                url = f"{API_URLS['GITLAB_API']}/projects/{path_format}/repository/tree"

                # 記錄完整的 URL
                self.view.log_message(f"嘗試使用專案路徑格式: {path_format}", level="INFO")
                self.view.log_message(f"完整的 API URL: {url}?ref={branch}&path={folder_path}&per_page=100", level="INFO")

                # 嘗試使用這個 URL
                try:
                    # 設定參數
                    params = {
                        "ref": branch,
                        "path": folder_path,
                        "per_page": 100  # 每頁顯示的最大檔案數
                    }

                    # 設定標頭
                    headers = {
                        "PRIVATE-TOKEN": access_token,
                        "Accept": "application/json",  # 要求 JSON 回應
                        "Cache-Control": "no-cache",  # 不使用快取
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
                    }

                    # 發送請求
                    response = self.member_service.http_client.get(
                        url,
                        params=params,
                        headers=headers
                    )

                    # 記錄 API 回應
                    self.view.log_message(f"API 回應: {response}", level="INFO")

                    # 篩選 Excel 檔案
                    if isinstance(response, list):
                        result = [f for f in response if f.get('type') == 'blob' and
                                (f.get('path', '').endswith('.xlsx') or f.get('path', '').endswith('.xls'))]
                        if result:
                            self.view.log_message(f"找到 {len(result)} 個 Excel 檔案", level="INFO")
                            return result
                except Exception as e:
                    self.view.log_message(f"使用專案路徑格式 {path_format} 失敗: {e}", level="ERROR")

            # 如果所有專案路徑格式都失敗，使用原始的專案路徑
            encoded_project_path = quote(project_path, safe='')
            url = f"{API_URLS['GITLAB_API']}/projects/{encoded_project_path}/repository/tree"

            # 記錄完整的 URL
            self.view.log_message(f"完整的 API URL: {url}?ref={branch}&path={folder_path}&per_page=100", level="INFO")

            # 設定參數
            params = {
                "ref": branch,
                "path": folder_path,
                "per_page": 100  # 每頁顯示的最大檔案數
            }

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "application/json",  # 要求 JSON 回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 記錄 API 請求
            self.view.log_message(f"API 請求: {url}", level="INFO")
            self.view.log_message(f"API 參數: {params}", level="INFO")

            # 嘗試使用 curl 命令下載檔案
            try:
                import subprocess
                import json

                # 建立 curl 命令
                curl_cmd = [
                    "curl", "-s",
                    "-H", f"PRIVATE-TOKEN: {access_token}",
                    "-H", "Accept: application/json",
                    "-H", "Cache-Control: no-cache",
                    "-H", "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    f"{url}?ref={branch}&path={folder_path}&per_page=100"
                ]

                # 執行 curl 命令
                self.view.log_message(f"Curl 命令: {' '.join(curl_cmd)}", level="INFO")
                result = subprocess.run(curl_cmd, capture_output=True, text=True)

                # 檢查結果
                if result.returncode == 0 and result.stdout:
                    try:
                        # 嘗試解析 JSON
                        curl_response = json.loads(result.stdout)
                        self.view.log_message(f"Curl 回應: {curl_response}", level="INFO")

                        # 如果成功，使用 curl 的結果
                        if isinstance(curl_response, list):
                            return [f for f in curl_response if f.get('type') == 'blob' and
                                    (f.get('path', '').endswith('.xlsx') or f.get('path', '').endswith('.xls'))]
                    except json.JSONDecodeError:
                        self.view.log_message(f"Curl 回應不是有效的 JSON: {result.stdout[:200]}...", level="ERROR")
                else:
                    self.view.log_message(f"Curl 命令失敗: {result.stderr}", level="ERROR")
            except Exception as curl_error:
                self.view.log_message(f"Curl 命令失敗: {curl_error}", level="ERROR")

            # 如果 curl 失敗，使用原始的 HTTP 請求
            self.view.log_message("使用原始的 HTTP 請求...", level="INFO")
            # 發送請求
            response = self.member_service.http_client.get(
                url,
                params=params,
                headers=headers
            )

            # 記錄 API 回應
            self.view.log_message(f"API 回應: {response}", level="INFO")

            # 篩選 Excel 檔案
            if isinstance(response, list):
                return [f for f in response if f.get('type') == 'blob' and
                        (f.get('path', '').endswith('.xlsx') or f.get('path', '').endswith('.xls'))]
            return []

        except Exception as e:
            error_message = f"取得 Gitlab 檔案列表失敗: {e}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            self.view.log_message(f"API URL: {url}", level="ERROR")
            self.view.log_message(f"API 參數: {params}", level="ERROR")
            return []

    def _get_gitlab_file_content(self, project_path: str, file_path: str, branch: str, access_token: str) -> Optional[bytes]:
        """取得 Gitlab 檔案內容"""
        try:
            # 建立 API URL
            # 使用不同的專案路徑格式
            project_path_formats = [
                project_path,  # 原始格式
                quote(project_path, safe=''),  # URL 編碼
                project_path.replace('/', '%2F'),  # 手動 URL 編碼
                "igaming%2Figaming-web-tool",  # 硬編碼
                "igaming/igaming-web-tool",  # 原始格式
                "igaming",  # 只使用第一部分
                "igaming-web-tool"  # 只使用第二部分
            ]

            # 使用 URL 編碼的檔案路徑
            encoded_file_path = quote(file_path, safe='')

            # 嘗試使用不同的 API 端點
            api_endpoints = [
                # 1. 標準 API 端點
                lambda p: f"{API_URLS['GITLAB_API']}/projects/{p}/repository/files/{encoded_file_path}/raw",
                # 2. 備用 API 端點 - 直接存取檔案
                lambda p: f"{API_URLS['GITLAB_API_ALT']}/{p}/-/raw/{branch}/{file_path}",
                # 3. 備用 API 端點 - blob 格式
                lambda p: f"{API_URLS['GITLAB_API_ALT']}/{p}/blob/{branch}/{file_path}",
                # 4. 備用 API 端點 - 不使用 -/raw 格式
                lambda p: f"{API_URLS['GITLAB_API_ALT']}/{p}/raw/{branch}/{file_path}"
            ]

            # 嘗試使用不同的專案路徑格式和 API 端點
            for path_format in project_path_formats:
                for endpoint in api_endpoints:
                    url = endpoint(path_format)
                    self.view.log_message(f"嘗試使用專案路徑格式: {path_format}", level="INFO")
                    self.view.log_message(f"完整的檔案 API URL: {url}?ref={branch}", level="INFO")

                    # 嘗試使用 curl 命令下載檔案
                    try:
                        import subprocess

                        # 建立 curl 命令
                        curl_cmd = [
                            "curl", "-s",
                            "-H", f"PRIVATE-TOKEN: {access_token}",
                            "-H", "Accept: application/octet-stream",
                            "-H", "Cache-Control: no-cache",
                            "-H", "Accept-Encoding: identity",
                            "-H", "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                            f"{url}?ref={branch}"
                        ]

                        # 執行 curl 命令
                        self.view.log_message(f"Curl 命令: {' '.join(curl_cmd)}", level="INFO")
                        result = subprocess.run(curl_cmd, capture_output=True)

                        # 檢查結果
                        if result.returncode == 0 and result.stdout:
                            content = result.stdout
                            self.view.log_message(f"Curl 下載成功，大小: {len(content)} 位元組", level="INFO")

                            # 檢查檔案頭部，確保是 Excel 檔案
                            if len(content) >= 20:
                                header_hex = ' '.join([f'{b:02x}' for b in content[:20]])
                                self.view.log_message(f"Curl 下載的檔案頭部: {header_hex}", level="INFO")

                            # 如果是 Excel 檔案，直接返回
                            excel_signatures = [b'PK\x03\x04', b'\xd0\xcf\x11\xe0']  # XLSX 和 XLS 的檔案頭部特徵
                            for sig in excel_signatures:
                                if content.startswith(sig):
                                    return content

                            # 如果是 gzip 壓縮檔案，嘗試解壓縮
                            if content[:2] == b'\x1f\x8b':
                                try:
                                    decompressed_content = gzip.decompress(content)
                                    self.view.log_message(f"Curl 下載的檔案是 gzip 壓縮檔案，解壓縮後大小: {len(decompressed_content)} 位元組", level="INFO")
                                    return decompressed_content
                                except Exception as gzip_error:
                                    self.view.log_message(f"Curl 下載的檔案解壓縮失敗: {gzip_error}", level="ERROR")
                    except Exception as curl_error:
                        self.view.log_message(f"Curl 命令失敗: {curl_error}", level="ERROR")

            # 如果所有專案路徑格式和 API 端點都失敗，使用原始的專案路徑和 API 端點
            encoded_project_path = quote(project_path, safe='')
            url = f"{API_URLS['GITLAB_API']}/projects/{encoded_project_path}/repository/files/{encoded_file_path}/raw"

            # 記錄完整的 URL
            self.view.log_message(f"完整的檔案 API URL: {url}?ref={branch}", level="INFO")

            # 如果是備用 API URL，使用不同的端點格式
            if 'GITLAB_API_ALT' in API_URLS and API_URLS['GITLAB_API'] == API_URLS['GITLAB_API_ALT'] + '/api/v4':
                # 2. 備用 API 端點 - 直接存取檔案
                url = f"{API_URLS['GITLAB_API_ALT']}/{project_path}/-/raw/{branch}/{file_path}"
                self.view.log_message(f"備用檔案 API URL: {url}", level="INFO")

            # 如果有 SSH URL，也記錄下來
            if 'GITLAB_SSH_URL' in API_URLS:
                ssh_url = f"{API_URLS['GITLAB_SSH_URL']}/{project_path}.git"
                self.view.log_message(f"SSH URL: {ssh_url}", level="INFO")
                self.view.log_message(f"Git 命令: git clone {ssh_url}", level="INFO")

            # 設定參數
            params = {
                "ref": branch
            }

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "application/octet-stream",  # 要求二進位檔案
                "Accept-Encoding": "identity",  # 要求不要壓縮回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 嘗試使用 curl 命令下載檔案
            try:
                import subprocess

                # 建立 curl 命令
                curl_cmd = [
                    "curl", "-s",
                    "-H", f"PRIVATE-TOKEN: {access_token}",
                    "-H", "Accept: application/octet-stream",
                    "-H", "Cache-Control: no-cache",
                    "-H", "Accept-Encoding: identity",
                    "-H", "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    f"{url}?ref={branch}"
                ]

                # 執行 curl 命令
                self.view.log_message(f"Curl 命令: {' '.join(curl_cmd)}", level="INFO")
                result = subprocess.run(curl_cmd, capture_output=True)

                # 檢查結果
                if result.returncode == 0 and result.stdout:
                    content = result.stdout
                    self.view.log_message(f"Curl 下載成功，大小: {len(content)} 位元組", level="INFO")

                    # 檢查檔案頭部，確保是 Excel 檔案
                    if len(content) >= 20:
                        header_hex = ' '.join([f'{b:02x}' for b in content[:20]])
                        self.view.log_message(f"Curl 下載的檔案頭部: {header_hex}", level="INFO")

                    # 如果是 Excel 檔案，直接返回
                    excel_signatures = [b'PK\x03\x04', b'\xd0\xcf\x11\xe0']  # XLSX 和 XLS 的檔案頭部特徵
                    for sig in excel_signatures:
                        if content.startswith(sig):
                            return content

                    # 如果是 gzip 壓縮檔案，嘗試解壓縮
                    if content[:2] == b'\x1f\x8b':
                        try:
                            decompressed_content = gzip.decompress(content)
                            self.view.log_message(f"Curl 下載的檔案是 gzip 壓縮檔案，解壓縮後大小: {len(decompressed_content)} 位元組", level="INFO")
                            return decompressed_content
                        except Exception as gzip_error:
                            self.view.log_message(f"Curl 下載的檔案解壓縮失敗: {gzip_error}", level="ERROR")
                else:
                    error_msg = str(result.stderr)
                    self.view.log_message(f"Curl 命令失敗: {error_msg}", level="ERROR")
            except Exception as curl_error:
                self.view.log_message(f"Curl 命令失敗: {curl_error}", level="ERROR")

            # 如果 curl 失敗，使用原始的 HTTP 請求
            self.view.log_message("使用原始的 HTTP 請求...", level="INFO")

            # 使用 requests 直接下載二進位檔案
            import requests
            import urllib.request
            import ssl
            import gzip
            import io
            self.view.log_message(f"使用 requests 直接下載二進位檔案: {url}", level="INFO")

            # 嘗試不同的請求方式
            try:
                # 先嘗試標準的 API 請求
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    verify=False,  # 關閉 SSL 驗證
                    stream=True  # 使用串流模式下載
                )

                # 如果失敗，嘗試不同的 URL 格式
                if response.status_code != 200:
                    self.view.log_message(f"標準 API 請求失敗，嘗試備用 URL 格式", level="WARNING")
                    # 備用 URL 格式
                    alt_url = f"{API_URLS['GITLAB_API_ALT']}/{project_path}/raw/{branch}/{file_path}"
                    self.view.log_message(f"備用 URL: {alt_url}", level="INFO")
                    response = requests.get(
                        alt_url,
                        headers=headers,
                        verify=False,  # 關閉 SSL 驗證
                        stream=True  # 使用串流模式下載
                    )
            except Exception as req_error:
                self.view.log_message(f"請求失敗，嘗試備用方法: {req_error}", level="WARNING")
                try:
                    # 嘗試使用 urllib 下載
                    self.view.log_message("嘗試使用 urllib 下載", level="INFO")
                    # 建立不驗證 SSL 的上下文
                    context = ssl._create_unverified_context()
                    # 建立請求
                    req = urllib.request.Request(url, headers=headers)
                    # 發送請求
                    with urllib.request.urlopen(req, context=context) as resp:
                        # 讀取回應
                        content = resp.read()
                        self.view.log_message(f"使用 urllib 下載成功，大小: {len(content)} 位元組", level="INFO")
                        return content
                except Exception as urllib_error:
                    self.view.log_message(f"urllib 下載失敗: {urllib_error}", level="WARNING")
                    # 嘗試使用備用方法
                    alt_url = f"{API_URLS['GITLAB_API_ALT']}/{project_path}/blob/{branch}/{file_path}"
                    self.view.log_message(f"備用 URL: {alt_url}", level="INFO")
                    response = requests.get(
                        alt_url,
                        headers=headers,
                        verify=False,  # 關閉 SSL 驗證
                        stream=True  # 使用串流模式下載
                    )

            # 檢查回應
            if response.status_code == 200:
                try:
                    # 如果是串流模式，讀取內容
                    if hasattr(response, 'raw') and response.raw:
                        content = response.raw.read()
                        self.view.log_message(f"成功下載檔案(串流模式): {file_path}, 大小: {len(content)} 位元組", level="INFO")
                    else:
                        # 直接讀取內容
                        content = response.content
                        self.view.log_message(f"成功下載檔案: {file_path}, 大小: {len(content)} 位元組", level="INFO")

                    # 檢查是否為 gzip 壓縮檔案
                    if content and len(content) > 2 and content[:2] == b'\x1f\x8b':
                        self.view.log_message(f"偵測到 gzip 壓縮檔案，正在解壓縮...", level="INFO")
                        try:
                            # 解壓縮 gzip 內容
                            decompressed_content = gzip.decompress(content)
                            self.view.log_message(f"解壓縮成功，解壓縮後大小: {len(decompressed_content)} 位元組", level="INFO")
                            return decompressed_content
                        except Exception as gzip_error:
                            self.view.log_message(f"gzip 解壓縮失敗: {gzip_error}", level="ERROR")
                            # 如果解壓縮失敗，返回原始內容
                            return content

                    return content
                except Exception as read_error:
                    self.view.log_message(f"讀取回應內容失敗: {read_error}", level="ERROR")
                    # 嘗試使用備用方法
                    try:
                        content = response.content
                        self.view.log_message(f"使用備用方法成功讀取內容: {len(content)} 位元組", level="INFO")
                        return content
                    except Exception as backup_error:
                        self.view.log_message(f"備用方法也失敗: {backup_error}", level="ERROR")
                        return None
            else:
                self.view.log_message(f"下載檔案失敗: {file_path}, 狀態碼: {response.status_code}", level="ERROR")
                try:
                    self.view.log_message(f"回應內容: {response.text[:200]}...", level="ERROR")
                except Exception as text_error:
                    self.view.log_message(f"無法讀取回應內容: {text_error}", level="ERROR")
                return None

        except Exception as e:
            error_message = f"取得 Gitlab 檔案內容失敗: {e}"
            logger.error(error_message)
            self.view.log_message(error_message, level="ERROR")
            self.view.log_message(f"API URL: {url}", level="ERROR")
            self.view.log_message(f"API 參數: {params}", level="ERROR")
            self.view.log_message(f"檔案路徑: {file_path}", level="ERROR")
            return None

    def _process_downloaded_files(self, file_paths: List[Path]):
        """處理下載的檔案"""
        try:
            # 檢查是否在主線程中
            import threading
            is_main_thread = threading.current_thread() is threading.main_thread()

            self.game_data_dict = {}
            game_names = []

            # 更新進度條標籤
            if is_main_thread:
                self.view.update_progress_label(f"正在處理 {len(file_paths)} 個 Excel 檔案...")
            else:
                # 在子線程中，將任務放入隊列
                self.view.ui_queue.put((self.view._do_update_progress_label, (f"正在處理 {len(file_paths)} 個 Excel 檔案...",), {}))

            for file_path in file_paths:
                try:
                    # 嘗試讀取 Excel 檔案
                    try:
                        # 顯示檔案大小
                        file_size = os.path.getsize(file_path)
                        if is_main_thread:
                            self.view.log_message(f"檔案大小: {file_size} 位元組", level="INFO")
                        else:
                            # 在子線程中，將任務放入隊列
                            self.view.ui_queue.put((self.view.log_message, (f"檔案大小: {file_size} 位元組",), {"level": "INFO"}))

                        # 檢查檔案內容，確認是否為 gzip 壓縮檔案
                        with open(file_path, 'rb') as f:
                            header = f.read(2)
                            if header == b'\x1f\x8b':
                                if is_main_thread:
                                    self.view.log_message(f"偵測到 gzip 壓縮檔案，正在解壓縮: {file_path.name}", level="INFO")
                                else:
                                    # 在子線程中，將任務放入隊列
                                    self.view.ui_queue.put((self.view.log_message, (f"偵測到 gzip 壓縮檔案，正在解壓縮: {file_path.name}",), {"level": "INFO"}))
                                try:
                                    # 重新開啟檔案並讀取全部內容
                                    with open(file_path, 'rb') as gzip_file:
                                        compressed_content = gzip_file.read()

                                    # 解壓縮 gzip 內容
                                    decompressed_content = gzip.decompress(compressed_content)
                                    if is_main_thread:
                                        self.view.log_message(f"解壓縮成功，解壓縮後大小: {len(decompressed_content)} 位元組", level="INFO")
                                    else:
                                        # 在子線程中，將任務放入隊列
                                        self.view.ui_queue.put((self.view.log_message, (f"解壓縮成功，解壓縮後大小: {len(decompressed_content)} 位元組",), {"level": "INFO"}))

                                    # 將解壓縮後的內容寫入檔案
                                    with open(file_path, 'wb') as f:
                                        f.write(decompressed_content)

                                    # 更新檔案大小
                                    file_size = len(decompressed_content)
                                    if is_main_thread:
                                        self.view.log_message(f"更新後的檔案大小: {file_size} 位元組", level="INFO")
                                    else:
                                        # 在子線程中，將任務放入隊列
                                        self.view.ui_queue.put((self.view.log_message, (f"更新後的檔案大小: {file_size} 位元組",), {"level": "INFO"}))
                                except Exception as gzip_error:
                                    if is_main_thread:
                                        self.view.log_message(f"gzip 解壓縮失敗: {gzip_error}", level="ERROR")
                                    else:
                                        # 在子線程中，將任務放入隊列
                                        self.view.ui_queue.put((self.view.log_message, (f"gzip 解壓縮失敗: {gzip_error}",), {"level": "ERROR"}))

                        # 嘗試使用不同的引擎讀取 Excel 檔案
                        try:
                            # 先嘗試使用 openpyxl 引擎
                            if is_main_thread:
                                self.view.log_message(f"嘗試使用 openpyxl 引擎讀取檔案: {file_path.name}", level="INFO")
                            else:
                                # 在子線程中，將任務放入隊列
                                self.view.ui_queue.put((self.view.log_message, (f"嘗試使用 openpyxl 引擎讀取檔案: {file_path.name}",), {"level": "INFO"}))
                            with open(file_path, 'rb') as f:
                                df = pd.read_excel(f, engine='openpyxl')
                        except Exception as openpyxl_error:
                            if is_main_thread:
                                self.view.log_message(f"openpyxl 引擎失敗: {openpyxl_error}", level="WARNING")
                            else:
                                # 在子線程中，將任務放入隊列
                                self.view.ui_queue.put((self.view.log_message, (f"openpyxl 引擎失敗: {openpyxl_error}",), {"level": "WARNING"}))
                            try:
                                # 嘗試使用 xlrd 引擎
                                if is_main_thread:
                                    self.view.log_message(f"嘗試使用 xlrd 引擎讀取檔案: {file_path.name}", level="INFO")
                                else:
                                    # 在子線程中，將任務放入隊列
                                    self.view.ui_queue.put((self.view.log_message, (f"嘗試使用 xlrd 引擎讀取檔案: {file_path.name}",), {"level": "INFO"}))
                                with open(file_path, 'rb') as f:
                                    df = pd.read_excel(f, engine='xlrd')
                            except Exception as xlrd_error:
                                if is_main_thread:
                                    self.view.log_message(f"xlrd 引擎失敗: {xlrd_error}", level="WARNING")
                                    # 兩種引擎都失敗，記錄錯誤並繼續
                                    logger.error(f"讀取 Excel 檔案失敗: {openpyxl_error} / {xlrd_error}")
                                    self.view.log_message(f"讀取 Excel 檔案失敗: {openpyxl_error} / {xlrd_error}", level="ERROR")
                                else:
                                    # 在子線程中，將任務放入隊列
                                    self.view.ui_queue.put((self.view.log_message, (f"xlrd 引擎失敗: {xlrd_error}",), {"level": "WARNING"}))
                                    # 兩種引擎都失敗，記錄錯誤並繼續
                                    logger.error(f"讀取 Excel 檔案失敗: {openpyxl_error} / {xlrd_error}")
                                    self.view.ui_queue.put((self.view.log_message, (f"讀取 Excel 檔案失敗: {openpyxl_error} / {xlrd_error}",), {"level": "ERROR"}))
                                continue
                    except Exception as excel_error:
                        logger.error(f"讀取 Excel 檔案失敗: {excel_error}")
                        if is_main_thread:
                            self.view.log_message(f"讀取 Excel 檔案失敗: {excel_error}", level="ERROR")
                        else:
                            # 在子線程中，將任務放入隊列
                            self.view.ui_queue.put((self.view.log_message, (f"讀取 Excel 檔案失敗: {excel_error}",), {"level": "ERROR"}))
                        continue

                    game_name = file_path.stem  # 使用檔案名稱作為遊戲名稱

                    if COL_RNG_NAME in df.columns:
                        valid_df = df.dropna(subset=[COL_RNG_NAME])
                        if not valid_df.empty:
                            rng_names = valid_df[COL_RNG_NAME].unique().tolist()
                            self.game_data_dict[game_name] = {
                                'dataframe': valid_df.reset_index(drop=True),
                                'rng_names': rng_names
                            }
                            game_names.append(game_name)
                        else:
                            logger.warning(f"遊戲 '{game_name}' 的 '{COL_RNG_NAME}' 欄位皆為空值，已忽略。")
                    else:
                        logger.warning(f"遊戲 '{game_name}' 缺少 '{COL_RNG_NAME}' 欄位，已忽略。")

                except Exception as e:
                    logger.error(f"讀取檔案 {file_path} 失敗: {e}")

            if not self.game_data_dict:
                if is_main_thread:
                    self.view.show_error("所有檔案都無法成功解析或缺少必要的 RNGName 資訊。")
                else:
                    # 在子線程中，將任務放入隊列
                    self.view.ui_queue.put((self.view.show_error, ("所有檔案都無法成功解析或缺少必要的 RNGName 資訊。",), {}))
                return

            # 更新遊戲下拉選單 - 這部分需要在主線程中執行
            def update_ui():
                game_names_sorted = sorted(game_names)
                self.view.cb_game['values'] = game_names_sorted

                if game_names_sorted:
                    first_game = game_names_sorted[0]
                    self.view.cb_game.set(first_game)
                    self._handle_game_selected()

                # 隱藏進度條
                self.view.hide_progress_bar()

                # 顯示成功訊息
                self.view.show_success(f"已成功從 Git 匯入 {len(self.game_data_dict)} 個遊戲的 RNG 資料")

            # 根據當前線程決定如何執行 UI 更新
            if is_main_thread:
                update_ui()
            else:
                # 在子線程中，將 UI 更新任務放入隊列
                self.view.ui_queue.put((update_ui, (), {}))

        except Exception as e:
            logger.error(f"處理下載檔案失敗: {e}")

            # 檢查是否在主線程中
            import threading
            is_main_thread = threading.current_thread() is threading.main_thread()

            # 隱藏進度條
            if is_main_thread:
                self.view.hide_progress_bar()
                self.view.show_error(f"處理下載檔案失敗: {str(e)}")
            else:
                # 在子線程中，將任務放入隊列
                self.view.ui_queue.put((self.view.hide_progress_bar, (), {}))
                self.view.ui_queue.put((self.view.show_error, (f"處理下載檔案失敗: {str(e)}",), {}))

    def _list_available_projects(self, access_token: str) -> None:
        """列出所有可用的專案"""
        try:
            # 建立 API URL
            url = f"{API_URLS['GITLAB_API']}/projects"

            # 設定參數
            params = {
                "per_page": 20,  # 每頁顯示的最大專案數
                "order_by": "name",
                "sort": "asc"
            }

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "application/json",  # 要求 JSON 回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 記錄 API 請求
            self.view.log_message(f"API 請求: {url}", level="INFO")
            self.view.log_message(f"API 參數: {params}", level="INFO")

            # 發送請求
            response = self.member_service.http_client.get(
                url,
                params=params,
                headers=headers
            )

            # 記錄 API 回應
            if isinstance(response, list):
                self.view.log_message(f"找到 {len(response)} 個專案", level="INFO")
                for project in response[:5]:  # 只顯示前 5 個專案
                    project_info = f"ID: {project.get('id', 'N/A')}, 名稱: {project.get('name', 'N/A')}, 路徑: {project.get('path_with_namespace', 'N/A')}"
                    self.view.log_message(project_info, level="INFO")
            else:
                self.view.log_message(f"API 回應: {response}", level="INFO")
        except Exception as e:
            self.view.log_message(f"列出專案失敗: {e}", level="ERROR")

    def _get_project_id(self, project_path: str, access_token: str) -> Optional[str]:
        """獲取專案 ID"""
        try:
            # 建立 API URL
            url = f"{API_URLS['GITLAB_API']}/projects"

            # 設定參數
            params = {
                "search": project_path,
                "per_page": 10
            }

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "application/json",  # 要求 JSON 回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 記錄 API 請求
            self.view.log_message(f"API 請求: {url}", level="INFO")
            self.view.log_message(f"API 參數: {params}", level="INFO")

            # 發送請求
            response = self.member_service.http_client.get(
                url,
                params=params,
                headers=headers
            )

            # 記錄 API 回應
            if isinstance(response, list) and response:
                for project in response:
                    if project.get('path_with_namespace') == project_path:
                        return str(project.get('id'))

                # 如果沒有完全匹配的，返回第一個專案的 ID
                self.view.log_message(f"找不到完全匹配的專案，使用第一個專案: {response[0].get('path_with_namespace')}", level="INFO")
                return str(response[0].get('id'))
            else:
                self.view.log_message(f"API 回應: {response}", level="INFO")
                return None
        except Exception as e:
            self.view.log_message(f"獲取專案 ID 失敗: {e}", level="ERROR")
            return None

    def _list_project_root(self, project_path: str, branch: str, access_token: str) -> None:
        """列出專案的根目錄"""
        try:
            # 建立 API URL
            # 使用 URL 編碼的專案路徑
            encoded_project_path = quote(project_path, safe='')
            url = f"{API_URLS['GITLAB_API']}/projects/{encoded_project_path}/repository/tree"

            # 設定參數
            params = {
                "ref": branch,
                "per_page": 100  # 每頁顯示的最大檔案數
            }

            # 設定標頭
            headers = {
                "PRIVATE-TOKEN": access_token,
                "Accept": "application/json",  # 要求 JSON 回應
                "Cache-Control": "no-cache",  # 不使用快取
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 模擬瀏覽器
            }

            # 記錄 API 請求
            self.view.log_message(f"API 請求: {url}", level="INFO")
            self.view.log_message(f"API 參數: {params}", level="INFO")

            # 發送請求
            response = self.member_service.http_client.get(
                url,
                params=params,
                headers=headers
            )

            # 記錄 API 回應
            if isinstance(response, list):
                self.view.log_message(f"找到 {len(response)} 個檔案/目錄", level="INFO")
                for item in response[:10]:  # 只顯示前 10 個項目
                    item_info = f"類型: {item.get('type', 'N/A')}, 名稱: {item.get('name', 'N/A')}, 路徑: {item.get('path', 'N/A')}"
                    self.view.log_message(item_info, level="INFO")
            else:
                self.view.log_message(f"API 回應: {response}", level="INFO")
        except Exception as e:
            self.view.log_message(f"列出專案根目錄失敗: {e}", level="ERROR")

    def _get_game_id(self, game_name: str) -> Optional[str]:
        """從遊戲名稱中提取遊戲 ID"""
        # 嘗試從遊戲名稱提取 ID（假設格式為 "GameName_GameID"）
        parts = game_name.split('_')
        if len(parts) >= 2 and parts[-1].isdigit():
            return parts[-1]

        # 嘗試直接將整個名稱作為 ID（如果是數字）
        if game_name.isdigit():
            return game_name

        # 嘗試從括號中提取（假設格式為 "GameName (GameID)"）
        import re
        match = re.search(r'\((\d+)\)', game_name)
        if match:
            return match.group(1)

        # 嘗試從前六碼提取數字（假設格式為 "GameID_GameName"，如 "230001_金喜麻將"）
        match = re.search(r'^(\d{6})_', game_name)
        if match:
            return match.group(1)

        return None
