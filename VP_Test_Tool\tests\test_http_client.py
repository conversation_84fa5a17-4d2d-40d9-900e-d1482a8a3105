"""HttpClient 單元測試"""
import unittest
import json
import os
import sys
from unittest.mock import patch, MagicMock

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.http_client import HttpClient
from utils.exceptions import (
    APIConnectionError, APITimeoutError, APIResponseError
)

class TestHttpClient(unittest.TestCase):
    """HttpClient 單元測試類"""

    def setUp(self):
        """測試前準備"""
        # 創建 HttpClient 實例，禁用錯誤對話框
        self.client = HttpClient(show_error_dialog=False)

    def tearDown(self):
        """測試後清理"""
        # 確保 session 被關閉
        self.client.close()

    @patch('requests.Session.get')
    def test_get_success(self, mock_get):
        """測試 GET 請求成功的情況"""
        # 模擬成功的回應
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "data": [{"id": 1, "name": "test"}]}
        mock_get.return_value = mock_response

        # 發送請求
        result = self.client.get("http://example.com/api")

        # 驗證結果
        self.assertTrue(result["success"])
        self.assertEqual(result["data"][0]["name"], "test")
        mock_get.assert_called_once()

    @patch('requests.Session.get')
    def test_get_timeout(self, mock_get):
        """測試 GET 請求超時的情況"""
        # 模擬超時異常
        import requests
        mock_get.side_effect = requests.exceptions.Timeout("Connection timed out")

        # 發送請求
        result = self.client.get("http://example.com/api")

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("請求超時", result["error"])
        mock_get.assert_called_once()

    @patch('requests.Session.get')
    def test_get_connection_error(self, mock_get):
        """測試 GET 請求連接錯誤的情況"""
        # 模擬連接錯誤
        import requests
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection refused")

        # 發送請求
        result = self.client.get("http://example.com/api")

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("連接錯誤", result["error"])
        mock_get.assert_called_once()

    @patch('requests.Session.get')
    def test_get_http_error(self, mock_get):
        """測試 GET 請求 HTTP 錯誤的情況"""
        # 模擬 HTTP 錯誤
        import requests
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Client Error")
        mock_response.text = "Not Found"
        mock_get.return_value = mock_response

        # 發送請求
        result = self.client.get("http://example.com/api")

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("HTTP 錯誤", result["error"])
        mock_get.assert_called_once()

    @patch('requests.Session.post')
    def test_post_success(self, mock_post):
        """測試 POST 請求成功的情況"""
        # 模擬成功的回應
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "data": {"id": 1}}
        mock_post.return_value = mock_response

        # 發送請求
        result = self.client.post("http://example.com/api", json_data={"name": "test"})

        # 驗證結果
        self.assertTrue(result["success"])
        self.assertEqual(result["data"]["id"], 1)
        mock_post.assert_called_once()

    @patch('requests.Session.post')
    def test_post_timeout(self, mock_post):
        """測試 POST 請求超時的情況"""
        # 模擬超時異常
        import requests
        mock_post.side_effect = requests.exceptions.Timeout("Connection timed out")

        # 發送請求
        result = self.client.post("http://example.com/api", json_data={"name": "test"})

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("請求超時", result["error"])
        mock_post.assert_called_once()

    @patch('requests.Session.post')
    def test_post_connection_error(self, mock_post):
        """測試 POST 請求連接錯誤的情況"""
        # 模擬連接錯誤
        import requests
        mock_post.side_effect = requests.exceptions.ConnectionError("Connection refused")

        # 發送請求
        result = self.client.post("http://example.com/api", json_data={"name": "test"})

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("連接錯誤", result["error"])
        mock_post.assert_called_once()

    @patch('requests.Session.post')
    def test_post_http_error(self, mock_post):
        """測試 POST 請求 HTTP 錯誤的情況"""
        # 模擬 HTTP 錯誤
        import requests
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("500 Server Error")
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response

        # 發送請求
        result = self.client.post("http://example.com/api", json_data={"name": "test"})

        # 驗證結果
        self.assertFalse(result["success"])
        self.assertIn("HTTP 錯誤", result["error"])
        mock_post.assert_called_once()

    def test_cache(self):
        """測試快取功能"""
        # 模擬 get 方法
        with patch.object(self.client, 'get', return_value={"success": True, "data": [{"id": 1}]}) as mock_get:
            # 第一次調用
            result1 = self.client.get("http://example.com/api", use_cache=True)
            # 第二次調用
            result2 = self.client.get("http://example.com/api", use_cache=True)

            # 驗證結果
            self.assertEqual(result1, result2)
            mock_get.assert_called_once()  # 只應該被調用一次

    def test_clear_cache(self):
        """測試清除快取功能"""
        # 模擬 get 方法
        with patch.object(self.client, 'get', return_value={"success": True, "data": [{"id": 1}]}) as mock_get:
            # 第一次調用
            self.client.get("http://example.com/api", use_cache=True)
            # 清除快取
            self.client.clear_cache()
            # 第二次調用
            self.client.get("http://example.com/api", use_cache=True)

            # 驗證結果
            self.assertEqual(mock_get.call_count, 2)  # 應該被調用兩次

    def test_with_statement(self):
        """測試 with 語句"""
        with HttpClient(show_error_dialog=False) as client:
            # 模擬 get 方法
            with patch.object(client, 'get', return_value={"success": True}) as mock_get:
                result = client.get("http://example.com/api")
                self.assertTrue(result["success"])

        # with 語句結束後，session 應該被關閉
        self.assertFalse(hasattr(client, 'session') or client.session is None)

if __name__ == '__main__':
    unittest.main()
