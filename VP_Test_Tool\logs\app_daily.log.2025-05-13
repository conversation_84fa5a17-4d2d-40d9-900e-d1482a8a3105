2025-05-13 10:04:56,426 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 10:04:56,526 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 10:04:56,527 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 10:04:56,527 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 10:04:56,531 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 44287.21 MB, 使用率 32.2%
2025-05-13 10:04:56,532 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.27 GB, 使用率 95.7%
2025-05-13 10:04:56,596 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 10:04:56,656 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 10:04:56,721 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 10:04:56,884 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 10:04:56,885 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 10:04:59,139 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 10:04:59,255 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 10:04:59,322 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 10:04:59,323 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 10:04:59,374 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 10:04:59,374 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 10:05:00,858 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 10:05:00,860 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 10:05:01,193 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 10:05:01,194 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 10:05:01,249 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:19:23,764 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:19:23,776 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:19:23,777 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:23,777 - INFO - 34544 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:19:23,801 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-13 14:19:23,802 - WARNING - 34544 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:23,802 - ERROR - 34544 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:37,301 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:19:37,391 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:19:37,396 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38692.13 MB, 使用率 40.8%
2025-05-13 14:19:37,396 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:19:37,459 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:19:37,517 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:19:37,579 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:19:37,772 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:19:37,772 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:19:39,164 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:19:39,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:19:39,304 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:39,305 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:39,370 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:39,370 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:41,246 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:19:41,246 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:19:41,524 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:19:41,524 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:19:41,577 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:19:58,961 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:19:58,961 - WARNING - 15052 - MainThread - app - enhanced_logger.py:245 - 使用啟動畫面初始化失敗: list index out of range，使用直接初始化
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:19:58,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:19:58,967 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38749.76 MB, 使用率 40.7%
2025-05-13 14:19:58,967 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:19:58,969 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:19:59,138 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:19:59,139 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:19:59,380 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:19:59,385 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:19:59,386 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:59,386 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:19:59,387 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:19:59,387 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:20:01,161 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:20:01,161 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:20:01,412 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:20:01,413 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:20:01,413 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:20:01,426 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:20:01,428 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功初始化鍵盤快捷鍵管理器
2025-05-13 14:20:01,428 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:20:01,431 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 成功添加功能檢測按鈕
2025-05-13 14:25:03,291 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:03,291 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:03,299 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:03,299 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:03,300 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:03,300 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止資源監控
2025-05-13 14:25:04,311 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:05,321 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:05,322 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已取消所有待處理的 after 調用和閃爍效果
2025-05-13 14:25:05,350 - WARNING - 15052 - MainThread - app - enhanced_logger.py:245 - 關閉視窗時發生異常: can't delete Tcl command
2025-05-13 14:25:05,358 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:05,358 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:05,365 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:05,366 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:05,366 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:06,308 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:06,309 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:06,315 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:06,316 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:06,956 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:06,956 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:06,962 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:06,963 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:06,963 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,236 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,237 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,242 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,243 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,244 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,469 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,469 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,476 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,477 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,477 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:07,662 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:07,663 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:07,669 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:07,670 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:10,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:10,252 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:10,258 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:10,258 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:10,259 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:14,882 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 應用程式正在關閉...
2025-05-13 14:25:14,884 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有非守護線程
2025-05-13 14:25:14,889 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:14,890 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 正在停止 AccountController 線程
2025-05-13 14:25:14,890 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止所有 AccountController 線程
2025-05-13 14:25:14,891 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止內存監控
2025-05-13 14:25:14,892 - INFO - 15052 - MainThread - app - enhanced_logger.py:234 - 已停止網絡監控
2025-05-13 14:25:30,658 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:25:30,749 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:25:30,754 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38448.60 MB, 使用率 41.1%
2025-05-13 14:25:30,754 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:25:30,818 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:25:30,873 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:25:30,938 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:25:31,147 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:25:31,148 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:25:32,775 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:25:32,863 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:25:32,917 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:25:32,918 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:25:32,972 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:25:32,972 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:25:34,775 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:25:34,776 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:25:35,047 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:25:35,048 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:25:35,103 - INFO - 36684 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:28:46,616 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:28:46,729 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:28:46,729 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:28:46,730 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:28:46,735 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38386.61 MB, 使用率 41.2%
2025-05-13 14:28:46,736 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:28:46,801 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:28:46,857 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:28:46,921 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:28:47,114 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:28:47,114 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:28:48,612 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:28:48,702 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:28:48,766 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:28:48,767 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:28:48,834 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:28:48,834 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:28:50,649 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:28:50,650 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:28:50,921 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:28:50,922 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:28:50,973 - INFO - 35008 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:32:51,872 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-13 14:32:51,960 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-13 14:32:51,961 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-13 14:32:51,961 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-13 14:32:51,967 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 38322.00 MB, 使用率 41.3%
2025-05-13 14:32:51,968 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 40.26 GB, 使用率 95.7%
2025-05-13 14:32:52,029 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-13 14:32:52,086 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-13 14:32:52,150 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-13 14:32:52,340 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-13 14:32:52,341 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-13 14:32:53,829 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-13 14:32:53,918 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-13 14:32:53,970 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:32:53,970 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:32:54,026 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-13 14:32:54,027 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-13 14:32:55,804 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-13 14:32:55,805 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-13 14:32:56,072 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-13 14:32:56,072 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-13 14:32:56,124 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-13 14:43:00,027 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-13 14:43:00,037 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-13 14:43:00,037 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:43:00,037 - INFO - 22000 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-13 14:43:00,038 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-13 14:43:00,038 - WARNING - 22000 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:43:00,038 - ERROR - 22000 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
