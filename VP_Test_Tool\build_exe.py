"""
打包腳本 - 將 VP Test Tool 打包成 exe 文件
使用 PyInstaller 打包
"""
import os
import sys
import subprocess
import shutil

def main():
    """主函數"""
    print("開始打包 VP Test Tool 為 exe 文件...")
    
    # 檢查 PyInstaller 是否已安裝
    try:
        import PyInstaller
        print(f"已安裝 PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("PyInstaller 未安裝，正在安裝...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("PyInstaller 安裝完成")
    
    # 檢查圖示文件是否存在
    icon_path = os.path.join("assets", "icons", "vp_test_tool.ico")
    if not os.path.exists(icon_path):
        print(f"錯誤: 圖示文件 {icon_path} 不存在")
        return
    
    # 創建 dist 目錄（如果不存在）
    if not os.path.exists("dist"):
        os.makedirs("dist")
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--name=VP_Test_Tool",
        f"--icon={icon_path}",
        "--windowed",  # 不顯示控制台窗口
        "--onefile",   # 打包成單個 exe 文件
        "--clean",     # 清理臨時文件
        "--noconfirm", # 不詢問確認
        "--add-data=assets;assets",  # 添加資源文件
        "main.py"      # 主程序文件
    ]
    
    # 執行打包命令
    print("執行打包命令...")
    print(" ".join(cmd))
    subprocess.run(cmd, check=True)
    
    print("打包完成！")
    print(f"exe 文件位於: {os.path.abspath(os.path.join('dist', 'VP_Test_Tool.exe'))}")

if __name__ == "__main__":
    main()
