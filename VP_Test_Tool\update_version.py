"""更新版本號

此腳本用於更新應用程式的版本號。
"""
import os
import sys
import re
import argparse
import datetime

def update_version(major=None, minor=None, patch=None):
    """更新版本號
    
    Args:
        major (int): 主版本號
        minor (int): 次版本號
        patch (int): 修訂版本號
    """
    # 讀取當前版本號
    version_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'utils', 'version.py')
    with open(version_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 獲取當前版本號
    major_pattern = r'MAJOR\s*=\s*(\d+)'
    minor_pattern = r'MINOR\s*=\s*(\d+)'
    patch_pattern = r'PATCH\s*=\s*(\d+)'
    
    current_major = int(re.search(major_pattern, content).group(1))
    current_minor = int(re.search(minor_pattern, content).group(1))
    current_patch = int(re.search(patch_pattern, content).group(1))
    
    # 計算新版本號
    new_major = major if major is not None else current_major
    new_minor = minor if minor is not None else current_minor
    new_patch = patch if patch is not None else current_patch
    
    # 如果沒有指定任何版本號，則增加修訂版本號
    if major is None and minor is None and patch is None:
        new_patch = current_patch + 1
    
    # 更新版本號
    content = re.sub(major_pattern, f'MAJOR = {new_major}', content)
    content = re.sub(minor_pattern, f'MINOR = {new_minor}', content)
    content = re.sub(patch_pattern, f'PATCH = {new_patch}', content)
    
    # 更新發布日期
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    date_pattern = r'RELEASE_DATE\s*=\s*"([^"]+)"'
    content = re.sub(date_pattern, f'RELEASE_DATE = "{today}"', content)
    
    # 寫入文件
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"版本號已更新: {current_major}.{current_minor}.{current_patch} -> {new_major}.{new_minor}.{new_patch}")
    print(f"發布日期已更新: {today}")
    
    # 更新打包腳本中的版本號
    try:
        from update_version_in_scripts import main as update_scripts
        update_scripts()
    except ImportError:
        print("無法導入 update_version_in_scripts 模塊，請手動更新打包腳本中的版本號")
    
    # 更新 CHANGELOG.md
    try:
        update_changelog(f"{new_major}.{new_minor}.{new_patch}", today)
    except Exception as e:
        print(f"更新 CHANGELOG.md 失敗: {e}")

def update_changelog(version, date):
    """更新 CHANGELOG.md
    
    Args:
        version (str): 版本號
        date (str): 發布日期
    """
    changelog_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'CHANGELOG.md')
    
    # 讀取當前 CHANGELOG.md
    with open(changelog_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查版本號是否已存在
    if f"## V{version}" in content:
        print(f"CHANGELOG.md 中已存在版本 V{version}")
        return
    
    # 創建新版本的模板
    new_version_template = f"""# VP Test Tool 更新日誌

## V{version} ({date})

### 功能增強
- 

### 錯誤修復
- 

### 其他改進
- 

"""
    
    # 在文件開頭添加新版本
    if "# VP Test Tool 更新日誌" in content:
        content = content.replace("# VP Test Tool 更新日誌", new_version_template.rstrip())
    else:
        content = new_version_template + content
    
    # 寫入文件
    with open(changelog_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已在 CHANGELOG.md 中添加版本 V{version}")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='更新版本號')
    parser.add_argument('--major', type=int, help='主版本號')
    parser.add_argument('--minor', type=int, help='次版本號')
    parser.add_argument('--patch', type=int, help='修訂版本號')
    parser.add_argument('--version', type=str, help='完整版本號 (格式: x.y.z)')
    
    args = parser.parse_args()
    
    # 如果提供了完整版本號，解析它
    if args.version:
        try:
            major, minor, patch = map(int, args.version.split('.'))
            update_version(major, minor, patch)
        except ValueError:
            print(f"無效的版本號格式: {args.version}，應為 x.y.z")
            sys.exit(1)
    else:
        # 否則使用單獨的版本號參數
        update_version(args.major, args.minor, args.patch)

if __name__ == "__main__":
    main()
