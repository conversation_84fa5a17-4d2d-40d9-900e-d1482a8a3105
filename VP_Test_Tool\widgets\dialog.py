"""對話框元件"""
import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable, Dict, Any
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from widgets.modern_button import ModernButton

class Dialog(tk.Toplevel):
    """對話框元件
    
    提供更現代化的對話框功能，支援不同類型的對話框和自訂內容。
    
    Args:
        parent: 父元件
        title: 對話框標題
        message: 對話框訊息
        dialog_type: 對話框類型，可以是 'info', 'warning', 'error', 'confirm', 'custom'
        on_ok: 確定按鈕的回調函數
        on_cancel: 取消按鈕的回調函數
        **kwargs: 其他 tk.Toplevel 參數
    """
    
    def __init__(
        self, 
        parent, 
        title: str, 
        message: str, 
        dialog_type: str = "info", 
        on_ok: Optional[Callable[[], None]] = None, 
        on_cancel: Optional[Callable[[], None]] = None, 
        **kwargs
    ):
        super().__init__(parent, **kwargs)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.title(title)
        self.message = message
        self.dialog_type = dialog_type
        self.on_ok = on_ok
        self.on_cancel = on_cancel
        self.result = None
        
        # 設定對話框屬性
        self.transient(parent)
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
        # 設定對話框位置
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 設定對話框樣式
        self.configure(background=self.theme_manager.get_color("surface"))
        
        # 初始化 UI
        self._init_ui()
        
        # 設定焦點
        self.ok_button.focus_set()
        
        # 設定為模態對話框
        self.grab_set()
        
    def _init_ui(self):
        """初始化 UI"""
        # 建立主要框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 建立圖示和訊息框架
        icon_message_frame = ttk.Frame(main_frame)
        icon_message_frame.pack(fill="x", pady=10)
        
        # 取得對話框圖示
        icon_map = {
            "info": IconManager.get("info"),
            "warning": IconManager.get("warning"),
            "error": IconManager.get("error"),
            "confirm": IconManager.get("question"),
            "custom": IconManager.get("settings")
        }
        
        icon = icon_map.get(self.dialog_type, icon_map["info"])
        
        # 建立圖示標籤
        icon_label = ttk.Label(
            icon_message_frame, 
            text=icon, 
            font=("TkDefaultFont", 24),
            foreground=self.theme_manager.get_color(self.dialog_type if self.dialog_type in ["info", "warning", "error"] else "primary")
        )
        icon_label.pack(side="left", padx=(0, 10))
        
        # 建立訊息標籤
        message_label = ttk.Label(
            icon_message_frame, 
            text=self.message, 
            font=self.theme_manager.get_font("text"),
            wraplength=300,
            justify="left"
        )
        message_label.pack(side="left", fill="x", expand=True)
        
        # 建立自訂內容框架
        if self.dialog_type == "custom":
            self.content_frame = ttk.Frame(main_frame)
            self.content_frame.pack(fill="both", expand=True, pady=10)
        
        # 建立按鈕框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(20, 0))
        
        # 建立確定按鈕
        self.ok_button = ModernButton(
            button_frame, 
            text="確定", 
            command=self._on_ok, 
            button_type="primary"
        )
        self.ok_button.pack(side="right", padx=5)
        
        # 建立取消按鈕 (僅在確認對話框中顯示)
        if self.dialog_type in ["confirm", "custom"]:
            self.cancel_button = ModernButton(
                button_frame, 
                text="取消", 
                command=self._on_cancel, 
                button_type="secondary"
            )
            self.cancel_button.pack(side="right", padx=5)
        
        # 綁定鍵盤事件
        self.bind("<Return>", lambda event: self._on_ok())
        self.bind("<Escape>", lambda event: self._on_cancel())
        
    def _on_ok(self):
        """確定按鈕事件處理"""
        self.result = True
        
        # 呼叫回調函數
        if self.on_ok:
            self.on_ok()
            
        self.destroy()
        
    def _on_cancel(self):
        """取消按鈕事件處理"""
        self.result = False
        
        # 呼叫回調函數
        if self.on_cancel:
            self.on_cancel()
            
        self.destroy()
        
    def get_content_frame(self) -> ttk.Frame:
        """取得內容框架
        
        Returns:
            ttk.Frame: 內容框架
        """
        if self.dialog_type != "custom":
            raise ValueError("只有自訂對話框才能取得內容框架")
            
        return self.content_frame
        
    @staticmethod
    def show_info(parent, title: str, message: str) -> bool:
        """顯示資訊對話框
        
        Args:
            parent: 父元件
            title: 對話框標題
            message: 對話框訊息
            
        Returns:
            bool: 是否點擊確定按鈕
        """
        dialog = Dialog(parent, title, message, dialog_type="info")
        dialog.wait_window()
        return dialog.result
        
    @staticmethod
    def show_warning(parent, title: str, message: str) -> bool:
        """顯示警告對話框
        
        Args:
            parent: 父元件
            title: 對話框標題
            message: 對話框訊息
            
        Returns:
            bool: 是否點擊確定按鈕
        """
        dialog = Dialog(parent, title, message, dialog_type="warning")
        dialog.wait_window()
        return dialog.result
        
    @staticmethod
    def show_error(parent, title: str, message: str) -> bool:
        """顯示錯誤對話框
        
        Args:
            parent: 父元件
            title: 對話框標題
            message: 對話框訊息
            
        Returns:
            bool: 是否點擊確定按鈕
        """
        dialog = Dialog(parent, title, message, dialog_type="error")
        dialog.wait_window()
        return dialog.result
        
    @staticmethod
    def show_confirm(parent, title: str, message: str) -> bool:
        """顯示確認對話框
        
        Args:
            parent: 父元件
            title: 對話框標題
            message: 對話框訊息
            
        Returns:
            bool: 是否點擊確定按鈕
        """
        dialog = Dialog(parent, title, message, dialog_type="confirm")
        dialog.wait_window()
        return dialog.result
        
    @staticmethod
    def show_custom(parent, title: str, message: str, setup_func: Callable[[ttk.Frame], None]) -> Dict[str, Any]:
        """顯示自訂對話框
        
        Args:
            parent: 父元件
            title: 對話框標題
            message: 對話框訊息
            setup_func: 設定內容框架的函數
            
        Returns:
            Dict[str, Any]: 對話框結果
        """
        dialog = Dialog(parent, title, message, dialog_type="custom")
        setup_func(dialog.get_content_frame())
        dialog.wait_window()
        return {"result": dialog.result}
