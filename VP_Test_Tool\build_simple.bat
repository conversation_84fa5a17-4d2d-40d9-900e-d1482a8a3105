@echo off
setlocal

REM VP Test Tool Simple Build Script
REM Double-click to run or execute from command line

title VP Test Tool V2.6.1 Build System

echo.
echo ================================================================================
echo VP Test Tool V2.6.1 Build System
echo ================================================================================
echo.

REM Check Python
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Found: %PYTHON_VERSION%

REM Check required files
echo.
echo Checking required files...
if not exist "main.py" (
    echo ERROR: main.py not found
    pause
    exit /b 1
)
if not exist "setup_cx_freeze_stable.py" (
    echo ERROR: setup_cx_freeze_stable.py not found
    pause
    exit /b 1
)
echo Required files found

REM Show menu
:MENU
echo.
echo ================================================================================
echo Please select an option:
echo ================================================================================
echo.
echo 1. Check environment and dependencies
echo 2. Install/update dependencies
echo 3. Clean old build files
echo 4. Build with cx_Freeze (stable)
echo 5. Test build result
echo 6. Full build process (recommended)
echo 7. Show help
echo 0. Exit
echo.

set /p CHOICE="Enter option (0-7): "

if "%CHOICE%"=="0" goto EXIT
if "%CHOICE%"=="1" goto CHECK_ENV
if "%CHOICE%"=="2" goto INSTALL_DEPS
if "%CHOICE%"=="3" goto CLEAN
if "%CHOICE%"=="4" goto BUILD
if "%CHOICE%"=="5" goto TEST
if "%CHOICE%"=="6" goto FULL_BUILD
if "%CHOICE%"=="7" goto HELP

echo Invalid option, please try again
goto MENU

:CHECK_ENV
echo.
echo Checking environment...
python setup_cx_freeze_stable.py --help >nul 2>&1
if errorlevel 1 (
    echo Environment check completed with warnings
) else (
    echo Environment check completed
)
pause
goto MENU

:INSTALL_DEPS
echo.
echo Installing dependencies...
if exist "requirements.txt" (
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        goto MENU
    )
    echo Dependencies installed successfully
) else (
    echo WARNING: requirements.txt not found
)
pause
goto MENU

:CLEAN
echo.
echo Cleaning old build files...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
echo Cleanup completed
pause
goto MENU

:BUILD
echo.
echo Building with cx_Freeze (stable)...
echo This may take several minutes, please wait...
python setup_cx_freeze_stable.py build
if errorlevel 1 (
    echo ERROR: Build failed
    echo Please check the error messages above
    pause
    goto MENU
)
echo.
echo Build completed successfully!
echo Output: dist\cx_freeze_stable\VP_Test_Tool.exe
pause
goto MENU

:TEST
echo.
echo Testing build result...
if exist "test_build.py" (
    python test_build.py
    if errorlevel 1 (
        echo Test completed with warnings
    ) else (
        echo All tests passed!
    )
) else (
    echo WARNING: test_build.py not found, skipping tests
)
pause
goto MENU

:FULL_BUILD
echo.
echo Starting full build process...
echo This will clean, build, and test the application
echo.

REM Clean
echo Step 1/3: Cleaning...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

REM Build
echo Step 2/3: Building...
python setup_cx_freeze_stable.py build
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    goto MENU
)

REM Test
echo Step 3/3: Testing...
if exist "test_build.py" (
    python test_build.py
    if errorlevel 1 (
        echo Build completed with test warnings
    ) else (
        echo Full build process completed successfully!
    )
) else (
    echo Build completed (test skipped)
)

echo.
echo ================================================================================
echo Build Summary
echo ================================================================================
echo Output location: dist\cx_freeze_stable\
echo Main executable: dist\cx_freeze_stable\VP_Test_Tool.exe
echo.
echo Recommendations:
echo - Distribute the entire dist\cx_freeze_stable folder
echo - Ensure target systems have Visual C++ Runtime
echo - Add to antivirus whitelist if needed
echo.
pause
goto MENU

:HELP
echo.
echo ================================================================================
echo VP Test Tool Build System Help
echo ================================================================================
echo.
echo Build Process:
echo 1. Check environment - Verify Python and dependencies
echo 2. Install dependencies - Install required Python packages
echo 3. Clean - Remove old build files
echo 4. Build - Create executable with cx_Freeze
echo 5. Test - Verify build integrity
echo 6. Full build - Execute all steps automatically
echo.
echo Output:
echo - dist\cx_freeze_stable\ - Complete application folder
echo - dist\cx_freeze_stable\VP_Test_Tool.exe - Main executable
echo.
echo Troubleshooting:
echo - If build fails, try option 2 to install dependencies
echo - If Python not found, ensure Python 3.8+ is installed
echo - If exe doesn't run, check antivirus software
echo.
echo For detailed documentation, see PACKAGING_GUIDE.md
echo.
pause
goto MENU

:EXIT
echo.
echo Thank you for using VP Test Tool Build System!
echo.
pause
exit /b 0
