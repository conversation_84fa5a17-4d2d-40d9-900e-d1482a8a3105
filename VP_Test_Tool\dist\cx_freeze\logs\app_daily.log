2025-05-28 13:57:49,790 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-28 13:57:49,934 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-28 13:57:49,935 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-28 13:57:49,935 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-28 13:57:49,941 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36487.04 MB, 使用率 44.1%
2025-05-28 13:57:49,942 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.84 GB, 使用率 95.7%
2025-05-28 13:57:50,010 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-28 13:57:50,065 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-28 13:57:50,127 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-28 13:57:50,274 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-28 13:57:51,227 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-28 13:57:51,311 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-28 13:57:51,362 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 13:57:51,363 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 13:57:51,421 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-28 13:57:51,421 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-28 13:57:53,097 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-28 13:57:53,098 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-28 13:57:53,406 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-28 13:57:53,406 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-28 13:57:53,412 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化環境管理控制器
2025-05-28 13:57:53,426 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功初始化 IP 切換控制器
2025-05-28 13:57:53,494 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, data_processing, gui, image_processing
2025-05-28 14:48:10,405 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-28 14:48:10,426 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-28 14:48:10,427 - WARNING - 17572 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 14:48:10,427 - INFO - 17572 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-28 14:48:10,493 - WARNING - 17572 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-28 14:48:10,493 - WARNING - 17572 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-28 14:48:10,493 - ERROR - 17572 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "main.py", line 788, in main
  File "D:/Program Files/Python311/Lib/tkinter/__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
