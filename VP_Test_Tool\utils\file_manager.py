"""文件管理模塊"""
import os
import logging
import csv
import json
import tempfile
import shutil
from typing import List, Dict, Any, Optional, Union, TextIO, BinaryIO
from contextlib import contextmanager
from .exceptions import FileReadError, FileWriteError, FileFormatError

logger = logging.getLogger(__name__)

class FileManager:
    """文件管理類，用於處理文件操作"""

    @staticmethod
    @contextmanager
    def open_file(file_path: str, mode: str = 'r', encoding: str = 'utf-8') -> TextIO:
        """安全打開文件的上下文管理器

        Args:
            file_path: 文件路徑
            mode: 打開模式
            encoding: 文件編碼

        Yields:
            TextIO: 文件對象

        Raises:
            FileReadError: 讀取文件失敗
            FileWriteError: 寫入文件失敗
        """
        file = None
        try:
            file = open(file_path, mode, encoding=encoding)
            yield file
        except IOError as e:
            if 'r' in mode:
                logger.error(f"讀取文件 {file_path} 失敗: {e}")
                raise FileReadError(f"讀取文件 {file_path} 失敗: {e}")
            else:
                logger.error(f"寫入文件 {file_path} 失敗: {e}")
                raise FileWriteError(f"寫入文件 {file_path} 失敗: {e}")
        finally:
            if file:
                file.close()

    @staticmethod
    @contextmanager
    def open_binary_file(file_path: str, mode: str = 'rb') -> BinaryIO:
        """安全打開二進制文件的上下文管理器

        Args:
            file_path: 文件路徑
            mode: 打開模式

        Yields:
            BinaryIO: 文件對象

        Raises:
            FileReadError: 讀取文件失敗
            FileWriteError: 寫入文件失敗
        """
        file = None
        try:
            file = open(file_path, mode)
            yield file
        except IOError as e:
            if 'r' in mode:
                logger.error(f"讀取二進制文件 {file_path} 失敗: {e}")
                raise FileReadError(f"讀取二進制文件 {file_path} 失敗: {e}")
            else:
                logger.error(f"寫入二進制文件 {file_path} 失敗: {e}")
                raise FileWriteError(f"寫入二進制文件 {file_path} 失敗: {e}")
        finally:
            if file:
                file.close()

    @classmethod
    def read_text_file(cls, file_path: str, encoding: str = 'utf-8') -> str:
        """讀取文本文件

        Args:
            file_path: 文件路徑
            encoding: 文件編碼

        Returns:
            str: 文件內容

        Raises:
            FileReadError: 讀取文件失敗
        """
        try:
            with cls.open_file(file_path, 'r', encoding) as file:
                return file.read()
        except FileReadError:
            raise
        except Exception as e:
            logger.error(f"讀取文本文件 {file_path} 失敗: {e}")
            raise FileReadError(f"讀取文本文件 {file_path} 失敗: {e}")

    @classmethod
    def write_text_file(cls, file_path: str, content: str, encoding: str = 'utf-8') -> None:
        """寫入文本文件

        Args:
            file_path: 文件路徑
            content: 文件內容
            encoding: 文件編碼

        Raises:
            FileWriteError: 寫入文件失敗
        """
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            with cls.open_file(file_path, 'w', encoding) as file:
                file.write(content)
        except FileWriteError:
            raise
        except Exception as e:
            logger.error(f"寫入文本文件 {file_path} 失敗: {e}")
            raise FileWriteError(f"寫入文本文件 {file_path} 失敗: {e}")

    @classmethod
    def read_binary_file(cls, file_path: str) -> bytes:
        """讀取二進制文件

        Args:
            file_path: 文件路徑

        Returns:
            bytes: 文件內容

        Raises:
            FileReadError: 讀取文件失敗
        """
        try:
            with cls.open_binary_file(file_path, 'rb') as file:
                return file.read()
        except FileReadError:
            raise
        except Exception as e:
            logger.error(f"讀取二進制文件 {file_path} 失敗: {e}")
            raise FileReadError(f"讀取二進制文件 {file_path} 失敗: {e}")

    @classmethod
    def write_binary_file(cls, file_path: str, content: bytes) -> None:
        """寫入二進制文件

        Args:
            file_path: 文件路徑
            content: 文件內容

        Raises:
            FileWriteError: 寫入文件失敗
        """
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            with cls.open_binary_file(file_path, 'wb') as file:
                file.write(content)
        except FileWriteError:
            raise
        except Exception as e:
            logger.error(f"寫入二進制文件 {file_path} 失敗: {e}")
            raise FileWriteError(f"寫入二進制文件 {file_path} 失敗: {e}")

    @classmethod
    def read_csv_file(cls, file_path: str, has_header: bool = True, encoding: str = 'utf-8') -> List[Dict[str, str]]:
        """讀取 CSV 文件

        Args:
            file_path: 文件路徑
            has_header: 是否有標題行
            encoding: 文件編碼

        Returns:
            List[Dict[str, str]]: CSV 數據

        Raises:
            FileReadError: 讀取文件失敗
            FileFormatError: 文件格式錯誤
        """
        try:
            with cls.open_file(file_path, 'r', encoding) as file:
                if has_header:
                    reader = csv.DictReader(file)
                    return list(reader)
                else:
                    reader = csv.reader(file)
                    data = list(reader)
                    return [dict(zip([f"column_{i}" for i in range(len(row))], row)) for row in data]
        except FileReadError:
            raise
        except csv.Error as e:
            logger.error(f"CSV 文件 {file_path} 格式錯誤: {e}")
            raise FileFormatError(f"CSV 文件 {file_path} 格式錯誤: {e}")
        except Exception as e:
            logger.error(f"讀取 CSV 文件 {file_path} 失敗: {e}")
            raise FileReadError(f"讀取 CSV 文件 {file_path} 失敗: {e}")

    @classmethod
    def write_csv_file(cls, file_path: str, data: List[Dict[str, Any]], fieldnames: Optional[List[str]] = None, encoding: str = 'utf-8') -> None:
        """寫入 CSV 文件

        Args:
            file_path: 文件路徑
            data: CSV 數據
            fieldnames: 字段名列表，如果為 None，則使用第一行數據的鍵
            encoding: 文件編碼

        Raises:
            FileWriteError: 寫入文件失敗
        """
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            with cls.open_file(file_path, 'w', encoding) as file:
                if not fieldnames and data:
                    fieldnames = list(data[0].keys())
                
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
        except FileWriteError:
            raise
        except csv.Error as e:
            logger.error(f"CSV 文件 {file_path} 格式錯誤: {e}")
            raise FileFormatError(f"CSV 文件 {file_path} 格式錯誤: {e}")
        except Exception as e:
            logger.error(f"寫入 CSV 文件 {file_path} 失敗: {e}")
            raise FileWriteError(f"寫入 CSV 文件 {file_path} 失敗: {e}")

    @classmethod
    def read_json_file(cls, file_path: str, encoding: str = 'utf-8') -> Dict[str, Any]:
        """讀取 JSON 文件

        Args:
            file_path: 文件路徑
            encoding: 文件編碼

        Returns:
            Dict[str, Any]: JSON 數據

        Raises:
            FileReadError: 讀取文件失敗
            FileFormatError: 文件格式錯誤
        """
        try:
            with cls.open_file(file_path, 'r', encoding) as file:
                return json.load(file)
        except FileReadError:
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON 文件 {file_path} 格式錯誤: {e}")
            raise FileFormatError(f"JSON 文件 {file_path} 格式錯誤: {e}")
        except Exception as e:
            logger.error(f"讀取 JSON 文件 {file_path} 失敗: {e}")
            raise FileReadError(f"讀取 JSON 文件 {file_path} 失敗: {e}")

    @classmethod
    def write_json_file(cls, file_path: str, data: Dict[str, Any], encoding: str = 'utf-8', indent: int = 4) -> None:
        """寫入 JSON 文件

        Args:
            file_path: 文件路徑
            data: JSON 數據
            encoding: 文件編碼
            indent: 縮進空格數

        Raises:
            FileWriteError: 寫入文件失敗
        """
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            with cls.open_file(file_path, 'w', encoding) as file:
                json.dump(data, file, ensure_ascii=False, indent=indent)
        except FileWriteError:
            raise
        except Exception as e:
            logger.error(f"寫入 JSON 文件 {file_path} 失敗: {e}")
            raise FileWriteError(f"寫入 JSON 文件 {file_path} 失敗: {e}")

    @staticmethod
    def ensure_directory(directory_path: str) -> None:
        """確保目錄存在

        Args:
            directory_path: 目錄路徑

        Raises:
            FileWriteError: 創建目錄失敗
        """
        try:
            os.makedirs(directory_path, exist_ok=True)
        except Exception as e:
            logger.error(f"創建目錄 {directory_path} 失敗: {e}")
            raise FileWriteError(f"創建目錄 {directory_path} 失敗: {e}")

    @staticmethod
    def file_exists(file_path: str) -> bool:
        """檢查文件是否存在

        Args:
            file_path: 文件路徑

        Returns:
            bool: 文件是否存在
        """
        return os.path.isfile(file_path)

    @staticmethod
    def directory_exists(directory_path: str) -> bool:
        """檢查目錄是否存在

        Args:
            directory_path: 目錄路徑

        Returns:
            bool: 目錄是否存在
        """
        return os.path.isdir(directory_path)

    @staticmethod
    def list_files(directory_path: str, pattern: str = "*") -> List[str]:
        """列出目錄中的文件

        Args:
            directory_path: 目錄路徑
            pattern: 文件匹配模式

        Returns:
            List[str]: 文件列表

        Raises:
            FileReadError: 讀取目錄失敗
        """
        try:
            import glob
            return glob.glob(os.path.join(directory_path, pattern))
        except Exception as e:
            logger.error(f"列出目錄 {directory_path} 中的文件失敗: {e}")
            raise FileReadError(f"列出目錄 {directory_path} 中的文件失敗: {e}")

    @staticmethod
    def remove_file(file_path: str) -> None:
        """刪除文件

        Args:
            file_path: 文件路徑

        Raises:
            FileWriteError: 刪除文件失敗
        """
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
        except Exception as e:
            logger.error(f"刪除文件 {file_path} 失敗: {e}")
            raise FileWriteError(f"刪除文件 {file_path} 失敗: {e}")

    @staticmethod
    def remove_directory(directory_path: str, recursive: bool = False) -> None:
        """刪除目錄

        Args:
            directory_path: 目錄路徑
            recursive: 是否遞歸刪除

        Raises:
            FileWriteError: 刪除目錄失敗
        """
        try:
            if os.path.isdir(directory_path):
                if recursive:
                    shutil.rmtree(directory_path)
                else:
                    os.rmdir(directory_path)
        except Exception as e:
            logger.error(f"刪除目錄 {directory_path} 失敗: {e}")
            raise FileWriteError(f"刪除目錄 {directory_path} 失敗: {e}")

    @staticmethod
    @contextmanager
    def temp_file(suffix: str = "", prefix: str = "tmp", content: Optional[Union[str, bytes]] = None, mode: str = "w+", encoding: str = "utf-8") -> TextIO:
        """創建臨時文件的上下文管理器

        Args:
            suffix: 文件後綴
            prefix: 文件前綴
            content: 文件內容
            mode: 打開模式
            encoding: 文件編碼

        Yields:
            TextIO: 臨時文件對象

        Raises:
            FileWriteError: 創建臨時文件失敗
        """
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        file = None
        try:
            os.close(fd)  # 關閉文件描述符
            
            # 寫入內容
            if content is not None:
                if isinstance(content, str):
                    with open(path, "w", encoding=encoding) as f:
                        f.write(content)
                else:
                    with open(path, "wb") as f:
                        f.write(content)
            
            # 重新打開文件
            file = open(path, mode, encoding=encoding if "b" not in mode else None)
            yield file
        except Exception as e:
            logger.error(f"創建臨時文件失敗: {e}")
            raise FileWriteError(f"創建臨時文件失敗: {e}")
        finally:
            if file:
                file.close()
            try:
                os.remove(path)
            except Exception as e:
                logger.warning(f"刪除臨時文件 {path} 失敗: {e}")

    @staticmethod
    @contextmanager
    def temp_directory(suffix: str = "", prefix: str = "tmp") -> str:
        """創建臨時目錄的上下文管理器

        Args:
            suffix: 目錄後綴
            prefix: 目錄前綴

        Yields:
            str: 臨時目錄路徑

        Raises:
            FileWriteError: 創建臨時目錄失敗
        """
        path = tempfile.mkdtemp(suffix=suffix, prefix=prefix)
        try:
            yield path
        except Exception as e:
            logger.error(f"創建臨時目錄失敗: {e}")
            raise FileWriteError(f"創建臨時目錄失敗: {e}")
        finally:
            try:
                shutil.rmtree(path)
            except Exception as e:
                logger.warning(f"刪除臨時目錄 {path} 失敗: {e}")

# 創建全局文件管理器實例
file_manager = FileManager()
