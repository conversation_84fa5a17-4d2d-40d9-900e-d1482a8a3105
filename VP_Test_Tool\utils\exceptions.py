"""自定義異常模塊"""

class VPToolException(Exception):
    """VP Test Tool 基礎異常類"""
    pass

# API 相關異常
class APIException(VPToolException):
    """API 相關異常的基礎類"""
    pass

# 為了兼容性，添加 APIError 作為 APIException 的別名
APIError = APIException

class APIConnectionError(APIException):
    """API 連接錯誤"""
    def __init__(self, message="API 連接失敗", *args, **kwargs):
        super().__init__(message, *args, **kwargs)

class APITimeoutError(APIException):
    """API 請求超時"""
    def __init__(self, message="API 請求超時", *args, **kwargs):
        super().__init__(message, *args, **kwargs)

class APIResponseError(APIException):
    """API 回應錯誤"""
    def __init__(self, message="API 回應錯誤", status_code=None, response=None, *args, **kwargs):
        super().__init__(message, *args, **kwargs)
        self.status_code = status_code
        self.response = response

class APIAuthError(APIException):
    """API 認證錯誤"""
    def __init__(self, message="API 認證失敗", status_code=None, response=None, *args, **kwargs):
        super().__init__(message, *args, **kwargs)
        self.status_code = status_code
        self.response = response

class APIRateLimitError(APIException):
    """API 速率限制錯誤"""
    def __init__(self, message="API 請求過於頻繁", status_code=None, response=None, *args, **kwargs):
        super().__init__(message, *args, **kwargs)
        self.status_code = status_code
        self.response = response

# 資源操作異常
class ResourceException(VPToolException):
    """資源操作相關異常的基礎類"""
    pass

class ResourceUpdateError(ResourceException):
    """資源更新錯誤"""
    pass

class ResourceQueryError(ResourceException):
    """資源查詢錯誤"""
    pass

class ResourceNotFoundError(ResourceException):
    """資源不存在錯誤"""
    pass

# 會員操作異常
class MemberException(VPToolException):
    """會員操作相關異常的基礎類"""
    pass

class MemberNotFoundError(MemberException):
    """會員不存在錯誤"""
    pass

class MemberCreateError(MemberException):
    """會員創建錯誤"""
    pass

class MemberUpdateError(MemberException):
    """會員更新錯誤"""
    pass

# 文件操作異常
class FileException(VPToolException):
    """文件操作相關異常的基礎類"""
    pass

class FileReadError(FileException):
    """文件讀取錯誤"""
    pass

class FileWriteError(FileException):
    """文件寫入錯誤"""
    pass

class FileFormatError(FileException):
    """文件格式錯誤"""
    pass

# 配置相關異常
class ConfigException(VPToolException):
    """配置相關異常的基礎類"""
    pass

class ConfigReadError(ConfigException):
    """配置讀取錯誤"""
    pass

class ConfigWriteError(ConfigException):
    """配置寫入錯誤"""
    pass

# 線程相關異常
class ThreadException(VPToolException):
    """線程相關異常的基礎類"""
    pass

class ThreadCreateError(ThreadException):
    """線程創建錯誤"""
    pass

class ThreadTerminateError(ThreadException):
    """線程終止錯誤"""
    pass

# RNG 相關異常
class RNGException(VPToolException):
    """RNG 相關異常的基礎類"""
    pass

class RNGSetError(RNGException):
    """RNG 設置錯誤"""
    pass

class RNGFormatError(RNGException):
    """RNG 格式錯誤"""
    pass

# 代理商相關異常
class AgentException(VPToolException):
    """代理商相關異常的基礎類"""
    pass

class AgentNotFoundError(AgentException):
    """代理商不存在錯誤"""
    pass

class SubAgentNotFoundError(AgentException):
    """子代理商不存在錯誤"""
    pass

# 驗證相關異常
class ValidationException(VPToolException):
    """驗證相關異常的基礎類"""
    pass

class ValidationError(ValidationException):
    """驗證錯誤"""
    def __init__(self, message="驗證失敗", field=None, *args, **kwargs):
        if field:
            message = f"{field}: {message}"
        super().__init__(message, *args, **kwargs)
        self.field = field

# 網絡相關異常
class NetworkException(VPToolException):
    """網絡相關異常的基礎類"""
    pass

class NetworkConnectionError(NetworkException):
    """網絡連接錯誤"""
    def __init__(self, message="網絡連接失敗", host=None, port=None, *args, **kwargs):
        if host and port:
            message = f"網絡連接失敗 ({host}:{port}): {message}"
        elif host:
            message = f"網絡連接失敗 ({host}): {message}"
        super().__init__(message, *args, **kwargs)
        self.host = host
        self.port = port

class NetworkTimeoutError(NetworkException):
    """網絡超時錯誤"""
    def __init__(self, message="網絡連接超時", host=None, timeout=None, *args, **kwargs):
        if host and timeout:
            message = f"網絡連接超時 ({host}, {timeout}秒): {message}"
        elif host:
            message = f"網絡連接超時 ({host}): {message}"
        elif timeout:
            message = f"網絡連接超時 ({timeout}秒): {message}"
        super().__init__(message, *args, **kwargs)
        self.host = host
        self.timeout = timeout

class NetworkDNSError(NetworkException):
    """DNS 解析錯誤"""
    def __init__(self, message="DNS 解析失敗", host=None, *args, **kwargs):
        if host:
            message = f"DNS 解析失敗 ({host}): {message}"
        super().__init__(message, *args, **kwargs)
        self.host = host
