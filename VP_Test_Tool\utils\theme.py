"""主題管理系統

此模組提供應用程式的主題管理功能，包含顏色、字體和樣式的定義。
"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any
import json
import os
from .logger import setup_logger

# 設定日誌
logger = setup_logger(__name__)

class ThemeManager:
    """主題管理器類別

    負責管理應用程式的主題設定，包含顏色、字體和樣式。
    支援淺色和深色主題，並可以從設定檔載入自訂主題。
    """

    # 單例模式
    _instance = None

    # 淺色主題
    LIGHT_THEME = {
        "primary": "#1976D2",      # 主要顏色 (藍色)
        "secondary": "#43A047",    # 次要顏色 (綠色)
        "accent": "#FF5722",       # 強調顏色 (橙色)
        "danger": "#F44336",       # 危險顏色 (紅色)
        "warning": "#FFC107",      # 警告顏色 (黃色)
        "info": "#2196F3",         # 資訊顏色 (淺藍色)
        "success": "#4CAF50",      # 成功顏色 (綠色)
        "background": "#F5F5F5",   # 背景顏色 (淺灰色)
        "background_secondary": "#E0E0E0", # 次要背景顏色 (淺灰色)
        "surface": "#FFFFFF",      # 表面顏色 (白色)
        "text_primary": "#212121", # 主要文字顏色 (深灰色)
        "text_secondary": "#757575", # 次要文字顏色 (灰色)
        "text_on_primary": "#FFFFFF", # 主要顏色上的文字顏色 (白色)
        "text_on_secondary": "#FFFFFF", # 次要顏色上的文字顏色 (白色)
        "text_on_danger": "#FFFFFF", # 危險顏色上的文字顏色 (白色)
        "text_on_warning": "#000000", # 警告顏色上的文字顏色 (黑色)
        "text_on_info": "#FFFFFF", # 資訊顏色上的文字顏色 (白色)
        "text_on_success": "#FFFFFF", # 成功顏色上的文字顏色 (白色)
        "text_on_error": "#FFFFFF", # 錯誤顏色上的文字顏色 (白色)
        "border": "#E0E0E0",       # 邊框顏色 (淺灰色)
        "hover": "#E3F2FD",        # 懸停顏色 (淺藍色)
        "active": "#BBDEFB",       # 活動顏色 (藍色)
        "disabled": "#BDBDBD",     # 禁用顏色 (灰色)
        "error": "#F44336",        # 錯誤顏色 (紅色)
    }

    # 深色主題
    DARK_THEME = {
        "primary": "#2196F3",      # 主要顏色 (藍色)
        "secondary": "#4CAF50",    # 次要顏色 (綠色)
        "accent": "#FF9800",       # 強調顏色 (橙色)
        "danger": "#F44336",       # 危險顏色 (紅色)
        "warning": "#FFC107",      # 警告顏色 (黃色)
        "info": "#03A9F4",         # 資訊顏色 (淺藍色)
        "success": "#4CAF50",      # 成功顏色 (綠色)
        "background": "#121212",   # 背景顏色 (深灰色)
        "background_secondary": "#333333", # 次要背景顏色 (深灰色)
        "surface": "#1E1E1E",      # 表面顏色 (深灰色)
        "text_primary": "#FFFFFF", # 主要文字顏色 (白色)
        "text_secondary": "#B0B0B0", # 次要文字顏色 (淺灰色)
        "text_on_primary": "#FFFFFF", # 主要顏色上的文字顏色 (白色)
        "text_on_secondary": "#FFFFFF", # 次要顏色上的文字顏色 (白色)
        "text_on_danger": "#FFFFFF", # 危險顏色上的文字顏色 (白色)
        "text_on_warning": "#000000", # 警告顏色上的文字顏色 (黑色)
        "text_on_info": "#FFFFFF", # 資訊顏色上的文字顏色 (白色)
        "text_on_success": "#FFFFFF", # 成功顏色上的文字顏色 (白色)
        "text_on_error": "#FFFFFF", # 錯誤顏色上的文字顏色 (白色)
        "border": "#333333",       # 邊框顏色 (深灰色)
        "hover": "#1E3A5F",        # 懸停顏色 (深藍色)
        "active": "#0D47A1",       # 活動顏色 (深藍色)
        "disabled": "#424242",     # 禁用顏色 (灰色)
        "error": "#F44336",        # 錯誤顏色 (紅色)
    }

    # 字體設定
    FONTS = {
        "title": ("Microsoft JhengHei UI", 12, "bold"),
        "subtitle": ("Microsoft JhengHei UI", 11, "bold"),
        "label": ("Microsoft JhengHei UI", 10),
        "text": ("Microsoft JhengHei UI", 10),
        "small": ("Microsoft JhengHei UI", 9),
        "code": ("Consolas", 9),
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config=None):
        if self._initialized:
            return

        self.config = config
        self.current_theme_name = "light"
        self.current_theme = self.LIGHT_THEME.copy()
        self.custom_themes = {}

        # 從設定檔載入主題設定
        if config:
            theme_name = config.get("ui", {}).get("theme", "light")
            self.set_theme(theme_name)

        self._initialized = True

    def set_theme(self, theme_name: str) -> bool:
        """設定當前主題

        Args:
            theme_name: 主題名稱，可以是 'light', 'dark' 或自訂主題名稱

        Returns:
            bool: 是否成功設定主題
        """
        if theme_name == "light":
            self.current_theme = self.LIGHT_THEME.copy()
            self.current_theme_name = "light"
            return True
        elif theme_name == "dark":
            self.current_theme = self.DARK_THEME.copy()
            self.current_theme_name = "dark"
            return True
        elif theme_name in self.custom_themes:
            self.current_theme = self.custom_themes[theme_name].copy()
            self.current_theme_name = theme_name
            return True
        else:
            logger.warning(f"找不到主題: {theme_name}，使用預設淺色主題")
            self.current_theme = self.LIGHT_THEME.copy()
            self.current_theme_name = "light"
            return False

    def get_color(self, color_name: str) -> str:
        """取得顏色值

        Args:
            color_name: 顏色名稱

        Returns:
            str: 顏色值 (十六進位色碼)
        """
        return self.current_theme.get(color_name, "#000000")

    def get_font(self, font_name: str) -> tuple:
        """取得字體設定

        Args:
            font_name: 字體名稱

        Returns:
            tuple: 字體設定 (字體名稱, 大小, 樣式)
        """
        return self.FONTS.get(font_name, self.FONTS["text"])

    def load_custom_theme(self, theme_path: str) -> bool:
        """從檔案載入自訂主題

        Args:
            theme_path: 主題檔案路徑

        Returns:
            bool: 是否成功載入主題
        """
        try:
            if not os.path.exists(theme_path):
                logger.error(f"主題檔案不存在: {theme_path}")
                return False

            with open(theme_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)

            if "name" not in theme_data or "colors" not in theme_data:
                logger.error(f"主題檔案格式錯誤: {theme_path}")
                return False

            theme_name = theme_data["name"]
            self.custom_themes[theme_name] = theme_data["colors"]
            logger.info(f"成功載入自訂主題: {theme_name}")
            return True

        except Exception as e:
            logger.error(f"載入自訂主題失敗: {e}")
            return False

    def save_custom_theme(self, theme_name: str, colors: Dict[str, str], theme_path: str) -> bool:
        """儲存自訂主題

        Args:
            theme_name: 主題名稱
            colors: 顏色設定
            theme_path: 主題檔案路徑

        Returns:
            bool: 是否成功儲存主題
        """
        try:
            theme_data = {
                "name": theme_name,
                "colors": colors
            }

            with open(theme_path, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, indent=2, ensure_ascii=False)

            self.custom_themes[theme_name] = colors
            logger.info(f"成功儲存自訂主題: {theme_name}")
            return True

        except Exception as e:
            logger.error(f"儲存自訂主題失敗: {e}")
            return False

    def apply_theme_to_widgets(self, root: tk.Tk) -> None:
        """套用主題到所有元件

        Args:
            root: 根視窗
        """
        try:
            # 設定根視窗背景
            try:
                root.configure(background=self.get_color("background"))
            except Exception as e:
                logger.error(f"設定根視窗背景失敗: {e}")

            # 建立樣式
            style = ttk.Style()

            # 設定基本樣式
            try:
                # 設定 Notebook 樣式
                style.configure('TNotebook', background=self.get_color("background"))
                style.configure('TNotebook.Tab',
                            padding=(15, 5),
                            font=self.get_font("label"),
                            background=self.get_color("surface"),
                            foreground=self.get_color("text_primary"))
                style.map('TNotebook.Tab',
                        background=[('selected', self.get_color("primary"))],
                        foreground=[('selected', self.get_color("surface"))])

                # 設定 Frame 樣式
                style.configure('TFrame', background=self.get_color("surface"))
                style.map('TFrame', background=[(('active',), self.get_color("surface"))])

                # 設定 LabelFrame 樣式
                style.configure('TLabelframe',
                            background=self.get_color("surface"),
                            bordercolor=self.get_color("border"),
                            borderwidth=1)
                style.configure('TLabelframe.Label',
                            font=self.get_font("subtitle"),
                            foreground=self.get_color("primary"),
                            background=self.get_color("surface"))

                # 設定 Button 樣式
                style.configure('TButton',
                            font=self.get_font("label"),
                            background=self.get_color("primary"),
                            foreground=self.get_color("surface"))
                style.map('TButton',
                        background=[('active', self.get_color("active"))],
                        foreground=[('active', self.get_color("text_primary"))])

                # 設定 Primary Button 樣式
                style.configure('Primary.TButton',
                            font=self.get_font("label"),
                            background=self.get_color("primary"),
                            foreground=self.get_color("surface"))
                style.map('Primary.TButton',
                        background=[('active', self.get_color("active"))],
                        foreground=[('active', self.get_color("surface"))])

                # 設定 Secondary Button 樣式
                style.configure('Secondary.TButton',
                            font=self.get_font("label"),
                            background=self.get_color("secondary"),
                            foreground=self.get_color("surface"))
                style.map('Secondary.TButton',
                        background=[('active', self.get_color("active"))],
                        foreground=[('active', self.get_color("surface"))])

                # 設定 Danger Button 樣式
                style.configure('Danger.TButton',
                            font=self.get_font("label"),
                            background=self.get_color("danger"),
                            foreground=self.get_color("surface"))
                style.map('Danger.TButton',
                        background=[('active', self.get_color("active"))],
                        foreground=[('active', self.get_color("surface"))])

                # 設定 Label 樣式
                style.configure('TLabel',
                            font=self.get_font("label"),
                            background=self.get_color("surface"),
                            foreground=self.get_color("text_primary"))

                # 設定 Entry 樣式
                style.configure('TEntry',
                            font=self.get_font("text"),
                            fieldbackground=self.get_color("surface"),
                            foreground=self.get_color("text_primary"))

                # 設定 Combobox 樣式
                style.configure('TCombobox',
                            font=self.get_font("text"),
                            fieldbackground=self.get_color("surface"),
                            foreground=self.get_color("text_primary"))

                # 卡片框架樣式
                style.configure(
                    "Card.TFrame",
                    background=self.get_color("background")
                )

                style.configure(
                    "CardMain.TFrame",
                    background=self.get_color("surface"),
                    bordercolor=self.get_color("border")
                )

                # 分隔線樣式
                style.configure(
                    "TSeparator",
                    background=self.get_color("border")
                )
            except Exception as e:
                logger.error(f"設定樣式失敗: {e}")

            # 更新所有子元件
            try:
                self._update_all_widgets(root)
            except Exception as e:
                logger.error(f"更新子元件失敗: {e}")
        except Exception as e:
            logger.error(f"套用主題失敗: {e}")

    def _update_all_widgets(self, parent):
        """遞迴更新所有子元件的樣式

        Args:
            parent: 父元件
        """
        try:
            for child in parent.winfo_children():
                # 更新不同類型元件的樣式
                if isinstance(child, tk.Frame):
                    try:
                        child.configure(background=self.get_color("surface"))
                    except tk.TclError:
                        pass
                elif isinstance(child, ttk.Frame):
                    try:
                        child.configure(style="TFrame")
                    except tk.TclError:
                        pass
                elif isinstance(child, tk.Label) or isinstance(child, ttk.Label):
                    if isinstance(child, tk.Label):
                        try:
                            child.configure(
                                background=self.get_color("surface"),
                                foreground=self.get_color("text_primary"),
                                font=self.get_font("label")
                            )
                        except tk.TclError:
                            pass
                elif isinstance(child, tk.Button):
                    # 保留原有按鈕樣式，僅更新字體
                    try:
                        child.configure(font=self.get_font("label"))
                    except tk.TclError:
                        pass
                elif isinstance(child, tk.Text):
                    try:
                        child.configure(
                            background=self.get_color("surface"),
                            foreground=self.get_color("text_primary"),
                            font=self.get_font("text")
                        )
                    except tk.TclError:
                        pass

                # 遞迴處理子元件
                try:
                    if child.winfo_children():
                        self._update_all_widgets(child)
                except:
                    pass
        except Exception as e:
            logger.error(f"更新元件樣式失敗: {e}")

    def get_button_style(self, button_type: str = "primary") -> Dict[str, Any]:
        """取得按鈕樣式

        Args:
            button_type: 按鈕類型，可以是 'primary', 'secondary', 'danger', 'info', 'warning', 'success'

        Returns:
            Dict[str, Any]: 按鈕樣式設定
        """
        # 定義按鈕樣式 - 使用高對比度的顏色組合
        button_styles = {
            "primary": {
                "bg": "#0D47A1",  # 深藍色
                "fg": "#FFFFFF",  # 白色
                "font": ("Microsoft JhengHei UI", 12, "bold"),  # 粗體字型
                "activebackground": "#1565C0",  # 中藍色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            },
            "secondary": {
                "bg": "#424242",  # 深灰色
                "fg": "#FFFFFF",  # 白色
                "font": ("Microsoft JhengHei UI", 12, "bold"),
                "activebackground": "#616161",  # 中灰色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            },
            "danger": {
                "bg": "#B71C1C",  # 深紅色
                "fg": "#FFFFFF",  # 白色
                "font": ("Microsoft JhengHei UI", 12, "bold"),
                "activebackground": "#D32F2F",  # 中紅色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            },
            "info": {
                "bg": "#01579B",  # 深淺藍色
                "fg": "#FFFFFF",  # 白色
                "font": ("Microsoft JhengHei UI", 12, "bold"),
                "activebackground": "#0277BD",  # 中淺藍色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            },
            "warning": {
                "bg": "#E65100",  # 深橙色
                "fg": "#FFFFFF",  # 白色 (改為白色增加對比度)
                "font": ("Microsoft JhengHei UI", 12, "bold"),
                "activebackground": "#EF6C00",  # 中橙色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            },
            "success": {
                "bg": "#1B5E20",  # 深綠色
                "fg": "#FFFFFF",  # 白色
                "font": ("Microsoft JhengHei UI", 12, "bold"),
                "activebackground": "#2E7D32",  # 中綠色
                "activeforeground": "#FFFFFF",  # 白色
                "relief": tk.RAISED,
                "borderwidth": 2,
                "padx": 20,
                "pady": 10,
                "width": 12,
                "height": 1,
                "cursor": "hand2"
            }
        }

        return button_styles.get(button_type, button_styles["primary"])

