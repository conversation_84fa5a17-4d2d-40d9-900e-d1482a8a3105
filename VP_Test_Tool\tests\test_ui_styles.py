"""UI 樣式單元測試"""
import unittest
import os
import sys

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.ui_styles import (
    PADDING, SIZES, FONTS, COLORS, BUTTON_STYLES,
    CARD_STYLE, FORM_STYLE, TABLE_STYLE,
    ERROR_STYLE, SUCCESS_STYLE, WARNING_STYLE, INFO_STYLE,
    LOG_STYLE, STATUS_BAR_STYLE, TOOLBAR_STYLE, TAB_STYLE,
    MEMBER_INFO_STYLE, RESOURCE_SETTING_STYLE, BATCH_PROCESSING_STYLE,
    get_style
)

class TestUIStyles(unittest.TestCase):
    """UI 樣式單元測試類"""

    def test_padding(self):
        """測試間距設定"""
        self.assertIsInstance(PADDING, dict)
        self.assertIn("small", PADDING)
        self.assertIn("medium", PADDING)
        self.assertIn("large", PADDING)
        self.assertIn("xlarge", PADDING)
        self.assertIsInstance(PADDING["small"], int)
        self.assertIsInstance(PADDING["medium"], int)
        self.assertIsInstance(PADDING["large"], int)
        self.assertIsInstance(PADDING["xlarge"], int)

    def test_sizes(self):
        """測試大小設定"""
        self.assertIsInstance(SIZES, dict)
        self.assertIn("button", SIZES)
        self.assertIn("entry", SIZES)
        self.assertIn("combobox", SIZES)
        self.assertIn("text", SIZES)
        self.assertIsInstance(SIZES["button"], dict)
        self.assertIsInstance(SIZES["entry"], dict)
        self.assertIsInstance(SIZES["combobox"], dict)
        self.assertIsInstance(SIZES["text"], dict)
        self.assertIn("width", SIZES["button"])
        self.assertIn("height", SIZES["button"])
        self.assertIn("width", SIZES["entry"])
        self.assertIn("width", SIZES["combobox"])
        self.assertIn("width", SIZES["text"])
        self.assertIn("height", SIZES["text"])

    def test_fonts(self):
        """測試字體設定"""
        self.assertIsInstance(FONTS, dict)
        self.assertIn("title", FONTS)
        self.assertIn("subtitle", FONTS)
        self.assertIn("label", FONTS)
        self.assertIn("text", FONTS)
        self.assertIn("small", FONTS)
        self.assertIn("code", FONTS)

    def test_colors(self):
        """測試顏色設定"""
        self.assertIsInstance(COLORS, dict)
        self.assertIn("primary", COLORS)
        self.assertIn("secondary", COLORS)
        self.assertIn("accent", COLORS)
        self.assertIn("danger", COLORS)
        self.assertIn("warning", COLORS)
        self.assertIn("info", COLORS)
        self.assertIn("success", COLORS)
        self.assertIn("background", COLORS)
        self.assertIn("surface", COLORS)
        self.assertIn("text_primary", COLORS)
        self.assertIn("text_secondary", COLORS)
        self.assertIn("border", COLORS)
        self.assertIn("hover", COLORS)
        self.assertIn("active", COLORS)
        self.assertIn("disabled", COLORS)

    def test_button_styles(self):
        """測試按鈕樣式設定"""
        self.assertIsInstance(BUTTON_STYLES, dict)
        self.assertIn("primary", BUTTON_STYLES)
        self.assertIn("secondary", BUTTON_STYLES)
        self.assertIn("danger", BUTTON_STYLES)
        self.assertIn("info", BUTTON_STYLES)
        self.assertIn("warning", BUTTON_STYLES)
        self.assertIn("success", BUTTON_STYLES)

    def test_card_style(self):
        """測試卡片樣式設定"""
        self.assertIsInstance(CARD_STYLE, dict)
        self.assertIn("background", CARD_STYLE)
        self.assertIn("border_color", CARD_STYLE)
        self.assertIn("border_width", CARD_STYLE)
        self.assertIn("relief", CARD_STYLE)
        self.assertIn("padding", CARD_STYLE)

    def test_form_style(self):
        """測試表單樣式設定"""
        self.assertIsInstance(FORM_STYLE, dict)
        self.assertIn("label_width", FORM_STYLE)
        self.assertIn("entry_width", FORM_STYLE)
        self.assertIn("padding", FORM_STYLE)
        self.assertIn("row_padding", FORM_STYLE)

    def test_table_style(self):
        """測試表格樣式設定"""
        self.assertIsInstance(TABLE_STYLE, dict)
        self.assertIn("header_background", TABLE_STYLE)
        self.assertIn("header_foreground", TABLE_STYLE)
        self.assertIn("row_height", TABLE_STYLE)
        self.assertIn("alternate_row_color", TABLE_STYLE)

    def test_error_style(self):
        """測試錯誤提示樣式設定"""
        self.assertIsInstance(ERROR_STYLE, dict)
        self.assertIn("background", ERROR_STYLE)
        self.assertIn("foreground", ERROR_STYLE)
        self.assertIn("border_color", ERROR_STYLE)
        self.assertIn("border_width", ERROR_STYLE)
        self.assertIn("padding", ERROR_STYLE)
        self.assertIn("font", ERROR_STYLE)

    def test_success_style(self):
        """測試成功提示樣式設定"""
        self.assertIsInstance(SUCCESS_STYLE, dict)
        self.assertIn("background", SUCCESS_STYLE)
        self.assertIn("foreground", SUCCESS_STYLE)
        self.assertIn("border_color", SUCCESS_STYLE)
        self.assertIn("border_width", SUCCESS_STYLE)
        self.assertIn("padding", SUCCESS_STYLE)
        self.assertIn("font", SUCCESS_STYLE)

    def test_warning_style(self):
        """測試警告提示樣式設定"""
        self.assertIsInstance(WARNING_STYLE, dict)
        self.assertIn("background", WARNING_STYLE)
        self.assertIn("foreground", WARNING_STYLE)
        self.assertIn("border_color", WARNING_STYLE)
        self.assertIn("border_width", WARNING_STYLE)
        self.assertIn("padding", WARNING_STYLE)
        self.assertIn("font", WARNING_STYLE)

    def test_info_style(self):
        """測試資訊提示樣式設定"""
        self.assertIsInstance(INFO_STYLE, dict)
        self.assertIn("background", INFO_STYLE)
        self.assertIn("foreground", INFO_STYLE)
        self.assertIn("border_color", INFO_STYLE)
        self.assertIn("border_width", INFO_STYLE)
        self.assertIn("padding", INFO_STYLE)
        self.assertIn("font", INFO_STYLE)

    def test_log_style(self):
        """測試日誌樣式設定"""
        self.assertIsInstance(LOG_STYLE, dict)
        self.assertIn("background", LOG_STYLE)
        self.assertIn("foreground", LOG_STYLE)
        self.assertIn("font", LOG_STYLE)
        self.assertIn("padding", LOG_STYLE)

    def test_status_bar_style(self):
        """測試狀態列樣式設定"""
        self.assertIsInstance(STATUS_BAR_STYLE, dict)
        self.assertIn("background", STATUS_BAR_STYLE)
        self.assertIn("foreground", STATUS_BAR_STYLE)
        self.assertIn("font", STATUS_BAR_STYLE)
        self.assertIn("padding", STATUS_BAR_STYLE)

    def test_toolbar_style(self):
        """測試工具列樣式設定"""
        self.assertIsInstance(TOOLBAR_STYLE, dict)
        self.assertIn("background", TOOLBAR_STYLE)
        self.assertIn("padding", TOOLBAR_STYLE)

    def test_tab_style(self):
        """測試頁籤樣式設定"""
        self.assertIsInstance(TAB_STYLE, dict)
        self.assertIn("padding", TAB_STYLE)
        self.assertIn("font", TAB_STYLE)

    def test_member_info_style(self):
        """測試會員資訊區域樣式設定"""
        self.assertIsInstance(MEMBER_INFO_STYLE, dict)
        self.assertIn("background", MEMBER_INFO_STYLE)
        self.assertIn("border_color", MEMBER_INFO_STYLE)
        self.assertIn("border_width", MEMBER_INFO_STYLE)
        self.assertIn("relief", MEMBER_INFO_STYLE)
        self.assertIn("padding", MEMBER_INFO_STYLE)
        self.assertIn("label_width", MEMBER_INFO_STYLE)
        self.assertIn("entry_width", MEMBER_INFO_STYLE)
        self.assertIn("row_padding", MEMBER_INFO_STYLE)

    def test_resource_setting_style(self):
        """測試資源設定區域樣式設定"""
        self.assertIsInstance(RESOURCE_SETTING_STYLE, dict)
        self.assertIn("background", RESOURCE_SETTING_STYLE)
        self.assertIn("border_color", RESOURCE_SETTING_STYLE)
        self.assertIn("border_width", RESOURCE_SETTING_STYLE)
        self.assertIn("relief", RESOURCE_SETTING_STYLE)
        self.assertIn("padding", RESOURCE_SETTING_STYLE)
        self.assertIn("label_width", RESOURCE_SETTING_STYLE)
        self.assertIn("entry_width", RESOURCE_SETTING_STYLE)
        self.assertIn("row_padding", RESOURCE_SETTING_STYLE)

    def test_batch_processing_style(self):
        """測試批次處理區域樣式設定"""
        self.assertIsInstance(BATCH_PROCESSING_STYLE, dict)
        self.assertIn("background", BATCH_PROCESSING_STYLE)
        self.assertIn("border_color", BATCH_PROCESSING_STYLE)
        self.assertIn("border_width", BATCH_PROCESSING_STYLE)
        self.assertIn("relief", BATCH_PROCESSING_STYLE)
        self.assertIn("padding", BATCH_PROCESSING_STYLE)
        self.assertIn("label_width", BATCH_PROCESSING_STYLE)
        self.assertIn("entry_width", BATCH_PROCESSING_STYLE)
        self.assertIn("row_padding", BATCH_PROCESSING_STYLE)

    def test_get_style(self):
        """測試取得樣式"""
        # 測試取得已定義的樣式
        self.assertEqual(get_style("card"), CARD_STYLE)
        self.assertEqual(get_style("form"), FORM_STYLE)
        self.assertEqual(get_style("table"), TABLE_STYLE)
        self.assertEqual(get_style("error"), ERROR_STYLE)
        self.assertEqual(get_style("success"), SUCCESS_STYLE)
        self.assertEqual(get_style("warning"), WARNING_STYLE)
        self.assertEqual(get_style("info"), INFO_STYLE)
        self.assertEqual(get_style("log"), LOG_STYLE)
        self.assertEqual(get_style("status_bar"), STATUS_BAR_STYLE)
        self.assertEqual(get_style("toolbar"), TOOLBAR_STYLE)
        self.assertEqual(get_style("tab"), TAB_STYLE)
        self.assertEqual(get_style("member_info"), MEMBER_INFO_STYLE)
        self.assertEqual(get_style("resource_setting"), RESOURCE_SETTING_STYLE)
        self.assertEqual(get_style("batch_processing"), BATCH_PROCESSING_STYLE)

        # 測試取得不存在的樣式
        self.assertEqual(get_style("nonexistent"), {})

if __name__ == '__main__':
    unittest.main()
