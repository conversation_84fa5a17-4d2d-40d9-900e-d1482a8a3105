"""通知元件

此模組提供統一的通知元件，用於顯示錯誤、警告、成功和資訊訊息。
"""
import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable
from utils.theme import ThemeManager
from utils.ui_styles import ERROR_STYLE, SUCCESS_STYLE, WARNING_STYLE, INFO_STYLE
from utils.icon_manager import IconManager

class Notification(ttk.Frame):
    """通知元件
    
    提供統一的通知元件，用於顯示錯誤、警告、成功和資訊訊息。
    
    Args:
        parent: 父元件
        message: 通知訊息
        notification_type: 通知類型，可以是 'error', 'warning', 'success', 'info'
        duration: 顯示時間 (毫秒)，如果為 0 則不會自動關閉
        on_close: 關閉時的回調函數
        **kwargs: 其他參數
    """
    
    def __init__(
        self,
        parent,
        message: str,
        notification_type: str = "info",
        duration: int = 5000,
        on_close: Optional[Callable[[], None]] = None,
        **kwargs
    ):
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 根據通知類型選擇樣式
        if notification_type == "error":
            style = ERROR_STYLE
            icon = IconManager.get("error")
        elif notification_type == "warning":
            style = WARNING_STYLE
            icon = IconManager.get("warning")
        elif notification_type == "success":
            style = SUCCESS_STYLE
            icon = IconManager.get("success")
        else:  # info
            style = INFO_STYLE
            icon = IconManager.get("info")
        
        # 建立框架
        super().__init__(
            parent,
            padding=style["padding"],
            **kwargs
        )
        
        # 設定變數
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.on_close = on_close
        self.close_job = None
        
        # 設定樣式
        self.configure(style=f"{notification_type.capitalize()}.TFrame")
        
        # 初始化 UI
        self._init_ui(style, icon)
        
        # 如果有設定顯示時間，則設定自動關閉
        if duration > 0:
            self.close_job = self.after(duration, self.close)
    
    def _init_ui(self, style, icon):
        """初始化 UI"""
        # 建立容器
        container = ttk.Frame(self)
        container.pack(fill="both", expand=True)
        
        # 建立圖示標籤
        icon_label = ttk.Label(
            container,
            text=icon,
            font=("TkDefaultFont", 16),
            foreground=style["foreground"]
        )
        icon_label.pack(side="left", padx=(0, 10))
        
        # 建立訊息標籤
        message_label = ttk.Label(
            container,
            text=self.message,
            font=style["font"],
            foreground=style["foreground"],
            wraplength=300,
            justify="left"
        )
        message_label.pack(side="left", fill="x", expand=True)
        
        # 建立關閉按鈕
        close_button = ttk.Label(
            container,
            text=IconManager.get("close"),
            font=("TkDefaultFont", 12),
            foreground=style["foreground"],
            cursor="hand2"
        )
        close_button.pack(side="right", padx=(10, 0))
        
        # 綁定關閉按鈕事件
        close_button.bind("<Button-1>", lambda e: self.close())
        
        # 綁定整個通知元件的點擊事件
        self.bind("<Button-1>", lambda e: self.close())
    
    def close(self):
        """關閉通知"""
        # 如果有設定自動關閉，則取消
        if self.close_job:
            self.after_cancel(self.close_job)
            self.close_job = None
        
        # 銷毀元件
        self.destroy()
        
        # 如果有設定關閉回調函數，則呼叫
        if self.on_close:
            self.on_close()

class NotificationManager:
    """通知管理器
    
    管理通知元件的顯示和關閉。
    
    Args:
        parent: 父元件
        max_notifications: 最大通知數量
    """
    
    def __init__(self, parent, max_notifications: int = 3):
        self.parent = parent
        self.max_notifications = max_notifications
        self.notifications = []
        
        # 建立通知容器
        self.container = ttk.Frame(parent)
        self.container.pack(fill="x", padx=10, pady=10)
        
        # 初始時隱藏容器
        self.container.pack_forget()
    
    def show(
        self,
        message: str,
        notification_type: str = "info",
        duration: int = 5000
    ) -> Notification:
        """顯示通知
        
        Args:
            message: 通知訊息
            notification_type: 通知類型，可以是 'error', 'warning', 'success', 'info'
            duration: 顯示時間 (毫秒)，如果為 0 則不會自動關閉
            
        Returns:
            Notification: 通知元件
        """
        # 如果通知數量已達上限，則關閉最舊的通知
        if len(self.notifications) >= self.max_notifications:
            oldest = self.notifications.pop(0)
            oldest.close()
        
        # 顯示容器
        self.container.pack(fill="x", padx=10, pady=10)
        
        # 建立通知元件
        notification = Notification(
            self.container,
            message,
            notification_type,
            duration,
            on_close=lambda: self._on_notification_close(notification)
        )
        notification.pack(fill="x", pady=(0, 5))
        
        # 添加到通知列表
        self.notifications.append(notification)
        
        return notification
    
    def _on_notification_close(self, notification):
        """通知關閉時的回調函數"""
        # 從通知列表中移除
        if notification in self.notifications:
            self.notifications.remove(notification)
        
        # 如果沒有通知，則隱藏容器
        if not self.notifications:
            self.container.pack_forget()
    
    def clear(self):
        """清除所有通知"""
        for notification in self.notifications[:]:
            notification.close()
        
        self.notifications = []
        self.container.pack_forget()
    
    def show_error(self, message: str, duration: int = 5000) -> Notification:
        """顯示錯誤通知"""
        return self.show(message, "error", duration)
    
    def show_warning(self, message: str, duration: int = 5000) -> Notification:
        """顯示警告通知"""
        return self.show(message, "warning", duration)
    
    def show_success(self, message: str, duration: int = 5000) -> Notification:
        """顯示成功通知"""
        return self.show(message, "success", duration)
    
    def show_info(self, message: str, duration: int = 5000) -> Notification:
        """顯示資訊通知"""
        return self.show(message, "info", duration)
