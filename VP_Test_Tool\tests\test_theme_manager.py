"""ThemeManager 單元測試"""
import unittest
import os
import sys

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.theme import ThemeManager

class TestThemeManager(unittest.TestCase):
    """ThemeManager 單元測試類"""

    def setUp(self):
        """測試前準備"""
        self.theme_manager = ThemeManager()

    def test_get_color(self):
        """測試取得顏色"""
        # 測試取得已定義的顏色
        self.assertIsNotNone(self.theme_manager.get_color("primary"))
        self.assertIsNotNone(self.theme_manager.get_color("secondary"))
        self.assertIsNotNone(self.theme_manager.get_color("accent"))
        self.assertIsNotNone(self.theme_manager.get_color("background"))
        self.assertIsNotNone(self.theme_manager.get_color("background_secondary"))
        self.assertIsNotNone(self.theme_manager.get_color("surface"))
        self.assertIsNotNone(self.theme_manager.get_color("text_primary"))
        self.assertIsNotNone(self.theme_manager.get_color("text_secondary"))
        self.assertIsNotNone(self.theme_manager.get_color("border"))
        self.assertIsNotNone(self.theme_manager.get_color("hover"))
        self.assertIsNotNone(self.theme_manager.get_color("active"))
        self.assertIsNotNone(self.theme_manager.get_color("disabled"))
        self.assertIsNotNone(self.theme_manager.get_color("success"))
        self.assertIsNotNone(self.theme_manager.get_color("info"))
        self.assertIsNotNone(self.theme_manager.get_color("warning"))
        self.assertIsNotNone(self.theme_manager.get_color("danger"))

    def test_get_nonexistent_color(self):
        """測試取得不存在的顏色"""
        # 測試取得不存在的顏色，應該返回預設顏色
        self.assertEqual(self.theme_manager.get_color("nonexistent"), "#000000")

    def test_get_font(self):
        """測試取得字體"""
        # 測試取得已定義的字體
        self.assertIsNotNone(self.theme_manager.get_font("title"))
        self.assertIsNotNone(self.theme_manager.get_font("subtitle"))
        self.assertIsNotNone(self.theme_manager.get_font("label"))
        self.assertIsNotNone(self.theme_manager.get_font("text"))
        self.assertIsNotNone(self.theme_manager.get_font("small"))
        self.assertIsNotNone(self.theme_manager.get_font("code"))

    def test_get_nonexistent_font(self):
        """測試取得不存在的字體"""
        # 測試取得不存在的字體，應該返回預設字體
        self.assertEqual(self.theme_manager.get_font("nonexistent"), ("Microsoft JhengHei UI", 12))

    def test_get_button_style(self):
        """測試取得按鈕樣式"""
        # 測試取得已定義的按鈕樣式
        self.assertIsNotNone(self.theme_manager.get_button_style("primary"))
        self.assertIsNotNone(self.theme_manager.get_button_style("secondary"))
        self.assertIsNotNone(self.theme_manager.get_button_style("success"))
        self.assertIsNotNone(self.theme_manager.get_button_style("info"))
        self.assertIsNotNone(self.theme_manager.get_button_style("warning"))
        self.assertIsNotNone(self.theme_manager.get_button_style("danger"))

    def test_get_nonexistent_button_style(self):
        """測試取得不存在的按鈕樣式"""
        # 測試取得不存在的按鈕樣式，應該返回預設按鈕樣式
        default_style = self.theme_manager.get_button_style("nonexistent")
        self.assertIsInstance(default_style, dict)
        self.assertIn("background", default_style)
        self.assertIn("foreground", default_style)
        self.assertIn("activebackground", default_style)
        self.assertIn("activeforeground", default_style)
        self.assertIn("borderwidth", default_style)
        self.assertIn("relief", default_style)
        self.assertIn("font", default_style)

    def test_get_theme(self):
        """測試取得主題"""
        # 測試取得主題
        theme = self.theme_manager.get_theme()
        self.assertIsInstance(theme, dict)
        self.assertIn("colors", theme)
        self.assertIn("fonts", theme)
        self.assertIn("button_styles", theme)

    def test_set_theme(self):
        """測試設定主題"""
        # 測試設定主題
        original_theme = self.theme_manager.get_theme()
        
        # 創建新主題
        new_theme = {
            "colors": {
                "primary": "#FF0000",
                "secondary": "#00FF00",
                "accent": "#0000FF"
            },
            "fonts": {
                "title": ("Arial", 20, "bold"),
                "subtitle": ("Arial", 16, "bold"),
                "label": ("Arial", 12)
            },
            "button_styles": {
                "primary": {
                    "background": "#FF0000",
                    "foreground": "#FFFFFF"
                }
            }
        }
        
        # 設定新主題
        self.theme_manager.set_theme(new_theme)
        
        # 驗證主題已更新
        self.assertEqual(self.theme_manager.get_color("primary"), "#FF0000")
        self.assertEqual(self.theme_manager.get_color("secondary"), "#00FF00")
        self.assertEqual(self.theme_manager.get_color("accent"), "#0000FF")
        self.assertEqual(self.theme_manager.get_font("title"), ("Arial", 20, "bold"))
        self.assertEqual(self.theme_manager.get_font("subtitle"), ("Arial", 16, "bold"))
        self.assertEqual(self.theme_manager.get_font("label"), ("Arial", 12))
        self.assertEqual(self.theme_manager.get_button_style("primary")["background"], "#FF0000")
        self.assertEqual(self.theme_manager.get_button_style("primary")["foreground"], "#FFFFFF")
        
        # 恢復原始主題
        self.theme_manager.set_theme(original_theme)

if __name__ == '__main__':
    unittest.main()
