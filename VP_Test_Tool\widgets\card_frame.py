"""卡片式框架元件"""
import tkinter as tk
from tkinter import ttk
from typing import Optional
from utils.theme import ThemeManager

class CardFrame(tk.Frame):
    """卡片式框架元件

    提供卡片式的視覺效果，用於組織相關的內容。

    Args:
        parent: 父元件
        title: 卡片標題
        icon: 標題圖示 (Unicode 字元)
        **kwargs: 其他參數
    """

    def __init__(self, parent, title="", icon=None):
        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 使用 tk.Frame 而非 ttk.Frame
        super().__init__(parent, bg=self.theme_manager.get_color("surface"))

        # 主框架
        self.main_frame = tk.Frame(
            self,
            bg=self.theme_manager.get_color("surface"),
            relief="solid",  # 添加實線邊框
            borderwidth=1    # 設定邊框寬度
        )
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 標題框架 - 直接設定背景色
        title_frame = tk.Frame(self.main_frame, bg=self.theme_manager.get_color("background_secondary"))
        title_frame.pack(fill=tk.X, padx=3, pady=(2, 1))

        # 標題分隔線
        separator = ttk.Separator(self.main_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=1, pady=(0, 1))

        # 標題標籤 - 直接設定背景色和前景色
        if icon:
            title_label = tk.Label(
                title_frame,
                text=f" {icon} {title} ",
                font=("Microsoft JhengHei UI", 10, "bold"),
                bg=self.theme_manager.get_color("background_secondary"),
                fg=self.theme_manager.get_color("primary")
            )
        else:
            title_label = tk.Label(
                title_frame,
                text=f" {title} ",
                font=("Microsoft JhengHei UI", 10, "bold"),
                bg=self.theme_manager.get_color("background_secondary"),
                fg=self.theme_manager.get_color("primary")
            )
        title_label.pack(side=tk.LEFT, padx=(5, 0))

        # 內容框架 - 直接設定背景色
        self.content_frame = tk.Frame(
            self.main_frame,
            bg=self.theme_manager.get_color("surface"),
            relief="flat"
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=(1, 2))

    def get_content_frame(self) -> tk.Frame:
        """取得內容框架

        Returns:
            tk.Frame: 內容框架
        """
        return self.content_frame


