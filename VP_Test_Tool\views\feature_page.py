"""功能檢測頁面

此模塊提供功能檢測頁面，用於顯示可用功能。
"""
import tkinter as tk
from tkinter import ttk
import logging
from typing import Dict, Any, Optional, List, Tuple, Callable
from utils.theme import ThemeManager

logger = logging.getLogger(__name__)

class FeaturePage(ttk.Frame):
    """功能檢測頁面

    提供功能檢測頁面，用於顯示可用功能。

    Args:
        parent: 父窗口
        feature_detector: 功能檢測器
    """

    def __init__(
        self,
        parent,
        feature_detector=None
    ):
        super().__init__(parent)

        # 設置變數
        self.parent = parent
        self.feature_detector = feature_detector

        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 初始化 UI
        self._init_ui()

        # 檢測功能
        self._detect_features()

    def _init_ui(self):
        """初始化 UI"""
        # 創建主框架
        main_frame = ttk.Frame(self, padding=5)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 創建頂部框架
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 5))

        # 創建標題和按鈕框架
        title_frame = ttk.Frame(top_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.Y)

        # 創建標題標籤
        title_label = ttk.Label(
            title_frame,
            text="功能檢測",
            font=self.theme_manager.get_font("title")
        )
        title_label.pack(side=tk.LEFT, padx=(0, 10))

        # 創建按鈕框架
        button_frame = ttk.Frame(top_frame)
        button_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 創建重新檢測按鈕
        refresh_button = ttk.Button(
            button_frame,
            text="重新檢測",
            command=self._detect_features,
            width=10
        )
        refresh_button.pack(side=tk.LEFT, padx=2)

        # 創建查看報告按鈕
        report_button = ttk.Button(
            button_frame,
            text="查看報告",
            command=self._show_report,
            width=10
        )
        report_button.pack(side=tk.LEFT, padx=2)

        # 創建安裝指南按鈕
        guide_button = ttk.Button(
            button_frame,
            text="安裝指南",
            command=self._show_guide,
            width=10
        )
        guide_button.pack(side=tk.LEFT, padx=2)

        # 創建系統信息折疊區域
        self.system_frame = ttk.LabelFrame(main_frame, text="系統信息 (點擊展開/折疊)")
        self.system_frame.pack(fill=tk.X, pady=(0, 5))

        # 綁定點擊事件
        self.system_frame.bind("<Button-1>", self._toggle_system_info)

        # 創建系統信息摘要標籤
        self.system_summary = ttk.Label(
            self.system_frame,
            text="點擊查看系統信息",
            font=self.theme_manager.get_font("small")
        )
        self.system_summary.pack(fill=tk.X, padx=5, pady=5)

        # 創建系統信息詳情框架 (初始隱藏)
        self.system_details = ttk.Frame(self.system_frame)

        # 創建系統信息文本框
        self.system_text = tk.Text(
            self.system_details,
            wrap=tk.WORD,
            font=self.theme_manager.get_font("code"),
            height=3
        )
        self.system_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加滾動條
        system_scrollbar = ttk.Scrollbar(self.system_text)
        system_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.system_text.config(yscrollcommand=system_scrollbar.set)
        system_scrollbar.config(command=self.system_text.yview)

        # 創建標籤頁
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 創建可用功能頁面
        self.available_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.available_frame, text="可用功能")

        # 創建不可用功能頁面
        self.unavailable_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.unavailable_frame, text="不可用功能")

        # 創建可用功能滾動區域
        self.available_canvas = tk.Canvas(self.available_frame)
        self.available_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滾動條
        available_scrollbar = ttk.Scrollbar(self.available_frame, orient=tk.VERTICAL, command=self.available_canvas.yview)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.available_canvas.configure(yscrollcommand=available_scrollbar.set)

        # 創建可用功能內容框架
        self.available_content = ttk.Frame(self.available_canvas)
        self.available_canvas.create_window((0, 0), window=self.available_content, anchor=tk.NW)

        # 綁定調整大小事件
        self.available_content.bind("<Configure>", lambda e: self.available_canvas.configure(scrollregion=self.available_canvas.bbox("all")))

        # 創建不可用功能滾動區域
        self.unavailable_canvas = tk.Canvas(self.unavailable_frame)
        self.unavailable_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滾動條
        unavailable_scrollbar = ttk.Scrollbar(self.unavailable_frame, orient=tk.VERTICAL, command=self.unavailable_canvas.yview)
        unavailable_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.unavailable_canvas.configure(yscrollcommand=unavailable_scrollbar.set)

        # 創建不可用功能內容框架
        self.unavailable_content = ttk.Frame(self.unavailable_canvas)
        self.unavailable_canvas.create_window((0, 0), window=self.unavailable_content, anchor=tk.NW)

        # 綁定調整大小事件
        self.unavailable_content.bind("<Configure>", lambda e: self.unavailable_canvas.configure(scrollregion=self.unavailable_canvas.bbox("all")))

        # 綁定滾輪事件
        self.available_canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        self.unavailable_canvas.bind_all("<MouseWheel>", self._on_mousewheel)

    def _toggle_system_info(self, event):
        """切換系統信息顯示/隱藏"""
        if self.system_details.winfo_ismapped():
            self.system_details.pack_forget()
        else:
            self.system_details.pack(fill=tk.BOTH, expand=True)

    def _on_mousewheel(self, event):
        """處理滾輪事件"""
        # 獲取當前選中的標籤頁
        current_tab = self.notebook.select()

        # 根據當前標籤頁滾動相應的畫布
        if current_tab == str(self.available_frame):
            self.available_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        elif current_tab == str(self.unavailable_frame):
            self.unavailable_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def _detect_features(self):
        """檢測功能"""
        # 檢查功能檢測器
        if not self.feature_detector:
            self._show_error("功能檢測器未初始化")
            return

        try:
            # 清空功能框架
            for widget in self.available_content.winfo_children():
                widget.destroy()
            for widget in self.unavailable_content.winfo_children():
                widget.destroy()

            # 檢測功能
            features = self.feature_detector.detect_features()

            # 獲取系統信息
            system_info = self.feature_detector.get_system_info()

            # 顯示系統信息
            self._show_system_info(system_info)

            # 更新系統信息摘要
            os_info = f"{system_info.get('os')} {system_info.get('os_release')}"
            python_info = f"Python {system_info.get('python_version')}"

            if "memory_total" in system_info:
                memory_info = f"內存: {system_info.get('memory_percent')}% 使用"
                cpu_info = f"CPU: {system_info.get('cpu_count')} 核心"
                summary = f"{os_info} | {python_info} | {memory_info} | {cpu_info}"
            else:
                summary = f"{os_info} | {python_info}"

            self.system_summary.config(text=summary)

            # 分離可用和不可用功能
            available_features = {}
            unavailable_features = {}

            for name, feature in features.items():
                if feature["available"]:
                    available_features[name] = feature
                else:
                    unavailable_features[name] = feature

            # 顯示可用功能
            self._show_features(self.available_content, available_features, True)

            # 顯示不可用功能
            self._show_features(self.unavailable_content, unavailable_features, False)

            # 更新標籤頁標題
            self.notebook.tab(0, text=f"可用功能 ({len(available_features)})")
            self.notebook.tab(1, text=f"不可用功能 ({len(unavailable_features)})")

            # 選擇第一個標籤頁
            self.notebook.select(0)

        except Exception as e:
            logger.error(f"檢測功能失敗: {e}")
            self._show_error(f"檢測功能失敗: {e}")

    def _show_features(self, parent, features, is_available):
        """顯示功能列表

        Args:
            parent: 父容器
            features: 功能字典
            is_available: 是否為可用功能
        """
        if not features:
            # 如果沒有功能，顯示提示信息
            message = "沒有可用功能" if is_available else "沒有不可用功能"
            label = ttk.Label(
                parent,
                text=message,
                font=self.theme_manager.get_font("normal"),
                foreground=self.theme_manager.get_color("text_secondary")
            )
            label.pack(pady=20)
            return

        # 創建功能網格
        row = 0
        col = 0
        max_cols = 3  # 增加到3列

        # 計算每列寬度
        col_width = 150  # 更窄的列寬

        # 顯示功能
        for name, feature in features.items():
            # 創建功能框架
            feature_frame = ttk.Frame(parent, padding=3)  # 減少內邊距
            feature_frame.grid(row=row, column=col, sticky=tk.NSEW, padx=3, pady=3)  # 減少外邊距

            # 設置背景顏色
            if is_available:
                bg_color = self.theme_manager.get_color("success")
                fg_color = self.theme_manager.get_color("text_on_success")
            else:
                bg_color = self.theme_manager.get_color("error")
                fg_color = self.theme_manager.get_color("text_on_error")

            # 創建功能標籤
            feature_label = ttk.Label(
                feature_frame,
                text=feature["name"],
                font=self.theme_manager.get_font("subtitle"),
                foreground=fg_color,
                background=bg_color,
                padding=3,  # 減少內邊距
                anchor=tk.CENTER
            )
            feature_label.pack(fill=tk.X)

            # 創建描述標籤
            desc_label = ttk.Label(
                feature_frame,
                text=feature["description"],
                font=self.theme_manager.get_font("small"),
                wraplength=col_width,  # 更窄的換行寬度
                justify=tk.CENTER
            )
            desc_label.pack(fill=tk.X, pady=(3, 0))  # 減少間距

            # 創建信息框架
            info_frame = ttk.Frame(feature_frame)
            info_frame.pack(fill=tk.X, pady=(3, 0))  # 減少間距

            # 創建版本或錯誤標籤
            if is_available:
                version_text = f"版本: {feature['version'] or '未知'}"
                version_label = ttk.Label(
                    info_frame,
                    text=version_text,
                    font=self.theme_manager.get_font("small"),
                    foreground=self.theme_manager.get_color("text_secondary")
                )
                version_label.pack(fill=tk.X)
            else:
                error_text = feature['error'] or "未知錯誤"
                if len(error_text) > 30:
                    error_text = error_text[:27] + "..."
                error_label = ttk.Label(
                    info_frame,
                    text=error_text,
                    font=self.theme_manager.get_font("small"),
                    foreground=self.theme_manager.get_color("error")
                )
                error_label.pack(fill=tk.X)

            # 創建工具提示
            tooltip_text = f"名稱: {feature['name']}\n"
            tooltip_text += f"描述: {feature['description']}\n"

            if is_available:
                tooltip_text += f"版本: {feature['version'] or '未知'}\n"
            else:
                tooltip_text += f"錯誤: {feature['error'] or '未知錯誤'}\n"

            if feature["required_for"]:
                tooltip_text += f"用於: {', '.join(feature['required_for'])}"

            self._create_tooltip(feature_frame, tooltip_text)

            # 更新行列
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # 設置列權重
        for i in range(max_cols):
            parent.columnconfigure(i, weight=1)

    def _create_tooltip(self, widget, text):
        """創建工具提示

        Args:
            widget: 要添加工具提示的控件
            text: 工具提示文本
        """
        def enter(event):
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25

            # 創建工具提示窗口
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{x}+{y}")

            label = ttk.Label(
                self.tooltip,
                text=text,
                justify=tk.LEFT,
                background=self.theme_manager.get_color("info"),
                foreground=self.theme_manager.get_color("text_on_info"),
                relief="solid",
                borderwidth=1,
                font=self.theme_manager.get_font("small"),
                padding=5
            )
            label.pack()

        def leave(event):
            if hasattr(self, "tooltip"):
                self.tooltip.destroy()

        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def _show_system_info(self, system_info: Dict[str, Any]):
        """顯示系統信息

        Args:
            system_info: 系統信息
        """
        # 清空文本框
        self.system_text.config(state=tk.NORMAL)
        self.system_text.delete(1.0, tk.END)

        # 構建系統信息
        info_lines = [
            f"操作系統: {system_info.get('os')} {system_info.get('os_release')} {system_info.get('os_version')}",
            f"Python 版本: {system_info.get('python_version')} ({system_info.get('python_implementation')})",
            f"處理器: {system_info.get('processor')}"
        ]

        if "memory_total" in system_info:
            info_lines.extend([
                f"內存: {system_info.get('memory_total') / (1024 * 1024 * 1024):.2f} GB 總計, "
                f"{system_info.get('memory_available') / (1024 * 1024 * 1024):.2f} GB 可用, "
                f"{system_info.get('memory_percent')}% 使用率",
                f"磁盤: {system_info.get('disk_total') / (1024 * 1024 * 1024):.2f} GB 總計, "
                f"{system_info.get('disk_free') / (1024 * 1024 * 1024):.2f} GB 可用, "
                f"{system_info.get('disk_percent')}% 使用率",
                f"CPU: {system_info.get('cpu_count')} 核心, {system_info.get('cpu_percent')}% 使用率"
            ])

        # 插入系統信息
        self.system_text.insert(tk.END, "\n".join(info_lines))
        self.system_text.config(state=tk.DISABLED)

    def _show_error(self, message: str):
        """顯示錯誤

        Args:
            message: 錯誤信息
        """
        # 清空功能框架
        for widget in self.available_content.winfo_children():
            widget.destroy()
        for widget in self.unavailable_content.winfo_children():
            widget.destroy()

        # 創建錯誤標籤 (在兩個標籤頁中都顯示)
        for parent in [self.available_content, self.unavailable_content]:
            error_frame = ttk.Frame(parent, padding=10)
            error_frame.pack(fill=tk.BOTH, expand=True)

            error_label = ttk.Label(
                error_frame,
                text=message,
                font=self.theme_manager.get_font("normal"),
                foreground=self.theme_manager.get_color("error"),
                wraplength=400,
                justify=tk.CENTER
            )
            error_label.pack(pady=20)

            # 添加重試按鈕
            retry_button = ttk.Button(
                error_frame,
                text="重新檢測",
                command=self._detect_features
            )
            retry_button.pack(pady=10)

    def _show_report(self):
        """顯示報告"""
        # 檢查功能檢測器
        if not self.feature_detector:
            self._show_error("功能檢測器未初始化")
            return

        try:
            # 顯示報告對話框
            self.feature_detector.show_feature_report_dialog(self)
        except Exception as e:
            logger.error(f"顯示報告失敗: {e}")
            self._show_error(f"顯示報告失敗: {e}")

    def _show_guide(self):
        """顯示安裝指南"""
        try:
            # 導入 webbrowser 模塊
            import webbrowser

            # 獲取安裝指南路徑
            import os
            guide_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "docs",
                "INSTALLATION_GUIDE.md"
            )

            # 檢查文件是否存在
            if os.path.exists(guide_path):
                # 打開安裝指南
                webbrowser.open(guide_path)
            else:
                # 顯示錯誤
                from tkinter import messagebox
                messagebox.showerror("錯誤", "安裝指南文件不存在")

        except Exception as e:
            logger.error(f"顯示安裝指南失敗: {e}")
            from tkinter import messagebox
            messagebox.showerror("錯誤", f"顯示安裝指南失敗: {e}")

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)

    # 導入功能檢測器
    from utils.feature_detector import feature_detector

    # 創建根窗口
    root = tk.Tk()
    root.title("功能檢測頁面")
    root.geometry("800x600")

    # 創建功能檢測頁面
    page = FeaturePage(root, feature_detector)
    page.pack(fill=tk.BOTH, expand=True)

    # 啟動主循環
    root.mainloop()
