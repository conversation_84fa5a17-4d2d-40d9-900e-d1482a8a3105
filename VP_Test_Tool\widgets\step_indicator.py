"""步驟指示器元件"""
import tkinter as tk
from tkinter import ttk
from typing import List
from utils.theme import ThemeManager

class StepIndicator(ttk.Frame):
    """步驟指示器元件

    提供多步驟操作的視覺指示。

    Args:
        parent: 父元件
        steps: 步驟名稱列表
        current_step: 當前步驟索引 (從 0 開始)
        **kwargs: 其他參數
    """
    def __init__(
        self,
        parent,
        steps: List[str],
        current_step: int = 0,
        **kwargs
    ):
        super().__init__(parent, **kwargs)

        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 設定變數
        self.steps = steps
        self.current_step = current_step
        self.step_frames = []
        self.step_labels = []
        self.step_indicators = []
        self.step_lines = []

        # 初始化 UI
        self._init_ui()

    def _init_ui(self):
        """初始化 UI"""
        # 建立容器
        container = ttk.Frame(self)
        container.pack(fill="x", padx=10, pady=10)

        # 建立步驟指示器
        for i, step in enumerate(self.steps):
            # 建立步驟框架
            step_frame = ttk.Frame(container)
            step_frame.pack(side="left", fill="y")
            self.step_frames.append(step_frame)

            # 建立步驟指示器
            indicator_frame = ttk.Frame(step_frame)
            indicator_frame.pack(side="top", anchor="center")

            # 建立步驟圓圈
            indicator_canvas = tk.Canvas(
                indicator_frame,
                width=30,
                height=30,
                bg=self.theme_manager.get_color("background"),
                highlightthickness=0
            )
            indicator_canvas.pack(side="left")

            # 繪製圓圈
            if i < self.current_step:
                # 已完成步驟
                indicator = indicator_canvas.create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("success"),
                    outline=self.theme_manager.get_color("success")
                )
                # 添加勾號
                indicator_canvas.create_text(
                    15, 15,
                    text="✓",
                    fill="white",
                    font=("TkDefaultFont", 12, "bold")
                )
            elif i == self.current_step:
                # 當前步驟
                indicator = indicator_canvas.create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("primary"),
                    outline=self.theme_manager.get_color("primary")
                )
                # 添加數字
                indicator_canvas.create_text(
                    15, 15,
                    text=str(i + 1),
                    fill="white",
                    font=("TkDefaultFont", 10, "bold")
                )
            else:
                # 未完成步驟
                indicator = indicator_canvas.create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("border"),
                    outline=self.theme_manager.get_color("border")
                )
                # 添加數字
                indicator_canvas.create_text(
                    15, 15,
                    text=str(i + 1),
                    fill=self.theme_manager.get_color("text_secondary"),
                    font=("TkDefaultFont", 10)
                )

            self.step_indicators.append(indicator_canvas)

            # 建立步驟標籤
            label = ttk.Label(
                step_frame,
                text=step,
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary") if i <= self.current_step else self.theme_manager.get_color("text_secondary")
            )
            label.pack(side="top", pady=(5, 0))
            self.step_labels.append(label)

            # 如果不是最後一個步驟，添加連接線
            if i < len(self.steps) - 1:
                line_canvas = tk.Canvas(
                    container,
                    width=50,
                    height=30,
                    bg=self.theme_manager.get_color("background"),
                    highlightthickness=0
                )
                line_canvas.pack(side="left")

                # 繪製連接線
                line = line_canvas.create_line(
                    0, 15, 50, 15,
                    fill=self.theme_manager.get_color("success") if i < self.current_step else self.theme_manager.get_color("border"),
                    width=2
                )
                self.step_lines.append(line_canvas)

    def set_current_step(self, step_index: int):
        """設定當前步驟

        Args:
            step_index: 步驟索引 (從 0 開始)
        """
        if step_index < 0 or step_index >= len(self.steps):
            raise ValueError(f"步驟索引必須在 0 到 {len(self.steps) - 1} 之間")

        # 更新當前步驟
        self.current_step = step_index

        # 更新步驟指示器
        for i in range(len(self.steps)):
            # 清除畫布
            self.step_indicators[i].delete("all")

            # 繪製圓圈
            if i < self.current_step:
                # 已完成步驟
                self.step_indicators[i].create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("success"),
                    outline=self.theme_manager.get_color("success")
                )
                # 添加勾號
                self.step_indicators[i].create_text(
                    15, 15,
                    text="✓",
                    fill="white",
                    font=("TkDefaultFont", 12, "bold")
                )
            elif i == self.current_step:
                # 當前步驟
                self.step_indicators[i].create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("primary"),
                    outline=self.theme_manager.get_color("primary")
                )
                # 添加數字
                self.step_indicators[i].create_text(
                    15, 15,
                    text=str(i + 1),
                    fill="white",
                    font=("TkDefaultFont", 10, "bold")
                )
            else:
                # 未完成步驟
                self.step_indicators[i].create_oval(
                    5, 5, 25, 25,
                    fill=self.theme_manager.get_color("border"),
                    outline=self.theme_manager.get_color("border")
                )
                # 添加數字
                self.step_indicators[i].create_text(
                    15, 15,
                    text=str(i + 1),
                    fill=self.theme_manager.get_color("text_secondary"),
                    font=("TkDefaultFont", 10)
                )

            # 更新標籤顏色
            self.step_labels[i].config(
                foreground=self.theme_manager.get_color("text_primary") if i <= self.current_step else self.theme_manager.get_color("text_secondary")
            )

            # 更新連接線
            if i < len(self.steps) - 1:
                self.step_lines[i].delete("all")
                self.step_lines[i].create_line(
                    0, 15, 50, 15,
                    fill=self.theme_manager.get_color("success") if i < self.current_step else self.theme_manager.get_color("border"),
                    width=2
                )

    def next_step(self):
        """前進到下一步"""
        if self.current_step < len(self.steps) - 1:
            self.set_current_step(self.current_step + 1)

    def previous_step(self):
        """返回上一步"""
        if self.current_step > 0:
            self.set_current_step(self.current_step - 1)

    def set_step(self, step_index: int):
        """直接設定步驟索引

        Args:
            step_index: 步驟索引 (從 0 開始)
        """
        self.set_current_step(step_index)
