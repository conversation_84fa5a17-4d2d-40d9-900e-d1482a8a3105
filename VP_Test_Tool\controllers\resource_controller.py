"""資源調整控制器"""
import logging
from tkinter import filedialog
import csv
import re
import threading
import queue
import time
import concurrent.futures
from functools import partial
import json
from typing import List, Tuple, Optional, Dict, Any
from models.member import MemberService, Member
from views.resource_panel import ResourcePanel
from utils.constants import API_URLS
from utils.api_error_handler import ApiErrorHandler
from utils.thread_manager import thread_manager
from utils.file_manager import file_manager
from utils.batch_processor import batch_processor
from utils.data_validator import data_validator, ValidationError
from utils.http_client_enhanced import HttpClientEnhanced
http_client_enhanced = HttpClientEnhanced()
from utils.exceptions import (
    ResourceUpdateError, ResourceQueryError, ResourceNotFoundError,
    FileReadError, FileFormatError, ThreadCreateError
)

logger = logging.getLogger(__name__)

class ResourceController:
    """資源調整控制器"""
    def __init__(self, resource_panel: ResourcePanel, member_service: MemberService, http_client=None):
        self.view = resource_panel
        self.member_service = member_service
        self.http_client = http_client or member_service.http_client
        self.current_member: Optional[Member] = None
        self.batch_accounts: List[Tuple[str, str]] = []  # (account, member_id)

        # 用於多線程處理
        self.log_queue = queue.Queue()  # 用於存放日誌訊息
        self.is_processing = False  # 批次處理狀態標記

        self._init_bindings()

        # 啟動日誌更新定時器
        self._start_log_updater()

    def _init_bindings(self):
        """初始化事件綁定"""
        # 查詢按鈕事件
        self.view.btn_query.config(command=self._handle_query)
        # 更新資源按鈕事件
        self.view.btn_update.config(command=self._handle_update_resource)
        # 清除參數按鈕事件
        self.view.btn_clear.config(command=self._handle_clear_params)
        # 清除會員資訊按鈕事件
        self.view.btn_clear_member.config(command=self.view.clear_member_info)
        # 匯入帳號按鈕事件
        self.view.btn_import.config(command=self._handle_import_accounts)
        # 批次更新按鈕事件
        self.view.btn_batch_update.config(command=self._handle_batch_update)

    def _start_log_updater(self):
        """啟動日誌更新定時器 - 優化版本"""
        def update_logs():
            # 從佇列中取出日誌訊息並顯示
            # 每次最多取出20條訊息，避免一次處理太多導致UI凍結
            messages = []
            for _ in range(min(20, self.log_queue.qsize())):
                if not self.log_queue.empty():
                    messages.append(self.log_queue.get())
                    self.log_queue.task_done()

            # 如果有訊息要顯示
            if messages:
                # 使用一次性更新方式，減少UI重繪次數
                self.view.log_batch(messages)

            # 更新批次處理按鈕狀態
            if self.is_processing:
                if self.view.btn_batch_update['state'] != 'disabled':
                    self.view.btn_batch_update.config(state='disabled')
                    self.view.btn_import.config(state='disabled')
            else:
                if self.view.btn_batch_update['state'] != 'normal':
                    self.view.btn_batch_update.config(state='normal')
                    self.view.btn_import.config(state='normal')

            # 根據佇列大小動態調整更新間隔
            queue_size = self.log_queue.qsize()
            if queue_size > 100:
                # 如果佇列很大，加快更新频率
                update_interval = 50  # 50毫秒
            elif queue_size > 50:
                update_interval = 80  # 80毫秒
            else:
                update_interval = 100  # 100毫秒

            # 設定下一次更新
            self.view.after(update_interval, update_logs)

        # 啟動定時器
        update_logs()

    def _log_to_queue(self, message: str):
        """將日誌訊息加入佇列"""
        self.log_queue.put(message)

    def _handle_query(self):
        """處理查詢事件"""
        # 停止閃爍效果（如果有的話）
        if hasattr(self.view, '_stop_button_blink'):
            self.view._stop_button_blink()
        try:
            account = self.view.entry_account.get().strip()
            member_id = self.view.entry_member_id.get().strip()
            db_source = self.view.db_source.get()  # 獲取選擇的資料庫來源

            if not account and not member_id:
                raise ValueError("請輸入帳號或會員ID")

            # 使用真實 API 查詢會員

            # 顯示查詢中訊息
            self.view.log(f"🔍 正在查詢會員資訊... (資料庫: {db_source})")

            # 根據資料庫來源構建查詢 SQL
            table_name = f"{db_source}.Member"

            if account:
                query_sql = f"SELECT * FROM {table_name} WHERE Account='{account}'"
            else:
                query_sql = f"SELECT * FROM {table_name} WHERE MemberId='{member_id}'"

            # 準備查詢參數
            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": query_sql
            }

            # 顯示 API 請求詳細資訊
            self.view.log(f"🔹 API: {API_URLS['QUERY_MEMBER']}")
            self.view.log(f"📦 Payload:")
            self.view.log(json.dumps(query_payload, indent=2))

            # 發送請求
            response = self.http_client.post(
                API_URLS["QUERY_MEMBER"],
                json_data=query_payload
            )

            # 顯示回應詳細資訊
            self.view.log(f"✅ 回應碼: 200")
            self.view.log(f"📝 回應內容: {json.dumps(response, indent=2)}")

            # 使用 API 錯誤處理機制
            success, error_msg, member_data = ApiErrorHandler.handle_member_query_error(response, account, member_id)
            if success:
                self.current_member = Member(
                    member_id=str(member_data["MemberId"]),
                    account=member_data["Account"],
                    agent_code=member_data["AgentCode"],
                    sub_agent_code=member_data["SubAgentCode"],
                    currency=member_data["Currency"],
                    vip_level=member_data.get("VipLv", 0),
                    status=member_data.get("Status", "Active"),
                    db_source=db_source  # 設定資料庫來源
                )

                # 更新會員資訊顯示
                self.view.log(f"✅ 會員查詢成功！")
                self.view.log(f"會員資訊: (ID: {self.current_member.member_id})")
                self.view.log(f"資料庫來源: {self.current_member.db_source}")
                self.view.log(f"帳號: {self.current_member.account}")
                self.view.log(f"代理商: {self.current_member.agent_code}, 子代理商: {self.current_member.sub_agent_code}")
                self.view.log(f"幣別: {self.current_member.currency}, VIP等級: {self.current_member.vip_level}")
            else:
                self.current_member = None
                raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"查詢失敗: {e}")
            self.current_member = None
            self.view.log(f"❌ 查詢失敗: {e}")

    def _handle_update_resource(self):
        """處理更新資源事件"""
        if not self.current_member:
            self.view.log(f"\n❌ 更新資源失敗: 請先查詢會員\n")
            return

        try:
            # 取得輸入值
            coin = self.view.entry_coin.get().strip()
            vip_level = self.view.entry_vip.get().strip()
            gem = self.view.entry_gem.get().strip()
            lottery = self.view.entry_lottery.get().strip()

            if not any([coin, vip_level, gem, lottery]):
                self.view.log(f"\n❌ 更新資源失敗: 請至少設定一種資源\n")
                return

            # 記錄開始更新
            self.view.log(f"🔄 開始更新資源: 會員ID={self.current_member.member_id}")

            # 實際更新資源
            update_items = []
            success_count = 0

            # 準備更新資源

            # 更新金幣
            if coin:
                try:
                    coin_value = self._parse_number(coin)
                    self.view.log(f"正在更新金幣: {coin_value}...")

                    # 使用新的 API 更新金幣
                    payload = {
                        "agent": self.current_member.agent_code,
                        "account": self.current_member.account,
                        "newBalance": str(coin_value)
                    }

                    # 顯示 API 請求詳細資訊
                    self.view.log(f"🔹 API: {API_URLS['UPDATE_COIN']}")
                    self.view.log(f"📦 Payload:")
                    self.view.log(json.dumps(payload, indent=2))

                    # 發送請求
                    response = self.http_client.post(
                        API_URLS["UPDATE_COIN"],
                        json_data=payload
                    )

                    # 顯示回應詳細資訊
                    self.view.log(f"✅ 回應碼: 200")
                    self.view.log(f"📝 回應內容: {json.dumps(response, indent=2)}")

                    # 使用 API 錯誤處理機制
                    success, error_msg = self.http_client.handle_api_error(API_URLS["UPDATE_COIN"], response)
                    if success:
                        self.view.log(f"\n✅ 金幣更新成功！\n")
                        update_items.append(f"金幣: {coin_value}")
                        success_count += 1
                    else:
                        self.view.log(f"❌ {error_msg}")
                except Exception as e:
                    self.view.log(f"❌ 金幣更新失敗: {e}")

            # 更新 VIP 等級
            if vip_level:
                try:
                    vip_value = self._parse_number(vip_level)

                    # 驗證 VIP 等級範圍 (0-6)
                    if vip_value < 0 or vip_value > 6:
                        raise ValueError(f"VIP 等級必須在 0-6 範圍內，當前值: {vip_value}")

                    self.view.log(f"正在更新 VIP 等級: {vip_value}...")

                    # 使用真實 API 更新 VIP
                    payload = {
                        "accountInfo": {
                            "account": self.current_member.account,
                            "agentCode": self.current_member.agent_code,
                            "subAgentCode": self.current_member.sub_agent_code,
                            "currency": self.current_member.currency
                        },
                        "debugUpdateVip": vip_value
                    }

                    # 顯示 API 請求詳細資訊
                    self.view.log(f"🔹 API: {API_URLS['UPDATE_VIP']}")
                    self.view.log(f"📦 Payload:")
                    self.view.log(json.dumps(payload, indent=2))

                    # 發送請求
                    response = self.http_client.post(
                        API_URLS["UPDATE_VIP"],
                        json_data=payload
                    )

                    # 顯示回應詳細資訊
                    self.view.log(f"✅ 回應碼: 200")
                    self.view.log(f"📝 回應內容: {json.dumps(response, indent=2)}")

                    # 使用 API 錯誤處理機制
                    success, error_msg = self.http_client.handle_api_error(API_URLS["UPDATE_VIP"], response)
                    if success:
                        self.view.log(f"\n✅ VIP 等級更新成功！\n")
                        update_items.append(f"VIP 等級: {vip_value}")
                        success_count += 1
                    else:
                        self.view.log(f"❌ {error_msg}")
                except Exception as e:
                    self.view.log(f"❌ VIP 等級更新失敗: {e}")

            # 更新寶石
            if gem:
                try:
                    gem_value = self._parse_number(gem)
                    self.view.log(f"正在更新寶石: {gem_value}...")

                    # 使用真實 API 更新寶石
                    payload = {
                        "MemberId": int(self.current_member.member_id),
                        "AccountInfo": {
                            "account": self.current_member.account,
                            "agentCode": self.current_member.agent_code,
                            "subAgentCode": self.current_member.sub_agent_code,
                            "currency": self.current_member.currency
                        },
                        "source": "VP_Tool",
                        "description": "AddGem",
                        "rewardNodeList": [
                            {
                                "type": "Gem",
                                "id": "1",
                                "num": gem_value
                            }
                        ]
                    }

                    # 顯示 API 請求詳細資訊
                    self.view.log(f"🔹 API: {API_URLS['UPDATE_GEM']}")
                    self.view.log(f"📦 Payload:")
                    self.view.log(json.dumps(payload, indent=2))

                    # 發送請求
                    response = self.http_client.post(
                        API_URLS["UPDATE_GEM"],
                        json_data=payload
                    )

                    # 顯示回應詳細資訊
                    self.view.log(f"✅ 回應碼: 200")
                    self.view.log(f"📝 回應內容: {json.dumps(response, indent=2)}")

                    # 使用 API 錯誤處理機制
                    success, error_msg = self.http_client.handle_api_error(API_URLS["UPDATE_GEM"], response)
                    if success:
                        self.view.log(f"\n✅ 寶石更新成功！\n")
                        update_items.append(f"寶石: {gem_value}")
                        success_count += 1
                    else:
                        self.view.log(f"❌ {error_msg}")
                except Exception as e:
                    self.view.log(f"❌ 寶石更新失敗: {e}")

            # 更新樂透券
            if lottery:
                try:
                    lottery_value = self._parse_number(lottery)
                    self.view.log(f"正在更新樂透券: {lottery_value}...")

                    # 使用真實 API 更新樂透券
                    payload = {
                        "memberId": int(self.current_member.member_id),  # 添加會員ID
                        "service": "VP_Tool",
                        "rewardNode": [
                            {
                                "type": "LotteryFixedPool",
                                "id": "19",
                                "num": lottery_value
                            }
                        ]
                    }

                    # 顯示 API 請求詳細資訊
                    self.view.log(f"🔹 API: {API_URLS['UPDATE_LOTTERY']}")
                    self.view.log(f"📦 Payload:")
                    self.view.log(json.dumps(payload, indent=2))

                    # 發送請求
                    response = self.http_client.post(
                        API_URLS["UPDATE_LOTTERY"],
                        json_data=payload
                    )

                    # 顯示回應詳細資訊
                    self.view.log(f"✅ 回應碼: 200")
                    self.view.log(f"📝 回應內容: {json.dumps(response, indent=2)}")

                    # 使用 API 錯誤處理機制
                    success, error_msg = self.http_client.handle_api_error(API_URLS["UPDATE_LOTTERY"], response)
                    if success:
                        self.view.log(f"\n✅ 樂透券更新成功！\n")
                        update_items.append(f"樂透券: {lottery_value}")
                        success_count += 1
                    else:
                        self.view.log(f"❌ {error_msg}")
                except Exception as e:
                    logger.error(f"更新樂透券失敗: {e}")
                    self.view.log(f"❌ 更新樂透券失敗: {e}")

            # 顯示更新結果
            if success_count > 0:
                update_summary = ", ".join(update_items)
                success_message = f"會員 {self.current_member.account} 資源更新成功: {update_summary}"
                self.view.log(f"\n✅ {success_message}\n")
                # 不顯示彈出訊息，只在日誌中顯示成功訊息
                # 不清除輸入框，保持原值
            else:
                self.view.log(f"\n❌ 所有資源更新均失敗，請查看上方日誌\n")

        except Exception as e:
            logger.error(f"更新資源失敗: {e}")
            self.view.log(f"\n❌ 更新資源失敗: {e}\n")

    def _handle_import_accounts(self):
        """處理匯入帳號事件"""
        try:
            file_path = filedialog.askopenfilename(
                title="選擇帳號檔案",
                filetypes=[("CSV 檔案", "*.csv"), ("所有檔案", "*.*")]
            )

            if not file_path:
                return

            self.batch_accounts = []

            try:
                # 使用 FileManager 讀取 CSV 文件
                csv_data = file_manager.read_csv_file(file_path, has_header=True)

                for row in csv_data:
                    # 檢查 CSV 格式
                    if 'account' in row and 'member_id' in row:
                        account, member_id = row['account'], row['member_id']
                        self.batch_accounts.append((account, member_id))
                    elif 'account' in row:
                        account = row['account']
                        self.batch_accounts.append((account, ""))
                    elif len(row) >= 2:  # 如果沒有標題行，嘗試使用索引
                        keys = list(row.keys())
                        account, member_id = row[keys[0]], row[keys[1]]
                        self.batch_accounts.append((account, member_id))
                    elif len(row) == 1:
                        keys = list(row.keys())
                        account = row[keys[0]]
                        self.batch_accounts.append((account, ""))
            except FileFormatError as e:
                # 如果 CSV 格式不正確，嘗試使用基本文本讀取
                self.view.log(f"⚠️ CSV 格式不正確，嘗試使用基本文本讀取: {e}")
                content = file_manager.read_text_file(file_path)
                lines = content.strip().split('\n')

                # 跳過標題行
                if len(lines) > 1:
                    lines = lines[1:]

                for line in lines:
                    parts = line.strip().split(',')
                    if len(parts) >= 2:
                        account, member_id = parts[0], parts[1]
                        self.batch_accounts.append((account, member_id))
                    elif len(parts) == 1:
                        account = parts[0]
                        self.batch_accounts.append((account, ""))

            # 更新批次狀態標籤
            self.view.update_batch_status(f"已匯入 {len(self.batch_accounts)} 個帳號")
            # 只記錄到操作日誌，不顯示彈出視窗
            self.view.log(f"✅ 匯入成功: 已匯入 {len(self.batch_accounts)} 個帳號")

        except FileReadError as e:
            logger.error(f"讀取文件失敗: {e}")
            self.view.log(f"❌ 讀取文件失敗: {e}")
        except Exception as e:
            logger.error(f"匯入帳號失敗: {e}")
            # 只記錄到操作日誌，不顯示彈出視窗
            self.view.log(f"❌ 匯入失敗: {e}")

    def _process_single_account_update(self, account, member_id, resource_settings):
        """處理單個帳號的資源更新

        Args:
            account: 帳號
            member_id: 會員ID
            resource_settings: 資源設定字典

        Returns:
            tuple: (成功狀態, 帳號, 會員ID, 更新項目列表, 錯誤訊息)
        """
        try:
            # 從設定中提取資源值
            coin = resource_settings.get('coin', '')
            vip_level = resource_settings.get('vip_level', '')
            gem = resource_settings.get('gem', '')
            lottery = resource_settings.get('lottery', '')

            # 查詢會員資訊
            # 使用批次處理區域的資料庫來源
            db_source = resource_settings.get('db_source', 'OCMS')
            table_name = f"{db_source}.Member"

            # 準備查詢參數
            if account:
                query_sql = f"SELECT * FROM {table_name} WHERE Account='{account}'"
            else:
                query_sql = f"SELECT * FROM {table_name} WHERE MemberId='{member_id}'"

            query_payload = {
                "action": "query",
                "serverTag": "igaming-mysql-main",
                "sql": query_sql
            }

            # 使用增強版 HTTP 客戶端發送請求
            response = http_client_enhanced.post(
                API_URLS["QUERY_MEMBER"],
                json_data=query_payload
            )

            # 檢查回應是否成功
            if not response.get("success", False):
                error_msg = response.get("error", "查詢會員失敗")
                return (False, account, member_id, [], error_msg)

            # 解析回應數據
            result_data = response.get("data", {})
            if not result_data or not isinstance(result_data, list) or len(result_data) == 0:
                return (False, account, member_id, [], "找不到會員資料")

            # 建立會員物件
            data = result_data[0]

            try:
                # 驗證會員數據
                member_id_value = data_validator.validate_required(data.get("MemberId"), "會員ID")
                account_value = data_validator.validate_required(data.get("Account"), "帳號")
                agent_code = data_validator.validate_required(data.get("AgentCode"), "代理代碼")
                sub_agent_code = data.get("SubAgentCode", "")
                currency = data_validator.validate_required(data.get("Currency"), "幣別")
            except ValidationError as e:
                return (False, account, member_id, [], f"會員資料驗證失敗: {str(e)}")

            # 建立會員物件
            member = Member(
                member_id=str(member_id_value),
                account=account_value,
                agent_code=agent_code,
                sub_agent_code=sub_agent_code,
                currency=currency,
                vip_level=data.get("VipLv", 0),
                status=data.get("Status", "Active"),
                db_source=db_source  # 設定資料庫來源
            )

            # 更新資源
            update_items = []
            success_count = 0
            error_messages = []

            # 更新金幣
            if coin is not None:
                try:
                    # 使用新的 API 更新金幣
                    payload = {
                        "agent": member.agent_code,
                        "account": member.account,
                        "newBalance": str(coin)
                    }

                    # 使用增強版 HTTP 客戶端發送請求
                    response = http_client_enhanced.post(
                        API_URLS["UPDATE_COIN"],
                        json_data=payload
                    )

                    # 使用 API 錯誤處理機制處理回應
                    success, error_msg = http_client_enhanced.handle_api_error(API_URLS["UPDATE_COIN"], response)
                    if success:
                        update_items.append(f"金幣: {coin}")
                        success_count += 1
                    else:
                        error_messages.append(f"金幣更新失敗: {error_msg}")
                except Exception as e:
                    error_messages.append(f"金幣更新異常: {str(e)}")

            # 更新 VIP 等級
            if vip_level is not None:
                try:
                    # 使用真實 API 更新 VIP
                    payload = {
                        "accountInfo": {
                            "account": member.account,
                            "agentCode": member.agent_code,
                            "subAgentCode": member.sub_agent_code,
                            "currency": member.currency
                        },
                        "debugUpdateVip": vip_level
                    }

                    # 使用增強版 HTTP 客戶端發送請求
                    response = http_client_enhanced.post(
                        API_URLS["UPDATE_VIP"],
                        json_data=payload
                    )

                    # 檢查回應是否成功
                    if response.get("success", False):
                        update_items.append(f"VIP 等級: {vip_level}")
                        success_count += 1
                    else:
                        error_msg = response.get("error", "更新VIP等級失敗")
                        error_messages.append(f"VIP等級更新失敗: {error_msg}")
                except Exception as e:
                    error_messages.append(f"VIP等級更新異常: {str(e)}")

            # 更新寶石
            if gem is not None:
                try:
                    # 使用真實 API 更新寶石
                    payload = {
                        "MemberId": int(member.member_id),
                        "AccountInfo": {
                            "account": member.account,
                            "agentCode": member.agent_code,
                            "subAgentCode": member.sub_agent_code,
                            "currency": member.currency
                        },
                        "source": "VP_Tool",
                        "description": "AddGem",
                        "rewardNodeList": [
                            {
                                "type": "Gem",
                                "id": "1",
                                "num": gem
                            }
                        ]
                    }

                    # 使用增強版 HTTP 客戶端發送請求
                    response = http_client_enhanced.post(
                        API_URLS["UPDATE_GEM"],
                        json_data=payload
                    )

                    # 檢查回應是否成功
                    if response.get("success", False):
                        update_items.append(f"寶石: {gem}")
                        success_count += 1
                    else:
                        error_msg = response.get("error", "更新寶石失敗")
                        error_messages.append(f"寶石更新失敗: {error_msg}")
                except Exception as e:
                    error_messages.append(f"寶石更新異常: {str(e)}")

            # 更新樂透券
            if lottery is not None:
                try:
                    # 使用真實 API 更新樂透券
                    payload = {
                        "memberId": int(member.member_id),
                        "service": "VP_Tool",
                        "rewardNode": [
                            {
                                "type": "LotteryFixedPool",
                                "id": "19",
                                "num": lottery
                            }
                        ]
                    }

                    # 使用增強版 HTTP 客戶端發送請求
                    response = http_client_enhanced.post(
                        API_URLS["UPDATE_LOTTERY"],
                        json_data=payload
                    )

                    # 檢查回應是否成功
                    if response.get("success", False):
                        update_items.append(f"樂透券: {lottery}")
                        success_count += 1
                    else:
                        error_msg = response.get("error", "更新樂透券失敗")
                        error_messages.append(f"樂透券更新失敗: {error_msg}")
                except Exception as e:
                    error_messages.append(f"樂透券更新異常: {str(e)}")

            # 返回結果
            result = {
                "update_items": update_items,
                "error_msg": "; ".join(error_messages) if error_messages else None
            }

            if success_count > 0:
                return (True, member.account, member.member_id, result)
            else:
                error_msg = "所有資源更新均失敗" + (f": {'; '.join(error_messages)}" if error_messages else "")
                result["error_msg"] = error_msg
                return (False, account, member_id, result)

        except Exception as e:
            logger.error(f"處理帳號 {account} 更新失敗: {e}")
            return (False, account, member_id, {"error_msg": str(e)})

    def _handle_batch_update(self):
        """處理批次更新事件 - 使用 BatchProcessor 處理"""
        # 如果已經在處理中，則不再執行
        if self.is_processing:
            return

        if not self.batch_accounts:
            # 只記錄到操作日誌，不顯示彈出視窗
            self._log_to_queue(f"❌ 批次更新失敗: 請先匯入帳號")
            return

        try:
            # 取得並驗證輸入值
            try:
                coin = self.view.entry_coin.get().strip()
                if coin:
                    coin = data_validator.validate_float(coin, "金幣", min_value=0, required=False)

                vip_level = self.view.entry_vip.get().strip()
                if vip_level:
                    vip_level = data_validator.validate_integer(vip_level, "VIP等級", min_value=0, max_value=6, required=False)

                gem = self.view.entry_gem.get().strip()
                if gem:
                    gem = data_validator.validate_integer(gem, "寶石", min_value=0, required=False)

                lottery = self.view.entry_lottery.get().strip()
                if lottery:
                    lottery = data_validator.validate_integer(lottery, "樂透券", min_value=0, required=False)
            except ValidationError as e:
                self._log_to_queue(f"❌ 輸入驗證失敗: {str(e)}")
                return

            # 取得批次處理的資料庫來源
            batch_db_source = self.view.batch_db_source.get()

            if not any([coin, vip_level, gem, lottery]):
                # 只記錄到操作日誌，不顯示彈出視窗
                self._log_to_queue(f"❌ 批次更新失敗: 請至少設定一種資源")
                return

            # 將資源設定儲存到字典中
            resource_settings = {
                'coin': coin,
                'vip_level': vip_level,
                'gem': gem,
                'lottery': lottery,
                'db_source': batch_db_source  # 添加資料庫來源
            }

            # 設置處理中狀態
            self.is_processing = True

            # 更新批次狀態標籤 - 使用隊列處理 UI 更新
            self.view.update_batch_status("批次處理中...")

            # 顯示處理中訊息
            self._log_to_queue(f"\n🔄 開始批次更新 {len(self.batch_accounts)} 個帳號的資源...")
            self._log_to_queue(f"📊 使用資料庫來源: {batch_db_source}")

            # 顯示資源設定訊息
            resource_info = []
            if resource_settings.get('coin') is not None:
                resource_info.append(f"金幣: {resource_settings['coin']}")
            if resource_settings.get('vip_level') is not None:
                resource_info.append(f"VIP等級: {resource_settings['vip_level']}")
            if resource_settings.get('gem') is not None:
                resource_info.append(f"寶石: {resource_settings['gem']}")
            if resource_settings.get('lottery') is not None:
                resource_info.append(f"樂透券: {resource_settings['lottery']}")

            self._log_to_queue(f"資源設定: {', '.join(resource_info)}")
            self._log_to_queue("⏳ 批次處理已在背景執行，請稍候...")

            # 配置批量處理器
            batch_processor.max_workers = min(10, len(self.batch_accounts))

            # 設置回調函數
            batch_processor.on_progress = self._on_batch_progress
            batch_processor.on_item_complete = self._on_batch_item_complete
            batch_processor.on_complete = self._on_batch_complete
            batch_processor.on_error = self._on_batch_error

            # 啟動批量處理
            thread_id = thread_manager.create_thread(
                name="batch_update_worker",
                target=self._batch_update_thread,
                args=(resource_settings,),
                daemon=True
            )
            logger.info(f"批次更新工作線程已啟動: {thread_id}")

            # 添加暫停和恢復按鈕
            self.view.show_batch_control_buttons(
                pause_command=batch_processor.pause,
                resume_command=batch_processor.resume,
                cancel_command=batch_processor.cancel
            )

        except Exception as e:
            self.is_processing = False
            logger.error(f"批次更新失敗: {e}")
            self._log_to_queue(f"❌ 批次更新失敗: {str(e)}")
            self.view.update_batch_status("批次處理失敗")

    def _batch_update_thread(self, resource_settings):
        """批次更新線程函數 - 使用 BatchProcessor 處理"""
        try:
            # 獲取批次帳號
            batch_accounts = self.batch_accounts.copy()

            # 使用 BatchProcessor 處理批次帳號
            batch_processor.process(
                items=batch_accounts,
                process_func=lambda item: self._process_single_account_update(
                    account=item[0],
                    member_id=item[1],
                    resource_settings=resource_settings
                ),
                batch_size=20,
                update_interval=0.1
            )

        except Exception as e:
            logger.error(f"批次更新線程失敗: {e}")
            self._log_to_queue(f"❌ 批次更新失敗: {str(e)}")
            # 使用隊列處理 UI 更新
            self.view.update_batch_status("批次處理失敗")

        finally:
            # 處理完成，重設狀態
            self.is_processing = False

            # 隱藏批次控制按鈕
            self.view.hide_batch_control_buttons()

            # 清理線程管理器中的已完成線程
            thread_manager.cleanup()

    def _on_batch_progress(self, current, total, percentage):
        """批次處理進度回調函數"""
        # 使用隊列處理 UI 更新，避免在非主執行緒中直接更新 UI
        # 更新進度條
        self.view.update_batch_progress(percentage)

        # 更新批次狀態標籤
        status_text = f"處理中... {current}/{total} ({int(percentage)}%)"
        self.view.update_batch_status(status_text)

        # 每處理10%的帳號顯示一次進度
        if current % max(1, total // 10) == 0 or current == total:
            self._log_to_queue(f"⏳ 處理進度: {current}/{total} ({int(percentage)}%)")

    def _on_batch_item_complete(self, item, result, success):
        """批次處理項目完成回調函數"""
        account, member_id = item

        if success:
            # 解析結果
            if isinstance(result, dict):
                update_items = result.get("update_items", [])
                update_summary = ", ".join(update_items)
                self._log_to_queue(f"  ✅ 帳號 {account} (會員ID: {member_id}) 更新成功: {update_summary}")
            else:
                # 舊格式兼容
                try:
                    # 忽略返回的帳號和會員ID，使用傳入的參數
                    _, _, _, update_items, _ = result
                    update_summary = ", ".join(update_items)
                    self._log_to_queue(f"  ✅ 帳號 {account} (會員ID: {member_id}) 更新成功: {update_summary}")
                except:
                    self._log_to_queue(f"  ✅ 帳號 {account} (會員ID: {member_id}) 更新成功")
        else:
            # 獲取錯誤訊息
            if isinstance(result, dict):
                error_msg = result.get("error_msg", "未知錯誤")
            else:
                # 舊格式兼容
                try:
                    # 忽略返回的帳號和會員ID，使用傳入的參數
                    _, _, _, _, error_msg = result
                    if not error_msg:
                        error_msg = "未知錯誤"
                except:
                    error_msg = str(result)

            self._log_to_queue(f"  ❌ 帳號 {account} (會員ID: {member_id}) 處理失敗: {error_msg}")

    def _on_batch_complete(self, results, success_count, fail_count):
        """批次處理完成回調函數"""
        # 計算總耗時
        batch_status = batch_processor.get_status()
        elapsed_time = batch_status.get("elapsed_time", 0)

        # 顯示結果統計
        self._log_to_queue(f"\n✅ 批次更新完成: 成功 {success_count} 個帳號, 失敗 {fail_count} 個帳號")
        self._log_to_queue(f"⏱️ 總耗時: {elapsed_time:.2f} 秒")

        # 更新批次狀態標籤
        if success_count > 0:
            status_text = f"完成: 成功 {success_count}, 失敗 {fail_count}"
            self.view.update_batch_status(status_text)
        else:
            self.view.update_batch_status("批次處理失敗")
            self._log_to_queue(f"❌ 批次更新失敗，所有帳號處理均失敗")

        # 保存結果到文件
        try:
            # 創建結果摘要
            summary = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_accounts": success_count + fail_count,
                "success_count": success_count,
                "fail_count": fail_count,
                "elapsed_time": elapsed_time,
                "results": results
            }

            # 使用 FileManager 保存結果
            result_dir = "results"
            file_manager.ensure_directory(result_dir)

            # 生成文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            result_file = f"{result_dir}/batch_update_{timestamp}.json"

            # 保存 JSON 文件
            file_manager.write_json_file(result_file, summary)
            self._log_to_queue(f"📄 結果已保存到文件: {result_file}")

            # 同時保存 CSV 格式的結果
            csv_file = f"{result_dir}/batch_update_{timestamp}.csv"
            csv_data = []

            for result in results:
                item = result.get("item", ("", ""))
                account, member_id = item
                result_data = result.get("result", {})

                csv_entry = {
                    "account": account,
                    "member_id": member_id,
                    "success": "成功" if result.get("success", False) else "失敗",
                    "update_items": ", ".join(result_data.get("update_items", [])) if isinstance(result_data, dict) else "",
                    "error_msg": result_data.get("error_msg", "") if isinstance(result_data, dict) else str(result_data),
                    "timestamp": result.get("timestamp", "")
                }
                csv_data.append(csv_entry)

            # 保存 CSV 文件
            file_manager.write_csv_file(csv_file, csv_data)
            self._log_to_queue(f"📄 結果已保存到 CSV 文件: {csv_file}")

        except Exception as e:
            logger.error(f"保存結果失敗: {e}")
            self._log_to_queue(f"⚠️ 保存結果失敗: {e}")

    def _on_batch_error(self, error):
        """批次處理錯誤回調函數"""
        logger.error(f"批次處理錯誤: {error}")
        self._log_to_queue(f"❌ 批次處理錯誤: {str(error)}")
        self.view.update_batch_status("批次處理失敗")

    def _log_to_queue(self, message: str):
        """將日誌訊息加入佇列"""
        self.log_queue.put(message)

    def _handle_clear_params(self):
        """處理清除參數事件"""
        # 呼叫視圖的清除參數方法
        self.view.clear_resource_params()

    def _parse_number(self, value):
        """將格式化的數字字串轉換為數字（支援小數點）

        Args:
            value: 要轉換的字串

        Returns:
            float 或 int: 轉換後的數值
        """
        if not value:
            return 0

        # 移除所有非數字字元（除了負號和小數點）
        clean_value = re.sub(r'[^0-9\-\.]', '', value)

        try:
            # 嘗試轉換為浮點數
            parsed_value = float(clean_value)

            # 如果是整數值，轉換為整數返回
            if parsed_value.is_integer():
                return int(parsed_value)

            # 否則返回浮點數
            return parsed_value
        except ValueError:
            return 0
