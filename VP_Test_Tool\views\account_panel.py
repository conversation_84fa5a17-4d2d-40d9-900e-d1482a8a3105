"""帳號產生器面板"""
import tkinter as tk
from tkinter import ttk
from tkinter.scrolledtext import ScrolledText
from utils.constants import PADDING
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from widgets.modern_button import ModernButton

class AccountPanel(ttk.Frame):
    """帳號產生器面板"""
    def __init__(self, parent):
        super().__init__(parent)
        # 初始化主題管理器
        self.theme_manager = ThemeManager()
        self._init_ui()
        self._setup_button_styles()

    def _init_ui(self):
        """初始化 UI"""
        # 建立主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 從幫助按鈕模組導入 HelpButton
        from widgets.help_button import HelpButton

        # 添加幫助按鈕
        help_text = """帳號產生器可以幫助您批量產生會員帳號，支援不同代理商。

使用步驟：
1. 設定參數：輸入要產生的帳號數量
2. 選擇代理商：從下拉選單中選擇代理商代碼
3. 選擇子代理商：從下拉選單中選擇子代理商
4. 產生帳號：點擊「創建帳號」按鈕產生帳號
5. 匯出結果：點擊「下載帳號列表」按鈕匯出結果

所有操作日誌將顯示在右側的「操作日誌與說明」區域。"""
        self.help_button = HelpButton(main_frame, help_text=help_text, title="帳號產生器幫助")
        self.help_button.pack(side=tk.TOP, anchor=tk.NW, padx=PADDING, pady=PADDING)

        # 建立主容器框架
        container_frame = tk.Frame(main_frame, bg=self.theme_manager.get_color("surface"))
        container_frame.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 左側設定區域 - 佔空間的 50%
        left_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING/2))

        # 右側日誌區域 - 佔空間的 50%
        right_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING/2, 0))

        # 帳號產生設定區域 - 使用卡片式設計
        from widgets.card_frame import CardFrame
        settings_card = CardFrame(left_frame, title="帳號產生設定", icon=IconManager.get('user'))
        settings_card.pack(fill=tk.BOTH, expand=True)

        # 取得內容框架
        settings_frame = settings_card.get_content_frame()

        # 建立左右布局框架
        settings_content = tk.Frame(settings_frame, bg=self.theme_manager.get_color("surface"))
        settings_content.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 左側參數設定區域
        params_frame = tk.Frame(settings_content, bg=self.theme_manager.get_color("surface"))
        params_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 右側按鈕區域
        buttons_frame = tk.Frame(settings_content, bg=self.theme_manager.get_color("surface"))
        buttons_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(PADDING, 0))

        # 輸入參數提示
        tk.Label(params_frame, text="請輸入以下參數：", font=("Microsoft JhengHei UI", 11, "bold"), bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).pack(anchor="w", pady=(0, PADDING))

        # Account 數量
        account_frame = tk.Frame(params_frame, bg=self.theme_manager.get_color("surface"))
        account_frame.pack(fill=tk.X, pady=PADDING)
        tk.Label(account_frame, text="Account 數量", font=("Microsoft JhengHei UI", 11, "bold"), width=15, bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).pack(side=tk.LEFT)
        self.entry_account_count = ttk.Entry(account_frame, width=30, font=("Microsoft JhengHei UI", 10))
        self.entry_account_count.insert(0, "1")
        self.entry_account_count.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # AgentCode
        agent_frame = tk.Frame(params_frame, bg=self.theme_manager.get_color("surface"))
        agent_frame.pack(fill=tk.X, pady=PADDING)
        tk.Label(agent_frame, text="AgentCode", font=("Microsoft JhengHei UI", 11, "bold"), width=15, bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).pack(side=tk.LEFT)
        self.cb_agent_code = ttk.Combobox(agent_frame, width=30, font=("Microsoft JhengHei UI", 10), state="readonly")
        self.cb_agent_code.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # SubAgentCode
        sub_agent_frame = tk.Frame(params_frame, bg=self.theme_manager.get_color("surface"))
        sub_agent_frame.pack(fill=tk.X, pady=PADDING)
        tk.Label(sub_agent_frame, text="SubAgentCode", font=("Microsoft JhengHei UI", 11, "bold"), width=15, bg=self.theme_manager.get_color("surface"), fg=self.theme_manager.get_color("text_primary")).pack(side=tk.LEFT)
        self.cb_sub_agent_code = ttk.Combobox(sub_agent_frame, width=30, font=("Microsoft JhengHei UI", 10), state="readonly")
        self.cb_sub_agent_code.pack(side=tk.LEFT, fill=tk.X, expand=True)
        # 設定提示文字
        self.cb_sub_agent_code.set("請先選擇代理商")

        # 添加進度條
        progress_frame = tk.Frame(params_frame, bg=self.theme_manager.get_color("surface"))
        progress_frame.pack(fill=tk.X, pady=PADDING)

        # 創建進度條容器
        progress_container = tk.Frame(progress_frame, bg=self.theme_manager.get_color("surface"))
        progress_container.pack(fill=tk.X, expand=True)

        # 設定進度條樣式
        style = ttk.Style()
        style.configure("Accent.Horizontal.TProgressbar",
                        background="#4CAF50",  # 綠色進度條
                        troughcolor="#E0E0E0",  # 淺灰色背景
                        thickness=15)  # 增加厚度

        # 創建進度條
        self.progress_bar = ttk.Progressbar(
            progress_container,
            orient="horizontal",
            length=100,
            mode="determinate",
            style="Accent.Horizontal.TProgressbar"
        )
        self.progress_bar.pack(fill=tk.X, expand=True, side=tk.TOP)

        # 創建百分比標籤
        self.progress_percent = tk.Label(
            progress_container,
            text="0%",
            font=("Microsoft JhengHei UI", 9),
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        )
        self.progress_percent.pack(side=tk.TOP, pady=(2, 0))

        # 右側按鈕區域 - 由上到下排列
        # 重置預設值按鈕
        self.btn_reset = ModernButton(
            buttons_frame,
            text="重置預設值",
            icon=IconManager.get('refresh'),
            button_type="success",
            width=15
        )
        self.btn_reset.pack(side=tk.TOP, pady=(0, PADDING))

        # 清空代理商按鈕
        self.btn_clear = ModernButton(
            buttons_frame,
            text="清空代理商",
            icon=IconManager.get('delete'),
            button_type="danger",
            width=15
        )
        self.btn_clear.pack(side=tk.TOP, pady=PADDING)

        # 創建帳號按鈕
        self.btn_generate = ModernButton(
            buttons_frame,
            text="創建帳號",
            icon=IconManager.get('add'),
            button_type="primary",
            width=15
        )
        self.btn_generate.pack(side=tk.TOP, pady=PADDING)

        # 下載帳號列表按鈕
        self.btn_download = ModernButton(
            buttons_frame,
            text="下載帳號列表",
            icon=IconManager.get('download'),
            button_type="info",
            state="disabled",  # 初始設置為禁用狀態
            width=15
        )
        self.btn_download.pack(side=tk.TOP, pady=PADDING)

        # 操作日誌與說明區域 - 使用卡片式設計
        log_card = CardFrame(right_frame, title="操作日誌與說明", icon=IconManager.get('comment'))
        log_card.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)
        log_frame = log_card.get_content_frame()

        # 使用 ScrolledText 而非普通的 Text
        from tkinter.scrolledtext import ScrolledText
        self.debug_text = ScrolledText(log_frame, width=40, height=15, font=("Microsoft JhengHei UI", 10))
        self.debug_text.pack(fill=tk.BOTH, expand=True)

        # 設定各種文本標籤樣式
        self.debug_text.tag_configure("json", foreground="#0000FF", font=("Consolas", 10))
        self.debug_text.tag_configure("api_request", foreground="#008800", font=("Consolas", 10, "bold"))
        self.debug_text.tag_configure("api_response", foreground="#880088", font=("Consolas", 10, "bold"))
        self.debug_text.tag_configure("csv", foreground="#888800", font=("Consolas", 10))

        # 初始日誌內容
        self.debug_text.insert(tk.END, "👋 歡迎使用帳號產生器\n")
        self.debug_text.insert(tk.END, "⚠️ 請設定產生參數後點擊「創建帳號」按鈕\n")
        self.debug_text.insert(tk.END, "📝 操作流程：設定產生數量、選擇代理商和子代理商，然後點擊「創建帳號」\n")

        # 清除日誌按鈕
        self.btn_clear_log = ModernButton(
            log_frame,
            text="清除日誌",
            icon=IconManager.get('delete'),
            button_type="secondary",
            command=lambda: self.debug_text.delete(1.0, tk.END)
        )
        self.btn_clear_log.pack(side=tk.RIGHT, padx=PADDING, pady=PADDING)

    def _setup_button_styles(self):
        """設定按鈕樣式"""
        style = ttk.Style()

        # 主要按鈕樣式
        style.configure("Primary.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 成功按鈕樣式
        style.configure("Success.TButton",
                        background="#4CAF50",
                        foreground="white",
                        padding=5)

        # 信息按鈕樣式
        style.configure("Info.TButton",
                        background="#2196F3",
                        foreground="white",
                        padding=5)

        # 次要按鈕樣式
        style.configure("Secondary.TButton",
                        background="#9E9E9E",
                        foreground="white",
                        padding=5)

        # 危險按鈕樣式
        style.configure("Danger.TButton",
                        background="#F44336",
                        foreground="white",
                        padding=5)

    def start_generation(self):
        """開始產生帳號"""
        # 這個方法將由控制器實現
        pass

    def reset_defaults(self):
        """重置預設值"""
        # 這個方法將由控制器實現
        pass

    def clear_params(self):
        """清空參數"""
        # 這個方法將由控制器實現
        pass

    def download_accounts(self):
        """下載帳號列表"""
        # 這個方法將由控制器實現
        pass

    def open_output_folder(self):
        """開啟輸出資料夾"""
        # 這個方法將由控制器實現
        pass

    # 移除 next_step 和 prev_step 方法

    def log(self, message):
        """記錄日誌"""
        self.debug_text.insert(tk.END, f"{message}\n")
        self.debug_text.see(tk.END)
        self.update_idletasks()

    def log_message(self, message: str, level: str = "INFO"):
        """記錄訊息到日誌區域"""
        # 取得當前時間
        from datetime import datetime
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 根據日誌級別設定前綴
        if level.upper() == "ERROR":
            prefix = "❌ 錯誤"
        elif level.upper() == "WARNING":
            prefix = "⚠️ 警告"
        elif level.upper() == "SUCCESS":
            prefix = "✅ 成功"
        else:  # INFO
            prefix = "ℹ️ 資訊"

        # 格式化訊息並記錄
        log_entry = f"[{now}] {prefix}: {message}"
        self.log(log_entry)

        # 特殊格式的訊息仍然保留原來的樣式
        if level.lower() in ["json", "api_request", "api_response", "csv", "sql"]:
            self.debug_text.insert(tk.END, message + "\n", level.lower())
            self.debug_text.see(tk.END)  # 自動滾動到最新訊息

    def clear_log(self):
        """清除日誌"""
        self.debug_text.delete(1.0, tk.END)

    # 移除 _update_step_description 方法，因為已經移除了階段功能

    def update_progress(self, current, total):
        """更新進度顯示

        Args:
            current: 當前進度
            total: 總數量
        """
        percentage = int((current / total) * 100) if total > 0 else 0

        # 更新進度條
        self.progress_bar["value"] = percentage

        # 更新百分比標籤
        self.progress_percent.config(text=f"{percentage}%")

        # 更新日誌
        self.log_message(f"進度: {current}/{total} ({percentage}%)")

    def enable_download_button(self):
        """啟用下載按鈕"""
        self.btn_download.config(state="normal")

    def disable_download_button(self):
        """禁用下載按鈕"""
        self.btn_download.config(state="disabled")

    def start_flash(self):
        """啟動創建帳號按鈕的閃爍效果"""
        self.btn_generate.flash(times=3, interval=800)
