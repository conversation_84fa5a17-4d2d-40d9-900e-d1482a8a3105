"""環境配置管理界面"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import logging
from typing import Dict, Any, List
from utils.constants import PADDING, FONT_TITLE, FONT_LABEL, FONT_TEXT

logger = logging.getLogger(__name__)

class EnvironmentPanel(ttk.Frame):
    """環境配置管理界面"""
    
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.current_env_var = tk.StringVar()
        self.environments_data = []
        
        self.create_widgets()
        self.setup_layout()
    
    def create_widgets(self):
        """創建界面元件"""
        # 標題
        self.title_label = ttk.Label(
            self, 
            text="環境配置管理", 
            font=FONT_TITLE
        )
        
        # 當前環境區域
        self.current_env_frame = ttk.LabelFrame(self, text="當前環境", padding=PADDING)
        
        self.current_env_label = ttk.Label(
            self.current_env_frame,
            text="當前環境:",
            font=FONT_LABEL
        )
        
        self.current_env_display = ttk.Label(
            self.current_env_frame,
            textvariable=self.current_env_var,
            font=FONT_TEXT,
            foreground="blue"
        )
        
        self.refresh_btn = ttk.Button(
            self.current_env_frame,
            text="重新整理",
            command=self.refresh_environments
        )
        
        # 環境列表區域
        self.env_list_frame = ttk.LabelFrame(self, text="環境列表", padding=PADDING)
        
        # 環境列表
        columns = ("name", "display_name", "description", "status", "last_used")
        self.env_tree = ttk.Treeview(
            self.env_list_frame,
            columns=columns,
            show="headings",
            height=8
        )
        
        # 設定欄位標題
        self.env_tree.heading("name", text="環境名稱")
        self.env_tree.heading("display_name", text="顯示名稱")
        self.env_tree.heading("description", text="描述")
        self.env_tree.heading("status", text="狀態")
        self.env_tree.heading("last_used", text="最後使用")
        
        # 設定欄位寬度
        self.env_tree.column("name", width=100)
        self.env_tree.column("display_name", width=120)
        self.env_tree.column("description", width=200)
        self.env_tree.column("status", width=80)
        self.env_tree.column("last_used", width=150)
        
        # 滾動條
        self.env_scrollbar = ttk.Scrollbar(
            self.env_list_frame,
            orient="vertical",
            command=self.env_tree.yview
        )
        self.env_tree.configure(yscrollcommand=self.env_scrollbar.set)
        
        # 環境操作按鈕
        self.env_buttons_frame = ttk.Frame(self.env_list_frame)
        
        self.switch_btn = ttk.Button(
            self.env_buttons_frame,
            text="切換環境",
            command=self.switch_environment
        )
        
        self.add_btn = ttk.Button(
            self.env_buttons_frame,
            text="新增環境",
            command=self.add_environment
        )
        
        self.edit_btn = ttk.Button(
            self.env_buttons_frame,
            text="編輯環境",
            command=self.edit_environment
        )
        
        self.delete_btn = ttk.Button(
            self.env_buttons_frame,
            text="刪除環境",
            command=self.delete_environment
        )
        
        # 伺服器配置區域
        self.server_frame = ttk.LabelFrame(self, text="伺服器配置", padding=PADDING)
        
        # 伺服器配置表格
        server_columns = ("server_type", "url", "description")
        self.server_tree = ttk.Treeview(
            self.server_frame,
            columns=server_columns,
            show="headings",
            height=6
        )
        
        self.server_tree.heading("server_type", text="伺服器類型")
        self.server_tree.heading("url", text="URL")
        self.server_tree.heading("description", text="描述")
        
        self.server_tree.column("server_type", width=120)
        self.server_tree.column("url", width=300)
        self.server_tree.column("description", width=200)
        
        # 伺服器操作按鈕
        self.server_buttons_frame = ttk.Frame(self.server_frame)
        
        self.edit_server_btn = ttk.Button(
            self.server_buttons_frame,
            text="編輯伺服器",
            command=self.edit_server
        )
        
        self.test_server_btn = ttk.Button(
            self.server_buttons_frame,
            text="測試連線",
            command=self.test_server_connection
        )
        
        # 工具區域
        self.tools_frame = ttk.LabelFrame(self, text="工具", padding=PADDING)
        
        self.backup_btn = ttk.Button(
            self.tools_frame,
            text="備份配置",
            command=self.backup_config
        )
        
        self.restore_btn = ttk.Button(
            self.tools_frame,
            text="恢復配置",
            command=self.restore_config
        )
        
        self.validate_btn = ttk.Button(
            self.tools_frame,
            text="驗證配置",
            command=self.validate_config
        )
        
        # 日誌區域
        self.log_frame = ttk.LabelFrame(self, text="操作日誌", padding=PADDING)
        
        self.log_text = tk.Text(
            self.log_frame,
            height=8,
            width=60,
            font=FONT_TEXT,
            wrap=tk.WORD
        )
        
        self.log_scrollbar = ttk.Scrollbar(
            self.log_frame,
            orient="vertical",
            command=self.log_text.yview
        )
        self.log_text.configure(yscrollcommand=self.log_scrollbar.set)
        
        # 綁定事件
        self.env_tree.bind("<<TreeviewSelect>>", self.on_environment_select)
        self.server_tree.bind("<Double-1>", self.edit_server)
    
    def setup_layout(self):
        """設置界面佈局"""
        # 標題
        self.title_label.pack(pady=(0, PADDING))
        
        # 主要內容區域
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左側區域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING//2))
        
        # 當前環境
        self.current_env_frame.pack(fill=tk.X, pady=(0, PADDING))
        self.current_env_label.pack(side=tk.LEFT)
        self.current_env_display.pack(side=tk.LEFT, padx=(10, 0))
        self.refresh_btn.pack(side=tk.RIGHT)
        
        # 環境列表
        self.env_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))
        
        # 環境樹狀圖和滾動條
        tree_frame = ttk.Frame(self.env_list_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))
        
        self.env_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.env_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 環境操作按鈕
        self.env_buttons_frame.pack(fill=tk.X)
        self.switch_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.add_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.delete_btn.pack(side=tk.LEFT)
        
        # 工具區域
        self.tools_frame.pack(fill=tk.X)
        self.backup_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.restore_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.validate_btn.pack(side=tk.LEFT)
        
        # 右側區域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING//2, 0))
        
        # 伺服器配置
        self.server_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))
        self.server_tree.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))
        
        self.server_buttons_frame.pack(fill=tk.X)
        self.edit_server_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.test_server_btn.pack(side=tk.LEFT)
        
        # 日誌區域
        self.log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_content_frame = ttk.Frame(self.log_frame)
        log_content_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(self, message: str, level: str = "INFO"):
        """記錄日誌訊息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日誌長度
        lines = self.log_text.get("1.0", tk.END).split("\n")
        if len(lines) > 100:
            self.log_text.delete("1.0", f"{len(lines)-100}.0")
    
    def refresh_environments(self):
        """重新整理環境列表"""
        self.log_message("正在重新整理環境列表...")
        # 這裡會由控制器實現具體邏輯
        pass
    
    def switch_environment(self):
        """切換環境"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "請選擇要切換的環境")
            return
        
        # 這裡會由控制器實現具體邏輯
        pass
    
    def add_environment(self):
        """新增環境"""
        # 這裡會由控制器實現具體邏輯
        pass
    
    def edit_environment(self):
        """編輯環境"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "請選擇要編輯的環境")
            return
        
        # 這裡會由控制器實現具體邏輯
        pass
    
    def delete_environment(self):
        """刪除環境"""
        selection = self.env_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "請選擇要刪除的環境")
            return
        
        # 這裡會由控制器實現具體邏輯
        pass
    
    def edit_server(self, event=None):
        """編輯伺服器配置"""
        selection = self.server_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "請選擇要編輯的伺服器")
            return
        
        # 這裡會由控制器實現具體邏輯
        pass
    
    def test_server_connection(self):
        """測試伺服器連線"""
        selection = self.server_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "請選擇要測試的伺服器")
            return
        
        # 這裡會由控制器實現具體邏輯
        pass
    
    def backup_config(self):
        """備份配置"""
        # 這裡會由控制器實現具體邏輯
        pass
    
    def restore_config(self):
        """恢復配置"""
        # 這裡會由控制器實現具體邏輯
        pass
    
    def validate_config(self):
        """驗證配置"""
        # 這裡會由控制器實現具體邏輯
        pass
    
    def on_environment_select(self, event):
        """環境選擇事件"""
        selection = self.env_tree.selection()
        if selection:
            # 更新伺服器配置顯示
            # 這裡會由控制器實現具體邏輯
            pass
    
    def update_environment_list(self, environments: List[Dict[str, Any]]):
        """更新環境列表"""
        # 清空現有項目
        for item in self.env_tree.get_children():
            self.env_tree.delete(item)
        
        # 添加新項目
        for env in environments:
            status = "當前" if env.get("is_current", False) else "可用"
            last_used = env.get("last_used", "從未使用")
            if last_used and last_used != "從未使用":
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_used.replace('Z', '+00:00'))
                    last_used = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    pass
            
            self.env_tree.insert("", tk.END, values=(
                env.get("name", ""),
                env.get("display_name", ""),
                env.get("description", ""),
                status,
                last_used
            ))
    
    def update_server_list(self, servers: Dict[str, str]):
        """更新伺服器列表"""
        # 清空現有項目
        for item in self.server_tree.get_children():
            self.server_tree.delete(item)
        
        # 伺服器類型描述
        server_descriptions = {
            "mysql_operator": "MySQL 資料庫操作服務",
            "gamebridge": "遊戲橋接服務",
            "tokenguard": "令牌守護服務",
            "lottery": "樂透服務",
            "simulation": "模擬服務"
        }
        
        # 添加新項目
        for server_type, url in servers.items():
            description = server_descriptions.get(server_type, "")
            self.server_tree.insert("", tk.END, values=(
                server_type,
                url,
                description
            ))
    
    def set_current_environment(self, env_name: str):
        """設置當前環境顯示"""
        self.current_env_var.set(env_name)
