#!/usr/bin/env python3
"""
IP 管理系統測試腳本

此腳本用於測試新的 IP 管理功能，包括：
1. 環境配置管理
2. 快速 IP 切換
3. 配置模板管理
4. 歷史記錄功能
"""

import sys
import os

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_environment_config():
    """測試環境配置管理"""
    print("=== 測試環境配置管理 ===")
    
    try:
        from utils.environment_config import env_config
        
        # 測試取得當前環境
        current_env = env_config.get_current_environment()
        print(f"當前環境: {current_env}")
        
        # 測試列出所有環境
        environments = env_config.list_environments()
        print(f"可用環境數量: {len(environments)}")
        for env in environments:
            print(f"  - {env['name']}: {env['display_name']}")
        
        # 測試取得環境資訊
        env_info = env_config.get_environment_info()
        print(f"當前環境伺服器配置:")
        for server_type, url in env_info.get("api_servers", {}).items():
            print(f"  {server_type}: {url}")
        
        # 測試配置驗證
        validation_result = env_config.validate_environment()
        print(f"配置驗證結果: {'通過' if validation_result['valid'] else '失敗'}")
        if validation_result['errors']:
            print(f"錯誤: {validation_result['errors']}")
        if validation_result['warnings']:
            print(f"警告: {validation_result['warnings']}")
        
        print("✅ 環境配置管理測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 環境配置管理測試失敗: {e}")
        return False

def test_ip_switcher():
    """測試 IP 切換工具"""
    print("\n=== 測試 IP 切換工具 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        
        # 測試取得模板
        templates = ip_switcher.get_templates()
        print(f"可用模板數量: {len(templates)}")
        for template_name, template_data in templates.items():
            print(f"  - {template_name}: {template_data.get('description', '無描述')}")
        
        # 測試取得當前 IP 配置
        current_ips = ip_switcher.get_current_ips()
        print(f"當前 IP 配置:")
        for server_type, ip in current_ips.items():
            print(f"  {server_type}: {ip}")
        
        # 測試找出常見 IP
        common_ips = ip_switcher.find_common_ips()
        print(f"常見 IP: {common_ips}")
        
        # 測試歷史記錄
        history = ip_switcher.get_history(5)
        print(f"歷史記錄數量: {len(history)}")
        for i, entry in enumerate(history):
            print(f"  {i+1}. {entry.get('operation', '未知操作')} - {entry.get('timestamp', '未知時間')[:19]}")
        
        print("✅ IP 切換工具測試通過")
        return True
        
    except Exception as e:
        print(f"❌ IP 切換工具測試失敗: {e}")
        return False

def test_dynamic_api_urls():
    """測試動態 API URLs"""
    print("\n=== 測試動態 API URLs ===")
    
    try:
        from utils.constants import get_api_urls, API_URLS
        
        # 測試動態取得 API URLs
        dynamic_urls = get_api_urls()
        print(f"動態 API URLs 數量: {len(dynamic_urls)}")
        
        # 測試全域 API_URLS
        print(f"全域 API_URLS 數量: {len(API_URLS)}")
        
        # 比較兩者是否一致
        if dynamic_urls == API_URLS:
            print("✅ 動態 API URLs 與全域 API_URLS 一致")
        else:
            print("⚠️ 動態 API URLs 與全域 API_URLS 不一致")
        
        # 顯示部分 URL
        print("部分 API URLs:")
        for key in ["MYSQL_OPERATOR", "UPDATE_COIN", "CREATE_MEMBER"]:
            if key in API_URLS:
                print(f"  {key}: {API_URLS[key]}")
        
        print("✅ 動態 API URLs 測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 動態 API URLs 測試失敗: {e}")
        return False

def test_backup_restore():
    """測試備份和恢復功能"""
    print("\n=== 測試備份和恢復功能 ===")
    
    try:
        from utils.environment_config import env_config
        import tempfile
        import os
        
        # 創建臨時備份檔案
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            backup_path = f.name
        
        try:
            # 測試備份
            result_path = env_config.backup_environments(backup_path)
            if result_path and os.path.exists(result_path):
                print(f"✅ 備份成功: {result_path}")
                
                # 測試恢復
                success = env_config.restore_environments(result_path)
                if success:
                    print("✅ 恢復成功")
                else:
                    print("❌ 恢復失敗")
                    return False
            else:
                print("❌ 備份失敗")
                return False
        finally:
            # 清理臨時檔案
            if os.path.exists(backup_path):
                os.unlink(backup_path)
        
        print("✅ 備份和恢復功能測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 備份和恢復功能測試失敗: {e}")
        return False

def test_template_management():
    """測試模板管理功能"""
    print("\n=== 測試模板管理功能 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        
        # 測試新增模板
        test_template_name = "測試模板"
        test_template_data = {
            "mysql_operator": "http://test.example.com:5000",
            "gamebridge": "http://test.example.com:8080"
        }
        
        success = ip_switcher.add_template(
            test_template_name, 
            test_template_data, 
            "這是一個測試模板"
        )
        
        if success:
            print(f"✅ 新增模板成功: {test_template_name}")
            
            # 測試取得模板
            template = ip_switcher.get_template(test_template_name)
            if template:
                print(f"✅ 取得模板成功: {template.get('description', '無描述')}")
                
                # 測試刪除模板
                success = ip_switcher.remove_template(test_template_name)
                if success:
                    print(f"✅ 刪除模板成功: {test_template_name}")
                else:
                    print(f"❌ 刪除模板失敗: {test_template_name}")
                    return False
            else:
                print(f"❌ 取得模板失敗: {test_template_name}")
                return False
        else:
            print(f"❌ 新增模板失敗: {test_template_name}")
            return False
        
        print("✅ 模板管理功能測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 模板管理功能測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("開始測試 IP 管理系統...")
    print("=" * 50)
    
    test_results = []
    
    # 執行各項測試
    test_results.append(test_environment_config())
    test_results.append(test_ip_switcher())
    test_results.append(test_dynamic_api_urls())
    test_results.append(test_backup_restore())
    test_results.append(test_template_management())
    
    # 統計測試結果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"測試完成: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試都通過了！IP 管理系統運作正常。")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
