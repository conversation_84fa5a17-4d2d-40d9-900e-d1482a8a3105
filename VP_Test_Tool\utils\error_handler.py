"""增強錯誤處理模塊

此模塊提供增強的錯誤處理功能，包括詳細的錯誤信息、解決方案建議和錯誤報告生成。
"""
import os
import sys
import time
import json
import logging
import traceback
import platform
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
from .version import VERSION, APP_TITLE

logger = logging.getLogger(__name__)

# 錯誤類型定義
ERROR_TYPES = {
    "NetworkError": {
        "title": "網絡錯誤",
        "description": "連接到服務器時發生錯誤",
        "solutions": [
            "檢查您的網絡連接是否正常",
            "確認服務器地址是否正確",
            "檢查防火牆設置是否阻止了連接",
            "嘗試重新連接或稍後再試"
        ]
    },
    "DatabaseError": {
        "title": "數據庫錯誤",
        "description": "訪問數據庫時發生錯誤",
        "solutions": [
            "確認數據庫連接設置是否正確",
            "檢查數據庫服務是否正常運行",
            "確認您有足夠的權限訪問數據庫",
            "嘗試重新連接或聯繫管理員"
        ]
    },
    "AuthenticationError": {
        "title": "認證錯誤",
        "description": "用戶認證失敗",
        "solutions": [
            "確認您的用戶名和密碼是否正確",
            "檢查您的帳戶是否已被鎖定",
            "確認您有權限訪問該資源",
            "嘗試重新登錄或聯繫管理員"
        ]
    },
    "FileError": {
        "title": "文件錯誤",
        "description": "讀取或寫入文件時發生錯誤",
        "solutions": [
            "確認文件路徑是否正確",
            "檢查文件是否存在且未被鎖定",
            "確認您有足夠的權限訪問該文件",
            "檢查磁盤空間是否足夠"
        ]
    },
    "ConfigurationError": {
        "title": "配置錯誤",
        "description": "程序配置有誤",
        "solutions": [
            "檢查配置文件是否存在且格式正確",
            "確認配置參數是否有效",
            "嘗試重置配置或使用默認配置",
            "聯繫管理員獲取正確的配置"
        ]
    },
    "ResourceError": {
        "title": "資源錯誤",
        "description": "訪問或操作資源時發生錯誤",
        "solutions": [
            "確認資源是否存在且可用",
            "檢查資源格式是否正確",
            "確認您有足夠的權限訪問該資源",
            "嘗試重新加載資源或聯繫管理員"
        ]
    },
    "ValidationError": {
        "title": "驗證錯誤",
        "description": "輸入數據驗證失敗",
        "solutions": [
            "檢查輸入數據是否符合要求",
            "確認數據格式是否正確",
            "參考輸入要求修改數據",
            "查看錯誤詳情了解具體問題"
        ]
    },
    "PermissionError": {
        "title": "權限錯誤",
        "description": "沒有足夠的權限執行操作",
        "solutions": [
            "確認您有足夠的權限執行該操作",
            "嘗試以管理員身份運行程序",
            "聯繫管理員獲取必要的權限",
            "檢查文件或資源的權限設置"
        ]
    },
    "TimeoutError": {
        "title": "超時錯誤",
        "description": "操作超時",
        "solutions": [
            "檢查網絡連接是否穩定",
            "確認服務器是否正常運行",
            "嘗試增加超時時間",
            "稍後再試或聯繫管理員"
        ]
    },
    "MemoryError": {
        "title": "內存錯誤",
        "description": "程序內存不足",
        "solutions": [
            "關閉其他不必要的程序釋放內存",
            "減少處理的數據量",
            "重新啟動程序",
            "升級您的計算機內存"
        ]
    },
    "UnknownError": {
        "title": "未知錯誤",
        "description": "發生未知錯誤",
        "solutions": [
            "嘗試重新啟動程序",
            "檢查日誌文件獲取更多信息",
            "聯繫技術支持獲取幫助",
            "報告錯誤以幫助改進程序"
        ]
    }
}

class ErrorHandler:
    """錯誤處理器

    提供增強的錯誤處理功能，包括詳細的錯誤信息、解決方案建議和錯誤報告生成。

    Args:
        parent: 父窗口
        app_name: 應用程序名稱
        app_version: 應用程序版本
        log_dir: 日誌目錄
        report_url: 錯誤報告 URL
        on_error: 錯誤回調函數
        show_dialog: 是否顯示錯誤對話框
        exit_on_error: 是否在發生錯誤時退出程序
    """

    def __init__(
        self,
        parent: Optional[tk.Tk] = None,
        app_name: str = "VP Test Tool",
        app_version: str = None,
        log_dir: str = "logs",
        report_url: Optional[str] = None,
        on_error: Optional[Callable[[Exception, Dict[str, Any]], None]] = None,
        show_dialog: bool = True,
        exit_on_error: bool = False
    ):
        self.parent = parent
        self.app_name = app_name
        # 如果未提供版本號，使用 version.py 中定義的版本號
        self.app_version = app_version if app_version else VERSION
        self.log_dir = log_dir
        self.report_url = report_url
        self.on_error = on_error
        self.show_dialog = show_dialog
        self.exit_on_error = exit_on_error

        # 創建日誌目錄
        self._create_log_dir()

    def _create_log_dir(self):
        """創建日誌目錄"""
        if not os.path.exists(self.log_dir):
            try:
                os.makedirs(self.log_dir)
            except Exception as e:
                logger.warning(f"創建日誌目錄失敗: {e}")

    def handle_exception(
        self,
        exception: Exception,
        error_type: str = "UnknownError",
        context: Optional[Dict[str, Any]] = None,
        show_dialog: Optional[bool] = None,
        exit_on_error: Optional[bool] = None
    ) -> Dict[str, Any]:
        """處理異常

        Args:
            exception: 異常對象
            error_type: 錯誤類型
            context: 錯誤上下文
            show_dialog: 是否顯示錯誤對話框
            exit_on_error: 是否在發生錯誤時退出程序

        Returns:
            Dict[str, Any]: 錯誤信息
        """
        # 獲取錯誤信息
        error_info = self._get_error_info(exception, error_type, context)

        # 記錄錯誤
        self._log_error(error_info)

        # 保存錯誤報告
        report_path = self._save_error_report(error_info)
        if report_path:
            error_info["report_path"] = report_path

        # 調用錯誤回調函數
        if self.on_error:
            try:
                self.on_error(exception, error_info)
            except Exception as e:
                logger.error(f"調用錯誤回調函數失敗: {e}")

        # 顯示錯誤對話框
        if show_dialog is None:
            show_dialog = self.show_dialog

        if show_dialog:
            self.show_error_dialog(error_info)

        # 退出程序
        if exit_on_error is None:
            exit_on_error = self.exit_on_error

        if exit_on_error:
            logger.critical(f"程序因錯誤退出: {error_info['message']}")
            sys.exit(1)

        return error_info

    def _get_error_info(
        self,
        exception: Exception,
        error_type: str,
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """獲取錯誤信息

        Args:
            exception: 異常對象
            error_type: 錯誤類型
            context: 錯誤上下文

        Returns:
            Dict[str, Any]: 錯誤信息
        """
        # 獲取錯誤類型信息
        error_type_info = ERROR_TYPES.get(error_type, ERROR_TYPES["UnknownError"])

        # 獲取錯誤堆棧
        exc_type, exc_value, exc_traceback = sys.exc_info()
        stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)

        # 獲取系統信息
        system_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "os_release": platform.release(),
            "python_version": platform.python_version(),
            "python_implementation": platform.python_implementation(),
            "processor": platform.processor(),
            "machine": platform.machine(),
            "node": platform.node()
        }

        # 構建錯誤信息
        error_info = {
            "timestamp": time.time(),
            "error_type": error_type,
            "title": error_type_info["title"],
            "description": error_type_info["description"],
            "message": str(exception),
            "solutions": error_type_info["solutions"],
            "stack_trace": stack_trace,
            "system_info": system_info,
            "app_info": {
                "name": self.app_name,
                "version": self.app_version
            }
        }

        # 添加上下文信息
        if context:
            error_info["context"] = context

        return error_info

    def _log_error(self, error_info: Dict[str, Any]):
        """記錄錯誤

        Args:
            error_info: 錯誤信息
        """
        # 構建錯誤消息
        error_message = (
            f"錯誤: {error_info['title']} - {error_info['message']}\n"
            f"類型: {error_info['error_type']}\n"
            f"描述: {error_info['description']}\n"
            f"堆棧跟蹤:\n{''.join(error_info['stack_trace'])}"
        )

        # 記錄錯誤
        logger.error(error_message)

    def _save_error_report(self, error_info: Dict[str, Any]) -> Optional[str]:
        """保存錯誤報告

        Args:
            error_info: 錯誤信息

        Returns:
            Optional[str]: 錯誤報告路徑
        """
        try:
            # 構建報告路徑
            timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime(error_info["timestamp"]))
            report_name = f"error_report_{timestamp}.json"
            report_path = os.path.join(self.log_dir, report_name)

            # 保存報告
            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(error_info, f, ensure_ascii=False, indent=2)

            logger.info(f"錯誤報告已保存: {report_path}")
            return report_path

        except Exception as e:
            logger.warning(f"保存錯誤報告失敗: {e}")
            return None

    def show_error_dialog(self, error_info: Dict[str, Any]):
        """顯示錯誤對話框

        Args:
            error_info: 錯誤信息
        """
        try:
            # 檢查父窗口
            if self.parent is None:
                root = tk.Tk()
                root.withdraw()
                parent = root
            else:
                parent = self.parent

            # 創建對話框
            dialog = tk.Toplevel(parent)
            dialog.title(f"{error_info['title']}")
            dialog.geometry("600x500")
            dialog.minsize(500, 400)

            # 設置模態
            dialog.transient(parent)
            dialog.grab_set()

            # 創建內容框架
            content_frame = ttk.Frame(dialog, padding=10)
            content_frame.pack(fill=tk.BOTH, expand=True)

            # 創建標題標籤
            title_label = ttk.Label(
                content_frame,
                text=f"{error_info['title']}",
                font=("Microsoft JhengHei UI", 12, "bold")
            )
            title_label.pack(pady=(0, 5))

            # 創建描述標籤
            desc_label = ttk.Label(
                content_frame,
                text=f"{error_info['description']}",
                font=("Microsoft JhengHei UI", 10)
            )
            desc_label.pack(pady=(0, 5))

            # 創建錯誤消息標籤
            message_label = ttk.Label(
                content_frame,
                text=f"錯誤信息: {error_info['message']}",
                font=("Microsoft JhengHei UI", 10),
                foreground="red"
            )
            message_label.pack(pady=(0, 10))

            # 創建解決方案框架
            solutions_frame = ttk.LabelFrame(content_frame, text="可能的解決方案")
            solutions_frame.pack(fill=tk.X, pady=(0, 10))

            # 創建解決方案列表
            for i, solution in enumerate(error_info["solutions"]):
                solution_label = ttk.Label(
                    solutions_frame,
                    text=f"{i+1}. {solution}",
                    font=("Microsoft JhengHei UI", 10),
                    wraplength=550
                )
                solution_label.pack(anchor=tk.W, padx=10, pady=2)

            # 創建詳細信息框架
            details_frame = ttk.LabelFrame(content_frame, text="詳細信息")
            details_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 創建詳細信息文本框
            details_text = tk.Text(details_frame, wrap=tk.WORD, font=("Consolas", 9))
            details_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 添加滾動條
            details_scrollbar = ttk.Scrollbar(details_text)
            details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            details_text.config(yscrollcommand=details_scrollbar.set)
            details_scrollbar.config(command=details_text.yview)

            # 構建詳細信息
            details = [
                f"錯誤類型: {error_info['error_type']}",
                f"時間: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(error_info['timestamp']))}",
                f"應用程序: {error_info['app_info']['name']} {error_info['app_info']['version']}",
                f"操作系統: {error_info['system_info']['os']} {error_info['system_info']['os_release']} {error_info['system_info']['os_version']}",
                f"Python 版本: {error_info['system_info']['python_version']} ({error_info['system_info']['python_implementation']})",
                f"處理器: {error_info['system_info']['processor']}",
                "",
                "堆棧跟蹤:",
                "".join(error_info["stack_trace"])
            ]

            # 添加上下文信息
            if "context" in error_info:
                details.append("")
                details.append("上下文信息:")
                for key, value in error_info["context"].items():
                    details.append(f"{key}: {value}")

            # 插入詳細信息
            details_text.insert(tk.END, "\n".join(details))
            details_text.config(state=tk.DISABLED)

            # 創建按鈕框架
            button_frame = ttk.Frame(content_frame)
            button_frame.pack(fill=tk.X, pady=(0, 5))

            # 創建關閉按鈕
            close_button = ttk.Button(
                button_frame,
                text="關閉",
                command=dialog.destroy
            )
            close_button.pack(side=tk.RIGHT, padx=5)

            # 創建複製按鈕
            def copy_to_clipboard():
                dialog.clipboard_clear()
                dialog.clipboard_append("\n".join(details))
                messagebox.showinfo("複製成功", "錯誤詳情已複製到剪貼板")

            copy_button = ttk.Button(
                button_frame,
                text="複製詳情",
                command=copy_to_clipboard
            )
            copy_button.pack(side=tk.RIGHT, padx=5)

            # 創建保存按鈕
            def save_report():
                # 如果已有報告路徑，直接打開
                if "report_path" in error_info:
                    report_path = error_info["report_path"]
                    messagebox.showinfo("錯誤報告", f"錯誤報告已保存到: {report_path}")
                    return

                # 選擇保存路徑
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".json",
                    filetypes=[("JSON 文件", "*.json"), ("所有文件", "*.*")],
                    title="保存錯誤報告"
                )

                if not file_path:
                    return

                try:
                    # 保存報告
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(error_info, f, ensure_ascii=False, indent=2)

                    messagebox.showinfo("保存成功", f"錯誤報告已保存到: {file_path}")
                except Exception as e:
                    messagebox.showerror("保存失敗", f"保存錯誤報告失敗: {e}")

            save_button = ttk.Button(
                button_frame,
                text="保存報告",
                command=save_report
            )
            save_button.pack(side=tk.RIGHT, padx=5)

            # 創建報告按鈕
            if self.report_url:
                def report_error():
                    try:
                        import webbrowser
                        webbrowser.open(self.report_url)
                    except Exception as e:
                        messagebox.showerror("報告錯誤", f"打開報告頁面失敗: {e}")

                report_button = ttk.Button(
                    button_frame,
                    text="報告錯誤",
                    command=report_error
                )
                report_button.pack(side=tk.LEFT, padx=5)

            # 居中顯示
            dialog.update_idletasks()
            width = dialog.winfo_width()
            height = dialog.winfo_height()
            x = (dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (dialog.winfo_screenheight() // 2) - (height // 2)
            dialog.geometry(f"{width}x{height}+{x}+{y}")

            # 等待對話框關閉
            parent.wait_window(dialog)

        except Exception as e:
            logger.error(f"顯示錯誤對話框失敗: {e}")
            messagebox.showerror("錯誤", f"顯示錯誤對話框失敗: {e}\n\n原始錯誤: {error_info['message']}")

    def install_global_handler(self):
        """安裝全局異常處理器"""
        def global_exception_handler(exc_type, exc_value, exc_traceback):
            # 忽略鍵盤中斷
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            # 處理異常
            self.handle_exception(exc_value)

        # 設置全局異常處理器
        sys.excepthook = global_exception_handler
        logger.info("已安裝全局異常處理器")

    def wrap_function(self, func: Callable, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
        """包裝函數，添加錯誤處理

        Args:
            func: 要包裝的函數
            error_type: 錯誤類型
            context: 錯誤上下文

        Returns:
            Callable: 包裝後的函數
        """
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 更新上下文
                ctx = context.copy() if context else {}
                ctx.update({
                    "function": func.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                })

                # 處理異常
                self.handle_exception(e, error_type, ctx)
                return None

        return wrapper

    def wrap_method(self, method: Callable, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
        """包裝方法，添加錯誤處理

        Args:
            method: 要包裝的方法
            error_type: 錯誤類型
            context: 錯誤上下文

        Returns:
            Callable: 包裝後的方法
        """
        def wrapper(self_arg, *args, **kwargs):
            try:
                return method(self_arg, *args, **kwargs)
            except Exception as e:
                # 更新上下文
                ctx = context.copy() if context else {}
                ctx.update({
                    "class": self_arg.__class__.__name__,
                    "method": method.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                })

                # 處理異常
                self.handle_exception(e, error_type, ctx)
                return None

        return wrapper

    def wrap_class(self, cls: type, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
        """包裝類，為所有方法添加錯誤處理

        Args:
            cls: 要包裝的類
            error_type: 錯誤類型
            context: 錯誤上下文

        Returns:
            type: 包裝後的類
        """
        # 獲取所有方法
        for name, method in cls.__dict__.items():
            # 跳過特殊方法和非可調用對象
            if name.startswith("__") or not callable(method):
                continue

            # 更新上下文
            ctx = context.copy() if context else {}
            ctx.update({
                "class": cls.__name__,
                "method": name
            })

            # 包裝方法
            setattr(cls, name, self.wrap_method(method, error_type, ctx))

        return cls

# 創建全局錯誤處理器實例
error_handler = ErrorHandler()

def handle_exception(
    exception: Exception,
    error_type: str = "UnknownError",
    context: Optional[Dict[str, Any]] = None,
    show_dialog: bool = True,
    exit_on_error: bool = False
) -> Dict[str, Any]:
    """處理異常

    Args:
        exception: 異常對象
        error_type: 錯誤類型
        context: 錯誤上下文
        show_dialog: 是否顯示錯誤對話框
        exit_on_error: 是否在發生錯誤時退出程序

    Returns:
        Dict[str, Any]: 錯誤信息
    """
    return error_handler.handle_exception(exception, error_type, context, show_dialog, exit_on_error)

def install_global_handler():
    """安裝全局異常處理器"""
    error_handler.install_global_handler()

def wrap_function(func: Callable, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
    """包裝函數，添加錯誤處理

    Args:
        func: 要包裝的函數
        error_type: 錯誤類型
        context: 錯誤上下文

    Returns:
        Callable: 包裝後的函數
    """
    return error_handler.wrap_function(func, error_type, context)

def wrap_method(method: Callable, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
    """包裝方法，添加錯誤處理

    Args:
        method: 要包裝的方法
        error_type: 錯誤類型
        context: 錯誤上下文

    Returns:
        Callable: 包裝後的方法
    """
    return error_handler.wrap_method(method, error_type, context)

def wrap_class(cls: type, error_type: str = "UnknownError", context: Optional[Dict[str, Any]] = None):
    """包裝類，為所有方法添加錯誤處理

    Args:
        cls: 要包裝的類
        error_type: 錯誤類型
        context: 錯誤上下文

    Returns:
        type: 包裝後的類
    """
    return error_handler.wrap_class(cls, error_type, context)

if __name__ == "__main__":
    # 測試代碼
    logging.basicConfig(level=logging.INFO)

    # 創建根窗口
    root = tk.Tk()
    root.title("錯誤處理測試")
    root.geometry("300x200")

    # 創建錯誤處理器
    handler = ErrorHandler(root)

    # 創建測試函數
    def test_network_error():
        try:
            raise ConnectionError("無法連接到服務器")
        except Exception as e:
            handler.handle_exception(e, "NetworkError", {"url": "https://example.com", "timeout": 30})

    def test_file_error():
        try:
            with open("non_existent_file.txt", "r") as f:
                content = f.read()
        except Exception as e:
            handler.handle_exception(e, "FileError", {"file_path": "non_existent_file.txt", "mode": "r"})

    def test_unknown_error():
        try:
            result = 1 / 0
        except Exception as e:
            handler.handle_exception(e)

    # 創建按鈕
    network_button = ttk.Button(
        root,
        text="測試網絡錯誤",
        command=test_network_error
    )
    network_button.pack(pady=10)

    file_button = ttk.Button(
        root,
        text="測試文件錯誤",
        command=test_file_error
    )
    file_button.pack(pady=10)

    unknown_button = ttk.Button(
        root,
        text="測試未知錯誤",
        command=test_unknown_error
    )
    unknown_button.pack(pady=10)

    # 啟動主循環
    root.mainloop()
