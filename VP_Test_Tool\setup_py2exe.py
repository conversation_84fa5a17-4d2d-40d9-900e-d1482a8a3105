"""
使用 py2exe 打包 VP Test Tool 為 exe 文件
py2exe 是一個專為 Windows 設計的打包工具
這可能會減少被防毒軟體誤判為病毒的機率
"""
import sys
import os
from setuptools import setup
import py2exe

# 應用程式信息
APP_NAME = "VP_Test_Tool"
APP_VERSION = "2.5.2"
APP_DESCRIPTION = "VP Test Tool"
APP_AUTHOR = "VP Test Tool Team"

# 圖示路徑
icon_path = os.path.abspath(os.path.join("assets", "icons", "vp_test_tool.ico"))
if not os.path.exists(icon_path):
    print(f"Warning: Icon file not found at {icon_path}")
    icon_path = None

# 包含的文件和目錄
data_files = [
    ("assets", [os.path.join("assets", f) for f in os.listdir("assets") if os.path.isfile(os.path.join("assets", f))]),
    ("assets/icons", [os.path.join("assets", "icons", f) for f in os.listdir(os.path.join("assets", "icons")) if os.path.isfile(os.path.join("assets", "icons", f))]),
]

# 明確指定包
packages = [
    "utils",
    "views",
    "models",
    "controllers",
    "widgets",
    "services",
]

# 設置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    packages=packages,  # 明確指定包
    windows=[{
        "script": "main.py",
        "icon_resources": [(1, icon_path)] if icon_path else [],
        "dest_base": APP_NAME,
        "copyright": f"Copyright (c) {APP_AUTHOR}",
    }],
    options={
        "py2exe": {
            "bundle_files": 1,  # 將所有文件打包成一個 exe
            "compressed": True,  # 壓縮文件
            "optimize": 2,  # 優化級別
            "dist_dir": "dist/py2exe",  # 輸出目錄
            "includes": [
                "tkinter",
                "PIL",
            ],
            "excludes": [
                "unittest",
                "email",
                "html",
                "http",
                "xml",
                "pydoc",
            ],
        }
    },
    data_files=data_files,
    zipfile=None,  # 不創建 library.zip
)

print(f"打包完成！exe 文件位於: {os.path.abspath(os.path.join('dist', 'py2exe', APP_NAME + '.exe'))}")
