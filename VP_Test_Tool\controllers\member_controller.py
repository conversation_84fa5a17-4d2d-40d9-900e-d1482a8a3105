"""遊戲卡片工具控制器"""
from typing import Optional, Dict, Any, List, Tuple
import logging
import tkinter as tk
from tkinter import messagebox, filedialog
import csv
import os
import json
import threading
import queue
import time
import concurrent.futures
from functools import partial
from models.member import MemberService, Member
from models.agent import AgentService
from views.member_panel import MemberPanel

logger = logging.getLogger(__name__)

class MemberController:
    """遊戲卡片工具控制器"""
    def __init__(self,
                 member_panel: MemberPanel,
                 member_service: MemberService,
                 agent_service: AgentService):
        self.view = member_panel
        self.member_service = member_service
        self.agent_service = agent_service
        self.current_member = None
        self.games = []
        self.bet_lines = []
        self.batch_accounts = []  # (account, member_id)

        # 用於多線程處理
        self.log_queue = queue.Queue()  # 用於存放日誌訊息
        self.is_processing = False  # 批次處理狀態標記

        self._init_bindings()
        self._init_data()

        # 啟動日誌更新定時器
        self._start_log_updater()

    def _init_bindings(self):
        """初始化事件綁定"""
        # 會員資訊區域
        self.view.btn_query.config(command=self._handle_query)
        self.view.btn_clear_member.config(command=self.view.clear_member_info)

        # 批次處理區域
        self.view.btn_import.config(command=self._handle_import_accounts)
        self.view.btn_batch_update.config(command=self._handle_batch_update)
        self.view.btn_batch_delete.config(command=self._handle_batch_delete)

        # 遊戲與押注設定區域
        # 移除載入遊戲清單與載入投注額按鈕的事件綁定
        self.view.cb_game.bind('<<ComboboxSelected>>', lambda e: self._handle_game_selected())
        self.view.cb_bet_mode.bind('<<ComboboxSelected>>', lambda e: self._handle_bet_mode_selected())

        # 卡片設定與操作區域
        self.view.btn_add_card.config(command=self._handle_add_card)
        self.view.btn_delete_all_cards.config(command=self._handle_delete_all_cards)

    def _start_log_updater(self):
        """啟動日誌更新定時器 - 優化版本"""
        def update_logs():
            # 從佇列中取出日誌訊息並顯示
            # 每次最多取出20條訊息，避免一次處理太多導致UI凍結
            messages = []
            for _ in range(min(20, self.log_queue.qsize())):
                if not self.log_queue.empty():
                    messages.append(self.log_queue.get())
                    self.log_queue.task_done()

            # 如果有訊息要顯示
            if messages:
                # 使用一次性更新方式，減少UI重繪次數
                self.view.log_batch(messages)

            # 更新批次處理按鈕狀態
            if self.is_processing:
                if self.view.btn_batch_update['state'] != tk.DISABLED:
                    self.view.btn_batch_update.config(state=tk.DISABLED)
                    self.view.btn_batch_delete.config(state=tk.DISABLED)
                    self.view.btn_import.config(state=tk.DISABLED)
            else:
                if self.view.btn_batch_update['state'] != tk.NORMAL:
                    self.view.btn_batch_update.config(state=tk.NORMAL)
                    self.view.btn_batch_delete.config(state=tk.NORMAL)
                    self.view.btn_import.config(state=tk.NORMAL)

            # 根據佇列大小動態調整更新間隔
            queue_size = self.log_queue.qsize()
            if queue_size > 100:
                # 如果佇列很大，加快更新频率
                update_interval = 50  # 50毫秒
            elif queue_size > 50:
                update_interval = 80  # 80毫秒
            else:
                update_interval = 100  # 100毫秒

            # 設定下一次更新
            self.view.after(update_interval, update_logs)

        # 啟動定時器
        update_logs()

    def _log_to_queue(self, message: str):
        """將日誌訊息加入佇列"""
        self.log_queue.put(message)

    def _init_data(self):
        """初始化下拉選單資料"""
        try:
            # 初始化遊戲和投注額列表
            self.games = []
            self.bet_modes = []
            self.bet_lines = []

            # 清空下拉選單
            self.view.cb_game['values'] = []
            self.view.cb_bet_mode['values'] = []
            self.view.cb_bet_line['values'] = []

            # 記錄日誌
            self.view.log("✅ 初始化完成，正在載入遊戲列表與投注額...")

            # 自動載入遊戲列表
            self._load_game_list()

            # 自動載入投注模式列表
            self._load_bet_modes()

        except Exception as e:
            logger.error(f"初始化資料失敗: {e}")
            self.view.show_error("初始化失敗", str(e))



    def _load_bet_modes(self):
        """載入投注模式列表"""
        try:
            # 顯示載入中訊息
            self.view.log(f"🔍 正在載入投注模式列表...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT DISTINCT BetModeId FROM Platform.BetModeDetail ORDER BY BetModeId;\"")
            self.view.log("}")

            # 載入投注模式列表
            self.bet_modes, bet_modes_details = self.member_service.get_bet_modes()

            # 顯示回應詳情
            if bet_modes_details['status_code']:
                self.view.log(f"✅ 回應碼: {bet_modes_details['status_code']}")

            if bet_modes_details['response']:
                self.view.log(f"📝 回應內容:")
                self.view.log(json.dumps(bet_modes_details['response'], ensure_ascii=False, indent=2))

            # 更新投注模式下拉選單
            self.view.cb_bet_mode['values'] = [mode['name'] for mode in self.bet_modes]
            self.view.log(f"✅ 已載入 {len(self.bet_modes)} 個投注模式")

            # 選擇第一個投注模式
            if self.bet_modes:
                self.view.cb_bet_mode.current(0)
                # 觸發投注模式選擇事件
                self._handle_bet_mode_selected()
            else:
                self.view.log("⚠️ 無法載入投注模式列表")
                self.bet_modes = []
                self.view.cb_bet_mode['values'] = []

        except Exception as e:
            logger.error(f"載入投注模式列表失敗: {e}")
            self.view.log(f"❌ 載入投注模式列表失敗: {str(e)}")

    def _handle_bet_mode_selected(self):
        """處理投注模式選擇事件"""
        try:
            bet_mode_name = self.view.cb_bet_mode.get()
            if not bet_mode_name:
                return

            self.view.log(f"✅ 已選擇投注模式: {bet_mode_name}")

            # 從投注模式名稱中提取投注模式 ID
            bet_mode_id = None
            for mode in self.bet_modes:
                if mode["name"] == bet_mode_name:
                    bet_mode_id = mode["id"]
                    break

            if not bet_mode_id:
                self.view.log("⚠️ 無法取得投注模式 ID")
                return

            # 顯示載入中訊息
            self.view.log(f"🔍 正在載入 BetModeId: {bet_mode_id} 的投注額列表...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT BetModeId, TotalBet FROM Platform.BetModeDetail WHERE BetModeId = {bet_mode_id};\"")
            self.view.log("}")

            # 載入投注額列表
            self.bet_lines, bet_lines_details = self.member_service.get_bet_lines_with_details(bet_mode_id)

            # 顯示回應詳情
            if bet_lines_details['status_code']:
                self.view.log(f"✅ 回應碼: {bet_lines_details['status_code']}")

            if bet_lines_details['response']:
                self.view.log(f"📝 回應內容:")
                self.view.log(json.dumps(bet_lines_details['response'], ensure_ascii=False, indent=2))

            # 更新投注額下拉選單
            self.view.cb_bet_line['values'] = [line['name'] for line in self.bet_lines]
            self.view.log(f"✅ 已載入 {len(self.bet_lines)} 個投注額 (投注模式: {bet_mode_id})")

            # 選擇第一個投注額
            if self.bet_lines:
                self.view.cb_bet_line.current(0)
            else:
                self.view.log("⚠️ 無法載入投注額列表")
                # 清空投注額下拉選單
                self.bet_lines = []
                self.view.cb_bet_line['values'] = []

        except Exception as e:
            logger.error(f"選擇投注模式失敗: {e}")
            self.view.show_error("選擇投注模式失敗", str(e))

    def _load_bet_lines(self):
        """載入投注額列表"""
        try:
            # 顯示載入中訊息
            self.view.log(f"🔍 正在載入投注額列表...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT BetModeId, TotalBet FROM Platform.BetModeDetail;\"")
            self.view.log("}")

            # 載入投注額列表
            self.bet_lines, bet_lines_details = self.member_service.get_bet_lines_with_details()

            # 顯示回應詳情
            if bet_lines_details['status_code']:
                self.view.log(f"✅ 回應碼: {bet_lines_details['status_code']}")

            if bet_lines_details['response']:
                self.view.log(f"📝 回應內容:")
                self.view.log(json.dumps(bet_lines_details['response'], ensure_ascii=False, indent=2))

            # 更新投注額下拉選單
            self.view.cb_bet_line['values'] = [line['name'] for line in self.bet_lines]
            self.view.log(f"✅ 已載入 {len(self.bet_lines)} 個投注額")

            # 選擇第一個投注額
            if self.bet_lines:
                self.view.cb_bet_line.current(0)
            else:
                self.view.log("⚠️ 無法載入投注額列表")
                self.bet_lines = []
                self.view.cb_bet_line['values'] = []

        except Exception as e:
            logger.error(f"載入投注額列表失敗: {e}")
            self.view.log(f"❌ 載入投注額列表失敗: {str(e)}")

    def _handle_query(self):
        """處理查詢事件"""
        try:
            account = self.view.entry_account.get().strip()
            member_id = self.view.entry_member_id.get().strip()
            db_source = self.view.db_source.get()  # 獲取選擇的資料庫來源

            if not account and not member_id:
                raise ValueError("請輸入帳號或會員ID")

            # 顯示查詢中訊息
            self.view.log(f"🔍 正在查詢會員資訊... (資料庫: {db_source})")

            # 使用 MemberService 查詢會員，傳入資料庫來源
            self.current_member, details = self.member_service.query_member(
                account,
                member_id,
                db_source
            )
            # 將查詢詳細資訊存到實例變數中，以便其他方法使用
            self.query_details = details

            # 顯示 API 請求詳細資訊
            self.view.log(f"🔹 API: {details['api_url']}")
            self.view.log(f"📦 Payload:")
            self.view.log(json.dumps(details['payload'], indent=2, ensure_ascii=False))

            # 顯示回應詳細資訊
            if details['status_code']:
                self.view.log(f"✅ 回應碼: {details['status_code']}")

            if details['response']:
                self.view.log(f"📝 回應內容: {json.dumps(details['response'], indent=2, ensure_ascii=False)}")

            # 顯示分隔線
            self.view.log("-" * 46)

            if self.current_member:
                # 顯示會員資訊
                self.view.log(f"✅ 會員查詢成功！")
                self.view.log(f"會員資訊: (ID: {self.current_member.member_id})")
                self.view.log(f"資料庫來源: {self.current_member.db_source}")
                self.view.log(f"帳號: {self.current_member.account}")
                self.view.log(f"代理商: {self.current_member.agent_code}, 子代理商: {self.current_member.sub_agent_code}")
                self.view.log(f"幣別: {self.current_member.currency}, VIP等級: {self.current_member.vip_level}")

                # 不顯示成功提示視窗
            else:
                self.current_member = None
                error_msg = details['error'] if details['error'] else "查無此會員"
                self.view.log(f"❌ 查詢失敗: {error_msg}")
                raise ValueError(error_msg)

        except Exception as e:
            logger.error(f"查詢失敗: {e}")
            self.current_member = None
            self.view.show_error("查詢失敗", str(e))

    def _load_game_list(self):
        """載入遊戲列表"""
        try:
            # 顯示載入中訊息
            self.view.log(f"🔍 正在載入遊戲列表...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT GameId, Name FROM Platform.Games;\"")
            self.view.log("}")

            # 載入遊戲列表
            self.games, games_details = self.member_service.get_games()

            # 顯示回應詳情
            if games_details['status_code']:
                self.view.log(f"✅ 回應碼: {games_details['status_code']}")

            if games_details['response']:
                self.view.log(f"📝 回應內容:")
                self.view.log(json.dumps(games_details['response'], ensure_ascii=False, indent=2))

            if self.games:
                # 更新遊戲下拉選單
                self.view.cb_game['values'] = [game['name'] for game in self.games]
                self.view.log(f"✅ 已載入 {len(self.games)} 個遊戲")

                # 選擇第一個遊戲
                self.view.cb_game.current(0)
                # 不觸發遊戲選擇事件，因為我們已經在 _init_data 中載入了投注額列表
            else:
                self.view.log("⚠️ 無法載入遊戲列表")
                self.games = []
                self.view.cb_game['values'] = []

        except Exception as e:
            logger.error(f"載入遊戲列表失敗: {e}")
            self.view.log(f"❌ 載入遊戲列表失敗: {str(e)}")

    def _handle_game_selected(self):
        """處理遊戲選擇事件"""
        try:
            game_name = self.view.cb_game.get()
            if not game_name:
                return

            self.view.log(f"✅ 已選擇遊戲: {game_name}")

            # 從遊戲名稱中提取遊戲 ID
            game_id = None
            for game in self.games:
                if game["name"] == game_name:
                    game_id = game["id"]
                    break

            if not game_id:
                self.view.log("⚠️ 無法取得遊戲 ID")
                return

            # 顯示載入中訊息
            self.view.log(f"🔍 正在載入 {game_name} 的投注額...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT BetModeId, TotalBet FROM Platform.BetModeDetail;\"")
            self.view.log("}")

            # 載入投注額列表
            # 如果選擇的遊戲有對應的 BetModeId，則使用該 BetModeId
            # 在實際應用中，這裡可能需要先查詢遊戲對應的 BetModeId
            # 目前我們使用預設的第一個 BetModeId
            self.bet_lines, bet_lines_details = self.member_service.get_bet_lines_with_details()

            # 顯示回應詳情
            if bet_lines_details['status_code']:
                self.view.log(f"✅ 回應碼: {bet_lines_details['status_code']}")

            if bet_lines_details['response']:
                self.view.log(f"📝 回應內容:")
                self.view.log(json.dumps(bet_lines_details['response'], ensure_ascii=False, indent=2))

            # 更新投注額下拉選單
            self.view.cb_bet_line['values'] = [line['name'] for line in self.bet_lines]
            self.view.log(f"✅ 已載入 {len(self.bet_lines)} 個投注額")

            # 選擇第一個投注額
            if self.bet_lines:
                self.view.cb_bet_line.current(0)
            else:
                self.view.log("⚠️ 無法載入投注額列表")
                # 清空投注額下拉選單
                self.bet_lines = []
                self.view.cb_bet_line['values'] = []

        except Exception as e:
            logger.error(f"選擇遊戲失敗: {e}")
            self.view.show_error("選擇遊戲失敗", str(e))

    def _handle_add_game(self):
        """處理載入遊戲清單事件"""
        try:
            # 載入遊戲列表
            self._load_game_list()

        except Exception as e:
            logger.error(f"載入遊戲清單失敗: {e}")
            self.view.show_error("載入遊戲清單失敗", str(e))

    def _handle_add_bet_line(self):
        """處理載入投注額事件"""
        try:
            # 載入投注額列表
            self._load_bet_lines()

        except Exception as e:
            logger.error(f"載入投注額失敗: {e}")
            self.view.show_error("載入投注額失敗", str(e))

    def _handle_import_accounts(self):
        """處理批次匯入帳號事件"""
        try:
            # 開啟檔案選擇對話框
            file_path = filedialog.askopenfilename(
                title="選擇帳號檔案",
                filetypes=[("CSV 檔案", "*.csv"), ("TXT 檔案", "*.txt"), ("所有檔案", "*.*")]
            )

            if not file_path:
                self.view.log("⚠️ 未選擇檔案")
                return

            self.view.log(f"🔍 正在讀取檔案: {os.path.basename(file_path)}...")

            # 清除舊資料
            self.batch_accounts = []

            # 讀取檔案
            with open(file_path, 'r', encoding='utf-8') as f:
                # 判斷檔案類型
                if file_path.lower().endswith('.csv'):
                    # CSV 檔案
                    csv_reader = csv.reader(f)
                    header = next(csv_reader, None)  # 跳過標題列

                    # 判斷標題列格式
                    account_col = 0
                    member_id_col = 1

                    if header:
                        for i, col in enumerate(header):
                            if col.lower() in ['account', '帳號', '帳戶']:
                                account_col = i
                            elif col.lower() in ['member_id', 'memberid', '會員id', '會員編號']:
                                member_id_col = i

                    for row in csv_reader:
                        if len(row) > max(account_col, member_id_col):
                            account = row[account_col].strip()
                            member_id = row[member_id_col].strip() if len(row) > member_id_col else ""

                            if account or member_id:
                                self.batch_accounts.append((account, member_id))
                else:
                    # TXT 檔案
                    for line in f:
                        line = line.strip()
                        if not line:
                            continue

                        parts = line.split(',')
                        if len(parts) >= 2:
                            account = parts[0].strip()
                            member_id = parts[1].strip()
                        else:
                            account = line
                            member_id = ""

                        if account or member_id:
                            self.batch_accounts.append((account, member_id))

            # 更新批次狀態
            if self.batch_accounts:
                self.view.update_batch_status(f"已匯入 {len(self.batch_accounts)} 個帳號")

                self.view.log(f"✅ 批次匯入成功: {len(self.batch_accounts)} 個帳號")
                for i, (account, member_id) in enumerate(self.batch_accounts[:10]):  # 只顯示前 10 個
                    self.view.log(f"  {i+1}. 帳號: {account}, 會員ID: {member_id}")

                if len(self.batch_accounts) > 10:
                    self.view.log(f"  ... 共 {len(self.batch_accounts)} 個帳號")
            else:
                self.view.update_batch_status("未匯入帳號")
                raise ValueError("檔案中沒有有效的帳號資料")

        except Exception as e:
            logger.error(f"批次匯入失敗: {e}")
            self.view.show_error("批次匯入失敗", str(e))

    def _handle_batch_update(self):
        """處理批次更新事件"""
        try:
            # 如果已經在處理中，則不再執行
            if self.is_processing:
                return

            if not self.batch_accounts:
                raise ValueError("尚未匯入帳號，無法進行批次更新")

            # 檢查卡片數量 - 使用共用的卡片數量參數
            try:
                card_count = int(self.view.entry_card_count.get() or "1")
                if card_count <= 0 or card_count > 99:
                    raise ValueError("卡片數量必須在 1-99 之間")
            except ValueError:
                raise ValueError("卡片數量必須是有效的整數")

            # 檢查遊戲和押注線
            game_name = self.view.cb_game.get()
            if not game_name:
                raise ValueError("請選擇遊戲")

            bet_line_name = self.view.cb_bet_line.get()
            if not bet_line_name:
                raise ValueError("請選擇押注線")

            # 取得批次處理的資料庫來源
            batch_db_source = self.view.batch_db_source.get()

            # 從遊戲名稱中提取遊戲 ID
            game_id = None
            for game in self.games:
                if game["name"] == game_name:
                    game_id = game["id"]
                    break

            if not game_id:
                raise ValueError(f"無法找到遊戲 ID: {game_name}")

            # 從押注線名稱中提取押注線值
            bet_line_value = None
            for line in self.bet_lines:
                if line["name"] == bet_line_name:
                    bet_line_value = line["value"]
                    break

            if not bet_line_value:
                raise ValueError(f"無法找到押注線值: {bet_line_name}")

            # 獲取遊戲道具 ID
            self._log_to_queue(f"🔍 正在獲取遊戲道具 ID...")

            # 顯示 API 請求詳情
            self._log_to_queue(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self._log_to_queue(f"📦 Payload:")
            self._log_to_queue("{")
            self._log_to_queue(f"  \"action\": \"query\",")
            self._log_to_queue(f"  \"serverTag\": \"igaming-mysql-main\",")
            self._log_to_queue(f"  \"sql\": \"SELECT Id FROM Platform.GameItem WHERE TotalBet = '{bet_line_value:.6f}' AND GameId = {game_id};\"")
            self._log_to_queue("}")

            game_item_ids, item_details = self.member_service.get_game_item_ids(game_id, float(bet_line_value))

            # 顯示回應詳情
            if item_details['status_code']:
                self._log_to_queue(f"✅ 回應碼: {item_details['status_code']}")

            if item_details['response']:
                self._log_to_queue(f"📝 回應內容: {json.dumps(item_details['response'], ensure_ascii=False)}")

            if not game_item_ids:
                raise ValueError(f"無法找到遊戲道具 ID: 遊戲={game_id}, 投注額={bet_line_value:.6f}")

            # 將資料儲存到實例變數中，以便在線程中使用
            batch_data = {
                "game_name": game_name,
                "game_id": game_id,
                "bet_line_name": bet_line_name,
                "bet_line_value": bet_line_value,
                "card_count": card_count,
                "game_item_ids": game_item_ids,
                "batch_accounts": self.batch_accounts.copy(),
                "db_source": batch_db_source  # 添加資料庫來源
            }

            # 設置處理中狀態
            self.is_processing = True

            # 啟動新線程執行批次處理
            threading.Thread(target=self._batch_update_thread, args=(batch_data,), daemon=True).start()

            # 顯示處理中訊息
            self._log_to_queue(f"🔍 正在為 {len(self.batch_accounts)} 個帳號新增卡片...")
            self._log_to_queue(f"📊 使用資料庫來源: {batch_db_source}")
            self._log_to_queue("ℹ️ 批次處理已在背景執行，請稍候...")

        except Exception as e:
            logger.error(f"批次更新失敗: {e}")
            self.view.show_error("批次更新失敗", str(e))

    def _process_single_account_update(self, account, member_id, game_item_ids, card_count, db_source="OCMS"):
        """處理單個帳號的卡片更新

        Args:
            account: 會員帳號
            member_id: 會員ID
            game_item_ids: 遊戲道具ID列表
            card_count: 卡片數量
            db_source: 資料庫來源，預設為 "OCMS"

        Returns:
            tuple: (成功狀態, 帳號, 會員ID, 成功項目數, 失敗項目數, 錯誤訊息)
        """
        try:
            # 查詢會員資訊，使用指定的資料庫來源
            member, query_details = self.member_service.query_member(account=account, member_id=member_id, db_source=db_source)
            if not member:
                error_msg = query_details.get('error', '查詢失敗')
                return (False, account, member_id, 0, 0, error_msg)

            # 為會員新增卡片
            success_items = 0
            fail_items = 0

            for item_id in game_item_ids:
                result, _ = self.member_service.add_cards(member, int(item_id), card_count)
                if result:
                    success_items += 1
                else:
                    fail_items += 1

            if success_items > 0:
                return (True, account, member.member_id, success_items, fail_items, None)
            else:
                return (False, account, member.member_id, 0, fail_items, "新增失敗")

        except Exception as e:
            return (False, account, member_id, 0, 0, str(e))

    def _batch_update_thread(self, batch_data):
        """批次更新線程函數 - 使用線程池提高效能"""
        try:
            game_name = batch_data["game_name"]
            game_id = batch_data["game_id"]
            bet_line_name = batch_data["bet_line_name"]
            bet_line_value = batch_data["bet_line_value"]
            card_count = batch_data["card_count"]
            game_item_ids = batch_data["game_item_ids"]
            batch_accounts = batch_data["batch_accounts"]
            db_source = batch_data.get("db_source", "OCMS")  # 獲取資料庫來源，預設為 OCMS

            success_accounts = 0
            fail_accounts = 0

            # 顯示開始處理訊息
            self._log_to_queue(f"🔄 開始批次處理 {len(batch_accounts)} 個帳號...")
            self._log_to_queue(f"📊 資料庫來源: {db_source}")
            self._log_to_queue(f"🎮 遊戲: {game_name}")
            self._log_to_queue(f"💰 押注線: {bet_line_name}")
            self._log_to_queue(f"🎴 每個帳號卡片數量: {card_count}")

            # 使用線程池處理批次帳號
            # 根據CPU核心數和帳號數量決定線程池大小，但最多不超過10個線程
            max_workers = min(10, len(batch_accounts))

            # 創建處理單個帳號的函數的偏函數，固定game_item_ids、card_count和db_source參數
            process_account = partial(self._process_single_account_update,
                                     game_item_ids=game_item_ids,
                                     card_count=card_count,
                                     db_source=db_source)

            # 創建進度計數器
            processed_count = 0
            total_count = len(batch_accounts)

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任務
                future_to_account = {executor.submit(process_account, account, member_id): (account, member_id)
                                    for account, member_id in batch_accounts}

                # 處理完成的任務
                for future in concurrent.futures.as_completed(future_to_account):
                    account, member_id = future_to_account[future]
                    processed_count += 1

                    # 每處理10%的帳號顯示一次進度
                    if processed_count % max(1, total_count // 10) == 0 or processed_count == total_count:
                        progress_percent = int(processed_count / total_count * 100)
                        self._log_to_queue(f"⏳ 處理進度: {processed_count}/{total_count} ({progress_percent}%)")

                    try:
                        success, account, member_id, success_items, fail_items, error_msg = future.result()

                        if success:
                            self._log_to_queue(f"  ✅ 帳號 {account} (會員ID: {member_id}) 新增成功: {success_items} 個道具, 失敗: {fail_items} 個道具")
                            success_accounts += 1
                        else:
                            self._log_to_queue(f"  ❌ 帳號 {account} (會員ID: {member_id}) 處理失敗: {error_msg}")
                            fail_accounts += 1

                    except Exception as e:
                        self._log_to_queue(f"  ❌ 帳號 {account} (會員ID: {member_id}) 處理失敗: {str(e)}")
                        fail_accounts += 1

                    # 每處理一批次後暫停一下，讓UI有時間更新
                    if processed_count % 20 == 0:
                        time.sleep(0.05)

            # 顯示結果統計
            self._log_to_queue(f"\n✅ 批次處理完成: 成功 {success_accounts} 個帳號, 失敗 {fail_accounts} 個帳號")

            if success_accounts <= 0:
                self._log_to_queue(f"❌ 批次更新失敗，所有帳號處理均失敗")

        except Exception as e:
            logger.error(f"批次更新線程失敗: {e}")
            self._log_to_queue(f"❌ 批次更新失敗: {str(e)}")

        finally:
            # 處理完成，重設狀態
            self.is_processing = False

    def _handle_add_card(self):
        """處理新增卡片事件"""
        try:
            if not self.current_member:
                raise ValueError("請先查詢會員資訊")

            game_name = self.view.cb_game.get()
            if not game_name:
                raise ValueError("請選擇遊戲")

            bet_line_name = self.view.cb_bet_line.get()
            if not bet_line_name:
                raise ValueError("請選擇押注線")

            # 取得卡片數量
            try:
                card_count = int(self.view.entry_card_count.get() or "1")
                if card_count <= 0 or card_count > 99:
                    raise ValueError("卡片數量必須在 1-99 之間")
            except ValueError:
                raise ValueError("卡片數量必須是有效的整數")

            # 從遊戲名稱中提取遊戲 ID
            game_id = None
            for game in self.games:
                if game["name"] == game_name:
                    game_id = game["id"]
                    break

            if not game_id:
                raise ValueError(f"無法找到遊戲 ID: {game_name}")

            # 從押注線名稱中提取押注線值
            bet_line_value = None
            for line in self.bet_lines:
                if line["name"] == bet_line_name:
                    bet_line_value = line["value"]
                    break

            if not bet_line_value:
                raise ValueError(f"無法找到押注線值: {bet_line_name}")

            # 獲取遊戲道具 ID
            self.view.log(f"🔍 正在獲取遊戲道具 ID...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"query\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"SELECT Id FROM Platform.GameItem WHERE TotalBet = '{bet_line_value:.6f}' AND GameId = {game_id};\"")
            self.view.log("}")

            game_item_ids, item_details = self.member_service.get_game_item_ids(game_id, float(bet_line_value))

            # 顯示回應詳情
            if item_details['status_code']:
                self.view.log(f"✅ 回應碼: {item_details['status_code']}")

            if item_details['response']:
                self.view.log(f"📝 回應內容: {json.dumps(item_details['response'], ensure_ascii=False)}")

            if not game_item_ids:
                raise ValueError(f"無法找到遊戲道具 ID: 遊戲={game_id}, 投注額={bet_line_value:.6f}")

            # 新增卡片
            self.view.log(f"🔍 正在新增卡片...")
            success_count = 0
            fail_count = 0

            for item_id in game_item_ids:
                # 顯示 API 請求詳情
                self.view.log(f"🔹 API: https://gp001-qa1-tokenguard.xwautc.online/debug/common/RewardService/RewardOperateRequest/RedeemRewardNode")
                self.view.log(f"📦 Payload:")
                payload = {
                    "MemberId": self.current_member.member_id,
                    "AccountInfo": {
                        "account": self.current_member.account,
                        "agentCode": self.current_member.agent_code,
                        "subAgentCode": self.current_member.sub_agent_code,
                        "currency": self.current_member.currency
                    },
                    "source": "GBAO",
                    "description": "addItem",
                    "rewardNodeList": [
                        {
                            "id": item_id,
                            "num": card_count,
                            "type": "Item"
                        }
                    ]
                }
                self.view.log(json.dumps(payload, indent=2, ensure_ascii=False))

                # 新增卡片
                result, card_details = self.member_service.add_cards(self.current_member, int(item_id), card_count)

                # 顯示回應詳情
                if card_details['status_code']:
                    self.view.log(f"✅ 回應碼: {card_details['status_code']}")

                if card_details['response']:
                    self.view.log(f"📝 回應內容: {json.dumps(card_details['response'], ensure_ascii=False)}")

                if result:
                    success_count += 1
                else:
                    fail_count += 1

            # 顯示分隔線
            self.view.log("-" * 46)

            # 顯示結果統計
            if success_count > 0:
                self.view.log(f"✅ 新增卡片完成")
                self.view.log(f"成功: {success_count} 筆")
                self.view.log(f"失敗: {fail_count} 筆")
            else:
                raise ValueError("新增卡片失敗，請檢查日誌")

        except Exception as e:
            logger.error(f"新增卡片失敗: {e}")
            self.view.show_error("新增卡片失敗", str(e))

    def _handle_delete_all_cards(self):
        """處理刪除所有卡片事件"""
        try:
            if not self.current_member:
                raise ValueError("請先查詢會員資訊")

            # 刪除所有卡片
            self.view.log(f"🔍 正在刪除會員 {self.current_member.account} 的所有卡片...")

            # 顯示 API 請求詳情
            self.view.log(f"🔹 API: http://************:5000/tools/db/mysql-operator")
            self.view.log(f"📦 Payload:")
            self.view.log("{")
            self.view.log(f"  \"action\": \"delete\",")
            self.view.log(f"  \"serverTag\": \"igaming-mysql-main\",")
            self.view.log(f"  \"sql\": \"DELETE FROM OCMS.MemberOwnItems WHERE MemberId = {self.current_member.member_id}\"")
            self.view.log("}")

            result, delete_details = self.member_service.delete_all_cards(self.current_member.member_id)

            # 顯示回應詳情
            if delete_details['status_code']:
                self.view.log(f"✅ 回應碼: {delete_details['status_code']}")

            if delete_details['response']:
                self.view.log(f"📝 回應內容: {json.dumps(delete_details['response'], ensure_ascii=False)}")

            if result:
                self.view.log(f"✅ 刪除所有卡片成功:")
                self.view.log(f"  會員: {self.current_member.account} ({self.current_member.member_id})")
            else:
                error_msg = delete_details.get('error', '刪除卡片失敗')
                raise ValueError(f"刪除卡片失敗: {error_msg}")

        except Exception as e:
            logger.error(f"刪除卡片失敗: {e}")
            self.view.show_error("刪除卡片失敗", str(e))

    def _handle_batch_delete(self):
        """處理批次刪除事件"""
        try:
            # 如果已經在處理中，則不再執行
            if self.is_processing:
                return

            if not self.batch_accounts:
                raise ValueError("尚未匯入帳號，無法進行批次刪除")

            # 取得批次處理的資料庫來源
            batch_db_source = self.view.batch_db_source.get()

            # 將資料儲存到實例變數中，以便在線程中使用
            batch_data = {
                "batch_accounts": self.batch_accounts.copy(),
                "db_source": batch_db_source  # 添加資料庫來源
            }

            # 設置處理中狀態
            self.is_processing = True

            # 啟動新線程執行批次處理
            threading.Thread(target=self._batch_delete_thread, args=(batch_data,), daemon=True).start()

            # 顯示處理中訊息
            self._log_to_queue(f"🔍 正在為 {len(self.batch_accounts)} 個帳號刪除卡片...")
            self._log_to_queue(f"📊 使用資料庫來源: {batch_db_source}")
            self._log_to_queue("ℹ️ 批次處理已在背景執行，請稍候...")

        except Exception as e:
            logger.error(f"批次刪除失敗: {e}")
            self.view.show_error("批次刪除失敗", str(e))

    def _process_single_account_delete(self, account, member_id, db_source="OCMS"):
        """處理單個帳號的卡片刪除

        Args:
            account: 會員帳號
            member_id: 會員ID
            db_source: 資料庫來源，預設為 "OCMS"

        Returns:
            tuple: (成功狀態, 帳號, 會員ID, 錯誤訊息)
        """
        try:
            # 查詢會員資訊，使用指定的資料庫來源
            member, query_details = self.member_service.query_member(account=account, member_id=member_id, db_source=db_source)
            if not member:
                error_msg = query_details.get('error', '查詢失敗')
                return (False, account, member_id, error_msg)

            # 刪除會員的所有卡片
            result, delete_details = self.member_service.delete_all_cards(member.member_id)

            if result:
                return (True, account, member.member_id, None)
            else:
                error_msg = delete_details.get('error', '刪除失敗')
                return (False, account, member.member_id, error_msg)

        except Exception as e:
            return (False, account, member_id, str(e))

    def _batch_delete_thread(self, batch_data):
        """批次刪除線程函數 - 使用線程池提高效能"""
        try:
            batch_accounts = batch_data["batch_accounts"]
            db_source = batch_data.get("db_source", "OCMS")  # 獲取資料庫來源，預設為 OCMS

            success_accounts = 0
            fail_accounts = 0

            # 顯示開始處理訊息
            self._log_to_queue(f"🔄 開始批次刪除 {len(batch_accounts)} 個帳號的卡片...")
            self._log_to_queue(f"📊 資料庫來源: {db_source}")

            # 使用線程池處理批次帳號
            # 根據CPU核心數和帳號數量決定線程池大小，但最多不超過10個線程
            max_workers = min(10, len(batch_accounts))

            # 創建進度計數器
            processed_count = 0
            total_count = len(batch_accounts)

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任務，傳遞資料庫來源參數
                future_to_account = {executor.submit(self._process_single_account_delete, account, member_id, db_source): (account, member_id)
                                    for account, member_id in batch_accounts}

                # 處理完成的任務
                for future in concurrent.futures.as_completed(future_to_account):
                    account, member_id = future_to_account[future]
                    processed_count += 1

                    # 每處理10%的帳號顯示一次進度
                    if processed_count % max(1, total_count // 10) == 0 or processed_count == total_count:
                        progress_percent = int(processed_count / total_count * 100)
                        self._log_to_queue(f"⏳ 處理進度: {processed_count}/{total_count} ({progress_percent}%)")

                    try:
                        success, account, member_id, error_msg = future.result()

                        if success:
                            self._log_to_queue(f"  ✅ 帳號 {account} (會員ID: {member_id}) 刪除成功")
                            success_accounts += 1
                        else:
                            self._log_to_queue(f"  ❌ 帳號 {account} (會員ID: {member_id}) 刪除失敗: {error_msg}")
                            fail_accounts += 1

                    except Exception as e:
                        self._log_to_queue(f"  ❌ 帳號 {account} (會員ID: {member_id}) 處理失敗: {str(e)}")
                        fail_accounts += 1

                    # 每處理一批次後暫停一下，讓UI有時間更新
                    if processed_count % 20 == 0:
                        time.sleep(0.05)

            # 顯示結果統計
            self._log_to_queue(f"\n✅ 批次刪除完成: 成功 {success_accounts} 個帳號, 失敗 {fail_accounts} 個帳號")

            if success_accounts <= 0:
                self._log_to_queue(f"❌ 批次刪除失敗，所有帳號處理均失敗")

        except Exception as e:
            logger.error(f"批次刪除線程失敗: {e}")
            self._log_to_queue(f"❌ 批次刪除失敗: {str(e)}")

        finally:
            # 處理完成，重設狀態
            self.is_processing = False

