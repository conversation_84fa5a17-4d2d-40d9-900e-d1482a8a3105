"""資源監控元件

此模組提供資源監控元件，用於顯示系統資源使用情況。
"""
import tkinter as tk
from tkinter import ttk
import time
import threading
import psutil
from typing import Optional, Callable, Dict, Any, List, Tuple
from utils.theme import ThemeManager
try:
    from utils.memory_monitor import memory_monitor
except ImportError:
    # 如果無法導入 memory_monitor，創建一個模擬對象
    class MemoryMonitorMock:
        def __init__(self):
            self.threshold_mb = 200.0

        def get_memory_usage(self):
            return {
                "process_memory_mb": 0,
                "system_memory_total_mb": 0,
                "system_memory_available_mb": 0,
                "system_memory_used_mb": 0,
                "system_memory_percent": 0
            }

    memory_monitor = MemoryMonitorMock()

class ResourceMonitor(ttk.Frame):
    """資源監控元件

    提供資源監控元件，用於顯示系統資源使用情況。

    Args:
        parent: 父元件
        update_interval: 更新間隔（秒）
        show_memory: 是否顯示內存使用
        show_cpu: 是否顯示 CPU 使用
        show_disk: 是否顯示磁盤使用
        show_network: 是否顯示網絡使用
        **kwargs: 其他參數
    """

    def __init__(
        self,
        parent,
        update_interval: float = 1.0,
        show_memory: bool = True,
        show_cpu: bool = True,
        show_disk: bool = False,
        show_network: bool = False,
        **kwargs
    ):
        super().__init__(parent, **kwargs)

        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 設定變數
        self.update_interval = update_interval
        self.show_memory = show_memory
        self.show_cpu = show_cpu
        self.show_disk = show_disk
        self.show_network = show_network

        self.is_monitoring = False
        self.monitor_thread = None
        self.monitor_lock = threading.RLock()

        # 初始化 UI
        self._init_ui()

    def _init_ui(self):
        """初始化 UI"""
        # 建立容器
        self.container = ttk.Frame(self)
        self.container.pack(fill=tk.BOTH, expand=True)

        # 建立標題標籤
        self.title_label = ttk.Label(
            self.container,
            text="系統資源監控",
            font=self.theme_manager.get_font("subtitle"),
            foreground=self.theme_manager.get_color("text_primary")
        )
        self.title_label.pack(side=tk.TOP, padx=5, pady=5)

        # 建立資源容器
        self.resource_container = ttk.Frame(self.container)
        self.resource_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 建立內存使用元件
        if self.show_memory:
            self.memory_frame = ttk.LabelFrame(
                self.resource_container,
                text="內存使用"
            )
            self.memory_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立內存進度條
            self.memory_progress = ttk.Progressbar(
                self.memory_frame,
                orient="horizontal",
                length=200,
                mode="determinate"
            )
            self.memory_progress.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立內存標籤
            self.memory_label = ttk.Label(
                self.memory_frame,
                text="0% (0 MB / 0 MB)",
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.memory_label.pack(side=tk.TOP, padx=5, pady=5)

        # 建立 CPU 使用元件
        if self.show_cpu:
            self.cpu_frame = ttk.LabelFrame(
                self.resource_container,
                text="CPU 使用"
            )
            self.cpu_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立 CPU 進度條
            self.cpu_progress = ttk.Progressbar(
                self.cpu_frame,
                orient="horizontal",
                length=200,
                mode="determinate"
            )
            self.cpu_progress.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立 CPU 標籤
            self.cpu_label = ttk.Label(
                self.cpu_frame,
                text="0%",
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.cpu_label.pack(side=tk.TOP, padx=5, pady=5)

        # 建立磁盤使用元件
        if self.show_disk:
            self.disk_frame = ttk.LabelFrame(
                self.resource_container,
                text="磁盤使用"
            )
            self.disk_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立磁盤進度條
            self.disk_progress = ttk.Progressbar(
                self.disk_frame,
                orient="horizontal",
                length=200,
                mode="determinate"
            )
            self.disk_progress.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立磁盤標籤
            self.disk_label = ttk.Label(
                self.disk_frame,
                text="0% (0 GB / 0 GB)",
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.disk_label.pack(side=tk.TOP, padx=5, pady=5)

        # 建立網絡使用元件
        if self.show_network:
            self.network_frame = ttk.LabelFrame(
                self.resource_container,
                text="網絡使用"
            )
            self.network_frame.pack(fill=tk.X, expand=True, padx=5, pady=5)

            # 建立網絡標籤
            self.network_label = ttk.Label(
                self.network_frame,
                text="↑ 0 KB/s  ↓ 0 KB/s",
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.network_label.pack(side=tk.TOP, padx=5, pady=5)

    def start_monitoring(self):
        """開始監控"""
        with self.monitor_lock:
            if self.is_monitoring:
                return

            self.is_monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_thread,
                daemon=True
            )
            self.monitor_thread.start()

    def stop_monitoring(self):
        """停止監控"""
        with self.monitor_lock:
            if not self.is_monitoring:
                return

            self.is_monitoring = False

            # 取消所有待處理的 after 調用
            try:
                if self.winfo_exists():
                    # 獲取所有待處理的 after 調用 ID
                    for after_id in self.tk.call('after', 'info'):
                        try:
                            self.after_cancel(after_id)
                        except Exception:
                            pass
            except Exception:
                pass

    def _monitor_thread(self):
        """監控線程"""
        try:
            # 初始化網絡計數器
            last_net_io = psutil.net_io_counters()
            last_time = time.time()

            while self.is_monitoring:
                # 更新內存使用
                if self.show_memory:
                    self._update_memory_usage()

                # 更新 CPU 使用
                if self.show_cpu:
                    self._update_cpu_usage()

                # 更新磁盤使用
                if self.show_disk:
                    self._update_disk_usage()

                # 更新網絡使用
                if self.show_network:
                    self._update_network_usage(last_net_io, last_time)
                    last_net_io = psutil.net_io_counters()
                    last_time = time.time()

                # 等待下一次更新
                time.sleep(self.update_interval)

        except Exception as e:
            print(f"監控線程異常: {e}")

    def _update_memory_usage(self):
        """更新內存使用"""
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 獲取內存使用情況
            memory_info = memory_monitor.get_memory_usage()
            process_memory_mb = memory_info.get("process_memory_mb", 0)
            system_memory_total_mb = memory_info.get("system_memory_total_mb", 0)
            system_memory_used_mb = memory_info.get("system_memory_used_mb", 0)
            system_memory_percent = memory_info.get("system_memory_percent", 0)

            # 使用 after 方法在主線程中更新 UI
            try:
                if self.winfo_exists() and self.is_monitoring:
                    self.after(0, self._update_memory_ui,
                              system_memory_percent,
                              system_memory_used_mb,
                              system_memory_total_mb,
                              process_memory_mb)
            except Exception:
                self.is_monitoring = False

        except Exception as e:
            print(f"更新內存使用異常: {e}")
            self.is_monitoring = False

    def _update_memory_ui(self, system_memory_percent, system_memory_used_mb, system_memory_total_mb, process_memory_mb):
        """在主線程中更新內存 UI

        Args:
            system_memory_percent: 系統內存使用百分比
            system_memory_used_mb: 系統已用內存 (MB)
            system_memory_total_mb: 系統總內存 (MB)
            process_memory_mb: 進程內存使用 (MB)
        """
        try:
            # 檢查視窗是否仍然存在
            if not self.winfo_exists():
                self.is_monitoring = False
                return

            # 更新進度條
            self.memory_progress["value"] = system_memory_percent

            # 更新標籤
            self.memory_label.config(
                text=f"{system_memory_percent:.1f}% ({system_memory_used_mb:.1f} MB / {system_memory_total_mb:.1f} MB) 進程: {process_memory_mb:.1f} MB"
            )

            # 設定進度條顏色
            if system_memory_percent > 90:
                self._set_progress_color(self.memory_progress, "error")
            elif system_memory_percent > 70:
                self._set_progress_color(self.memory_progress, "warning")
            else:
                self._set_progress_color(self.memory_progress, "success")
        except Exception as e:
            # 如果發生異常，可能是視窗已經被銷毀
            self.is_monitoring = False
            print(f"更新內存 UI 異常: {e}")

    def _update_cpu_usage(self):
        """更新 CPU 使用"""
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 獲取 CPU 使用情況
            cpu_percent = psutil.cpu_percent()

            # 使用 after 方法在主線程中更新 UI
            try:
                if self.winfo_exists() and self.is_monitoring:
                    self.after(0, self._update_cpu_ui, cpu_percent)
            except Exception:
                self.is_monitoring = False

        except Exception as e:
            print(f"更新 CPU 使用異常: {e}")
            self.is_monitoring = False

    def _update_cpu_ui(self, cpu_percent):
        """在主線程中更新 CPU UI

        Args:
            cpu_percent: CPU 使用百分比
        """
        try:
            # 檢查視窗是否仍然存在
            if not self.winfo_exists():
                self.is_monitoring = False
                return

            # 更新進度條
            self.cpu_progress["value"] = cpu_percent

            # 更新標籤
            self.cpu_label.config(
                text=f"{cpu_percent:.1f}%"
            )

            # 設定進度條顏色
            if cpu_percent > 90:
                self._set_progress_color(self.cpu_progress, "error")
            elif cpu_percent > 70:
                self._set_progress_color(self.cpu_progress, "warning")
            else:
                self._set_progress_color(self.cpu_progress, "success")
        except Exception as e:
            # 如果發生異常，可能是視窗已經被銷毀
            self.is_monitoring = False
            print(f"更新 CPU UI 異常: {e}")

    def _update_disk_usage(self):
        """更新磁盤使用"""
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 獲取磁盤使用情況
            disk_usage = psutil.disk_usage("/")
            disk_total_gb = disk_usage.total / (1024 * 1024 * 1024)
            disk_used_gb = disk_usage.used / (1024 * 1024 * 1024)
            disk_percent = disk_usage.percent

            # 使用 after 方法在主線程中更新 UI
            try:
                if self.winfo_exists() and self.is_monitoring:
                    self.after(0, self._update_disk_ui, disk_percent, disk_used_gb, disk_total_gb)
            except Exception:
                self.is_monitoring = False

        except Exception as e:
            print(f"更新磁盤使用異常: {e}")
            self.is_monitoring = False

    def _update_disk_ui(self, disk_percent, disk_used_gb, disk_total_gb):
        """在主線程中更新磁盤 UI

        Args:
            disk_percent: 磁盤使用百分比
            disk_used_gb: 已用磁盤空間 (GB)
            disk_total_gb: 總磁盤空間 (GB)
        """
        try:
            # 檢查視窗是否仍然存在
            if not self.winfo_exists():
                self.is_monitoring = False
                return

            # 更新進度條
            self.disk_progress["value"] = disk_percent

            # 更新標籤
            self.disk_label.config(
                text=f"{disk_percent:.1f}% ({disk_used_gb:.1f} GB / {disk_total_gb:.1f} GB)"
            )

            # 設定進度條顏色
            if disk_percent > 90:
                self._set_progress_color(self.disk_progress, "error")
            elif disk_percent > 70:
                self._set_progress_color(self.disk_progress, "warning")
            else:
                self._set_progress_color(self.disk_progress, "success")
        except Exception as e:
            # 如果發生異常，可能是視窗已經被銷毀
            self.is_monitoring = False
            print(f"更新磁盤 UI 異常: {e}")

    def _update_network_usage(self, last_net_io, last_time):
        """更新網絡使用

        Args:
            last_net_io: 上次網絡 IO 計數器
            last_time: 上次時間
        """
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 獲取網絡使用情況
            net_io = psutil.net_io_counters()
            current_time = time.time()

            # 計算網絡速度
            bytes_sent = net_io.bytes_sent - last_net_io.bytes_sent
            bytes_recv = net_io.bytes_recv - last_net_io.bytes_recv
            time_elapsed = current_time - last_time

            # 計算網絡速度（KB/s）
            sent_speed = bytes_sent / time_elapsed / 1024
            recv_speed = bytes_recv / time_elapsed / 1024

            # 使用 after 方法在主線程中更新 UI
            try:
                if self.winfo_exists() and self.is_monitoring:
                    self.after(0, self._update_network_ui, sent_speed, recv_speed)
            except Exception:
                self.is_monitoring = False

        except Exception as e:
            print(f"更新網絡使用異常: {e}")
            self.is_monitoring = False

    def _update_network_ui(self, sent_speed, recv_speed):
        """在主線程中更新網絡 UI

        Args:
            sent_speed: 發送速度 (KB/s)
            recv_speed: 接收速度 (KB/s)
        """
        try:
            # 檢查視窗是否仍然存在
            if not self.winfo_exists():
                self.is_monitoring = False
                return

            # 更新標籤
            self.network_label.config(
                text=f"↑ {sent_speed:.1f} KB/s  ↓ {recv_speed:.1f} KB/s"
            )
        except Exception as e:
            # 如果發生異常，可能是視窗已經被銷毀
            self.is_monitoring = False
            print(f"更新網絡 UI 異常: {e}")

    def _set_progress_color(self, progress_bar, color_name):
        """設定進度條顏色

        Args:
            progress_bar: 進度條元件
            color_name: 顏色名稱
        """
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 獲取顏色
            color = self.theme_manager.get_color(color_name)

            # 使用 after 方法在主線程中更新 UI
            try:
                if self.winfo_exists() and self.is_monitoring:
                    self.after(0, self._set_progress_color_ui, color)
            except Exception:
                self.is_monitoring = False

        except Exception as e:
            print(f"設定進度條顏色異常: {e}")
            self.is_monitoring = False

    def _set_progress_color_ui(self, color):
        """在主線程中設定進度條顏色

        Args:
            color: 顏色值
        """
        try:
            # 檢查是否仍在監控中
            if not self.is_monitoring:
                return

            # 檢查視窗是否仍然存在
            try:
                if not self.winfo_exists():
                    self.is_monitoring = False
                    return
            except Exception:
                self.is_monitoring = False
                return

            # 設定進度條顏色
            try:
                style = ttk.Style()
                style.configure(
                    "TProgressbar",
                    background=color
                )
            except Exception as e:
                self.is_monitoring = False
                print(f"設定進度條樣式異常: {e}")

        except Exception as e:
            # 如果發生異常，可能是視窗已經被銷毀
            self.is_monitoring = False
            print(f"設定進度條顏色 UI 異常: {e}")

    def update_now(self):
        """立即更新"""
        if self.show_memory:
            self._update_memory_usage()

        if self.show_cpu:
            self._update_cpu_usage()

        if self.show_disk:
            self._update_disk_usage()

        if self.show_network:
            last_net_io = psutil.net_io_counters()
            last_time = time.time()
            time.sleep(0.1)
            self._update_network_usage(last_net_io, last_time)
