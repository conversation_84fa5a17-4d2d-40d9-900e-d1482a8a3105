"""增強版日誌記錄器

此模組提供增強版日誌記錄功能，支援日誌分級、過濾和導出。
"""
import os
import sys
import logging
import time
import json
import threading
import traceback
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from typing import Dict, Any, Optional, List, Tuple, Union

class EnhancedLogger:
    """增強版日誌記錄器
    
    提供增強版日誌記錄功能，支援日誌分級、過濾和導出。
    
    Args:
        name: 日誌名稱
        level: 日誌級別
        log_dir: 日誌目錄
        max_file_size: 最大文件大小（MB）
        backup_count: 備份文件數量
        console_output: 是否輸出到控制台
        file_output: 是否輸出到文件
        json_format: 是否使用 JSON 格式
        add_thread_info: 是否添加線程信息
        add_process_info: 是否添加進程信息
        add_module_info: 是否添加模塊信息
        add_line_info: 是否添加行號信息
    """
    
    def __init__(
        self,
        name: str = "app",
        level: int = logging.INFO,
        log_dir: str = "logs",
        max_file_size: int = 10,
        backup_count: int = 5,
        console_output: bool = True,
        file_output: bool = True,
        json_format: bool = False,
        add_thread_info: bool = True,
        add_process_info: bool = True,
        add_module_info: bool = True,
        add_line_info: bool = True
    ):
        self.name = name
        self.level = level
        self.log_dir = log_dir
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.console_output = console_output
        self.file_output = file_output
        self.json_format = json_format
        self.add_thread_info = add_thread_info
        self.add_process_info = add_process_info
        self.add_module_info = add_module_info
        self.add_line_info = add_line_info
        
        # 創建日誌目錄
        self._create_log_dir()
        
        # 創建日誌記錄器
        self.logger = self._create_logger()
        
        # 日誌緩存
        self.log_cache = []
        self.max_cache_size = 1000
        self.cache_lock = threading.RLock()
        
    def _create_log_dir(self):
        """創建日誌目錄"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
        except Exception as e:
            print(f"創建日誌目錄失敗: {e}")
            
    def _create_logger(self) -> logging.Logger:
        """創建日誌記錄器
        
        Returns:
            logging.Logger: 日誌記錄器
        """
        # 創建日誌記錄器
        logger = logging.getLogger(self.name)
        logger.setLevel(self.level)
        
        # 清除現有處理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
            
        # 創建格式化器
        if self.json_format:
            formatter = self._create_json_formatter()
        else:
            formatter = self._create_text_formatter()
            
        # 添加控制台處理器
        if self.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(self.level)
            logger.addHandler(console_handler)
            
        # 添加文件處理器
        if self.file_output:
            # 創建日誌文件路徑
            log_file = os.path.join(self.log_dir, f"{self.name}.log")
            
            # 創建文件處理器
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=self.max_file_size * 1024 * 1024,
                backupCount=self.backup_count,
                encoding="utf-8"
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(self.level)
            logger.addHandler(file_handler)
            
            # 創建錯誤日誌文件處理器
            error_log_file = os.path.join(self.log_dir, f"{self.name}_error.log")
            error_file_handler = RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size * 1024 * 1024,
                backupCount=self.backup_count,
                encoding="utf-8"
            )
            error_file_handler.setFormatter(formatter)
            error_file_handler.setLevel(logging.ERROR)
            logger.addHandler(error_file_handler)
            
            # 創建每日日誌文件處理器
            daily_log_file = os.path.join(self.log_dir, f"{self.name}_daily.log")
            daily_file_handler = TimedRotatingFileHandler(
                daily_log_file,
                when="midnight",
                interval=1,
                backupCount=30,
                encoding="utf-8"
            )
            daily_file_handler.setFormatter(formatter)
            daily_file_handler.setLevel(self.level)
            logger.addHandler(daily_file_handler)
            
        return logger
        
    def _create_text_formatter(self) -> logging.Formatter:
        """創建文本格式化器
        
        Returns:
            logging.Formatter: 文本格式化器
        """
        # 創建格式字符串
        format_parts = ["%(asctime)s - %(levelname)s"]
        
        if self.add_process_info:
            format_parts.append("%(process)d")
            
        if self.add_thread_info:
            format_parts.append("%(threadName)s")
            
        if self.add_module_info:
            format_parts.append("%(name)s")
            
        if self.add_line_info:
            format_parts.append("%(filename)s:%(lineno)d")
            
        format_parts.append("%(message)s")
        
        # 創建格式化器
        return logging.Formatter(" - ".join(format_parts))
        
    def _create_json_formatter(self) -> logging.Formatter:
        """創建 JSON 格式化器
        
        Returns:
            logging.Formatter: JSON 格式化器
        """
        class JsonFormatter(logging.Formatter):
            def format(self, record):
                log_data = {
                    "timestamp": self.formatTime(record, "%Y-%m-%d %H:%M:%S,%f")[:-3],
                    "level": record.levelname,
                    "message": record.getMessage()
                }
                
                # 添加額外信息
                if self.add_process_info:
                    log_data["process"] = record.process
                    
                if self.add_thread_info:
                    log_data["thread"] = record.threadName
                    
                if self.add_module_info:
                    log_data["module"] = record.name
                    
                if self.add_line_info:
                    log_data["file"] = record.filename
                    log_data["line"] = record.lineno
                    
                # 添加異常信息
                if record.exc_info:
                    log_data["exception"] = self.formatException(record.exc_info)
                    
                return json.dumps(log_data)
                
        return JsonFormatter()
        
    def debug(self, message: str, *args, **kwargs):
        """記錄調試信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.debug(message, *args, **kwargs)
        self._add_to_cache("DEBUG", message)
        
    def info(self, message: str, *args, **kwargs):
        """記錄信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.info(message, *args, **kwargs)
        self._add_to_cache("INFO", message)
        
    def warning(self, message: str, *args, **kwargs):
        """記錄警告信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.warning(message, *args, **kwargs)
        self._add_to_cache("WARNING", message)
        
    def error(self, message: str, *args, **kwargs):
        """記錄錯誤信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.error(message, *args, **kwargs)
        self._add_to_cache("ERROR", message)
        
    def critical(self, message: str, *args, **kwargs):
        """記錄嚴重錯誤信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.critical(message, *args, **kwargs)
        self._add_to_cache("CRITICAL", message)
        
    def exception(self, message: str, *args, **kwargs):
        """記錄異常信息
        
        Args:
            message: 日誌信息
            *args: 位置參數
            **kwargs: 關鍵字參數
        """
        self.logger.exception(message, *args, **kwargs)
        
        # 獲取異常信息
        exc_info = sys.exc_info()
        if exc_info[0] is not None:
            exc_message = f"{message}: {exc_info[1]}"
            self._add_to_cache("ERROR", exc_message)
            
    def _add_to_cache(self, level: str, message: str):
        """添加到緩存
        
        Args:
            level: 日誌級別
            message: 日誌信息
        """
        with self.cache_lock:
            # 添加到緩存
            self.log_cache.append({
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "level": level,
                "message": message
            })
            
            # 限制緩存大小
            if len(self.log_cache) > self.max_cache_size:
                self.log_cache = self.log_cache[-self.max_cache_size:]
                
    def get_logs(self, level: Optional[str] = None, count: int = 100) -> List[Dict[str, Any]]:
        """獲取日誌
        
        Args:
            level: 日誌級別
            count: 日誌數量
            
        Returns:
            List[Dict[str, Any]]: 日誌列表
        """
        with self.cache_lock:
            # 過濾日誌
            if level:
                logs = [log for log in self.log_cache if log["level"] == level]
            else:
                logs = self.log_cache.copy()
                
            # 限制數量
            return logs[-count:]
            
    def export_logs(self, file_path: str, level: Optional[str] = None) -> bool:
        """導出日誌
        
        Args:
            file_path: 文件路徑
            level: 日誌級別
            
        Returns:
            bool: 是否成功
        """
        try:
            # 獲取日誌
            logs = self.get_logs(level, count=self.max_cache_size)
            
            # 導出日誌
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            self.error(f"導出日誌失敗: {e}")
            return False
            
    def clear_cache(self):
        """清除緩存"""
        with self.cache_lock:
            self.log_cache = []
            
    def set_level(self, level: int):
        """設置日誌級別
        
        Args:
            level: 日誌級別
        """
        self.level = level
        self.logger.setLevel(level)
        
        # 更新處理器級別
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) or isinstance(handler, RotatingFileHandler) or isinstance(handler, TimedRotatingFileHandler):
                handler.setLevel(level)
                
    def get_logger(self) -> logging.Logger:
        """獲取日誌記錄器
        
        Returns:
            logging.Logger: 日誌記錄器
        """
        return self.logger
        
    def get_log_files(self) -> List[str]:
        """獲取日誌文件
        
        Returns:
            List[str]: 日誌文件列表
        """
        try:
            # 獲取日誌文件
            log_files = []
            for file in os.listdir(self.log_dir):
                if file.startswith(self.name) and file.endswith(".log"):
                    log_files.append(os.path.join(self.log_dir, file))
                    
            return log_files
            
        except Exception as e:
            self.error(f"獲取日誌文件失敗: {e}")
            return []
            
    def get_log_file_content(self, file_path: str, max_lines: int = 1000) -> str:
        """獲取日誌文件內容
        
        Args:
            file_path: 文件路徑
            max_lines: 最大行數
            
        Returns:
            str: 日誌文件內容
        """
        try:
            # 檢查文件是否存在
            if not os.path.exists(file_path):
                return f"文件不存在: {file_path}"
                
            # 讀取文件內容
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
                
            # 限制行數
            if len(lines) > max_lines:
                lines = lines[-max_lines:]
                
            return "".join(lines)
            
        except Exception as e:
            self.error(f"獲取日誌文件內容失敗: {e}")
            return f"獲取日誌文件內容失敗: {e}"
            
    def rotate_logs(self):
        """輪換日誌文件"""
        try:
            # 輪換日誌文件
            for handler in self.logger.handlers:
                if isinstance(handler, RotatingFileHandler) or isinstance(handler, TimedRotatingFileHandler):
                    handler.doRollover()
                    
        except Exception as e:
            self.error(f"輪換日誌文件失敗: {e}")
            
    def log_system_info(self):
        """記錄系統信息"""
        try:
            import platform
            import psutil
            
            # 記錄系統信息
            self.info(f"系統信息: {platform.system()} {platform.release()} {platform.version()}")
            self.info(f"Python 版本: {platform.python_version()}")
            self.info(f"處理器: {platform.processor()}")
            
            # 記錄內存信息
            memory = psutil.virtual_memory()
            self.info(f"內存: 總計 {memory.total / (1024 * 1024):.2f} MB, 可用 {memory.available / (1024 * 1024):.2f} MB, 使用率 {memory.percent}%")
            
            # 記錄磁盤信息
            disk = psutil.disk_usage("/")
            self.info(f"磁盤: 總計 {disk.total / (1024 * 1024 * 1024):.2f} GB, 可用 {disk.free / (1024 * 1024 * 1024):.2f} GB, 使用率 {disk.percent}%")
            
        except Exception as e:
            self.error(f"記錄系統信息失敗: {e}")
            
    def log_exception_with_traceback(self, message: str, exc_info=None):
        """記錄帶有堆棧跟踪的異常信息
        
        Args:
            message: 日誌信息
            exc_info: 異常信息
        """
        if exc_info is None:
            exc_info = sys.exc_info()
            
        if exc_info[0] is not None:
            # 獲取堆棧跟踪
            tb_str = "".join(traceback.format_exception(*exc_info))
            
            # 記錄異常信息
            self.error(f"{message}\n{tb_str}")
        else:
            self.error(message)
            
    def log_function_call(self, func_name: str, args: tuple, kwargs: dict):
        """記錄函數調用
        
        Args:
            func_name: 函數名稱
            args: 位置參數
            kwargs: 關鍵字參數
        """
        # 格式化參數
        args_str = ", ".join([str(arg) for arg in args])
        kwargs_str = ", ".join([f"{key}={value}" for key, value in kwargs.items()])
        
        # 記錄函數調用
        if args and kwargs:
            self.debug(f"調用函數 {func_name}({args_str}, {kwargs_str})")
        elif args:
            self.debug(f"調用函數 {func_name}({args_str})")
        elif kwargs:
            self.debug(f"調用函數 {func_name}({kwargs_str})")
        else:
            self.debug(f"調用函數 {func_name}()")
            
    def log_function_result(self, func_name: str, result: Any):
        """記錄函數結果
        
        Args:
            func_name: 函數名稱
            result: 函數結果
        """
        # 記錄函數結果
        self.debug(f"函數 {func_name} 返回: {result}")
        
    def log_api_request(self, method: str, url: str, headers: Optional[Dict[str, str]] = None, data: Any = None):
        """記錄 API 請求
        
        Args:
            method: 請求方法
            url: 請求 URL
            headers: 請求頭
            data: 請求數據
        """
        # 記錄 API 請求
        self.info(f"API 請求: {method} {url}")
        
        if headers:
            # 過濾敏感信息
            filtered_headers = headers.copy()
            for key in ["Authorization", "Cookie", "Token"]:
                if key in filtered_headers:
                    filtered_headers[key] = "***"
                    
            self.debug(f"請求頭: {filtered_headers}")
            
        if data:
            self.debug(f"請求數據: {data}")
            
    def log_api_response(self, url: str, status_code: int, response_data: Any):
        """記錄 API 響應
        
        Args:
            url: 請求 URL
            status_code: 狀態碼
            response_data: 響應數據
        """
        # 記錄 API 響應
        self.info(f"API 響應: {url} - {status_code}")
        self.debug(f"響應數據: {response_data}")
        
    def log_api_error(self, url: str, status_code: int, error_message: str):
        """記錄 API 錯誤
        
        Args:
            url: 請求 URL
            status_code: 狀態碼
            error_message: 錯誤信息
        """
        # 記錄 API 錯誤
        self.error(f"API 錯誤: {url} - {status_code} - {error_message}")
        
    def log_database_query(self, query: str, params: Optional[Dict[str, Any]] = None):
        """記錄數據庫查詢
        
        Args:
            query: 查詢語句
            params: 查詢參數
        """
        # 記錄數據庫查詢
        self.debug(f"數據庫查詢: {query}")
        
        if params:
            self.debug(f"查詢參數: {params}")
            
    def log_database_result(self, query: str, result: Any):
        """記錄數據庫結果
        
        Args:
            query: 查詢語句
            result: 查詢結果
        """
        # 記錄數據庫結果
        self.debug(f"數據庫結果: {query} - {result}")
        
    def log_database_error(self, query: str, error_message: str):
        """記錄數據庫錯誤
        
        Args:
            query: 查詢語句
            error_message: 錯誤信息
        """
        # 記錄數據庫錯誤
        self.error(f"數據庫錯誤: {query} - {error_message}")
        
    def log_file_operation(self, operation: str, file_path: str):
        """記錄文件操作
        
        Args:
            operation: 操作類型
            file_path: 文件路徑
        """
        # 記錄文件操作
        self.debug(f"文件操作: {operation} - {file_path}")
        
    def log_file_error(self, operation: str, file_path: str, error_message: str):
        """記錄文件錯誤
        
        Args:
            operation: 操作類型
            file_path: 文件路徑
            error_message: 錯誤信息
        """
        # 記錄文件錯誤
        self.error(f"文件錯誤: {operation} - {file_path} - {error_message}")
        
    def log_ui_event(self, event_type: str, widget_name: str, event_data: Optional[Dict[str, Any]] = None):
        """記錄 UI 事件
        
        Args:
            event_type: 事件類型
            widget_name: 元件名稱
            event_data: 事件數據
        """
        # 記錄 UI 事件
        self.debug(f"UI 事件: {event_type} - {widget_name}")
        
        if event_data:
            self.debug(f"事件數據: {event_data}")
            
    def log_ui_error(self, event_type: str, widget_name: str, error_message: str):
        """記錄 UI 錯誤
        
        Args:
            event_type: 事件類型
            widget_name: 元件名稱
            error_message: 錯誤信息
        """
        # 記錄 UI 錯誤
        self.error(f"UI 錯誤: {event_type} - {widget_name} - {error_message}")
        
    def log_performance(self, operation: str, start_time: float, end_time: float):
        """記錄性能
        
        Args:
            operation: 操作名稱
            start_time: 開始時間
            end_time: 結束時間
        """
        # 計算耗時
        elapsed_time = end_time - start_time
        
        # 記錄性能
        self.debug(f"性能: {operation} - {elapsed_time:.6f} 秒")
        
    def log_memory_usage(self):
        """記錄內存使用"""
        try:
            import psutil
            
            # 獲取進程
            process = psutil.Process(os.getpid())
            
            # 獲取內存使用
            memory_info = process.memory_info()
            memory_usage = memory_info.rss / (1024 * 1024)
            
            # 記錄內存使用
            self.debug(f"內存使用: {memory_usage:.2f} MB")
            
        except Exception as e:
            self.error(f"記錄內存使用失敗: {e}")
            
    def log_thread_info(self):
        """記錄線程信息"""
        try:
            # 獲取當前線程
            current_thread = threading.current_thread()
            
            # 記錄線程信息
            self.debug(f"線程信息: {current_thread.name} (ID: {current_thread.ident})")
            
        except Exception as e:
            self.error(f"記錄線程信息失敗: {e}")
            
    def log_process_info(self):
        """記錄進程信息"""
        try:
            import psutil
            
            # 獲取進程
            process = psutil.Process(os.getpid())
            
            # 記錄進程信息
            self.debug(f"進程信息: {process.name()} (PID: {process.pid})")
            
        except Exception as e:
            self.error(f"記錄進程信息失敗: {e}")

# 創建全局日誌記錄器實例
enhanced_logger = EnhancedLogger()
