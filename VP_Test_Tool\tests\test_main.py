"""主程式測試"""
import sys
import os
import unittest
from unittest.mock import MagicMock, patch

# 將專案根目錄加入 sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config import Config
from utils.http_client import HttpClient
from models.member import MemberService, Member
from models.agent import AgentService, Agent, SubAgent

class TestConfig(unittest.TestCase):
    """設定檔測試"""
    def test_singleton(self):
        """測試單例模式"""
        config1 = Config()
        config2 = Config()
        self.assertIs(config1, config2)
        
    def test_get_set(self):
        """測試 get 和 set 方法"""
        config = Config()
        config.set("test_key", "test_value")
        self.assertEqual(config.get("test_key"), "test_value")
        
class TestHttpClient(unittest.TestCase):
    """HTTP 客戶端測試"""
    @patch('requests.Session')
    def test_init(self, mock_session):
        """測試初始化"""
        client = HttpClient("http://test.com", 30)
        self.assertEqual(client.base_url, "http://test.com")
        self.assertEqual(client.timeout, 30)
        
class TestMemberService(unittest.TestCase):
    """會員服務測試"""
    def test_generate_random_account(self):
        """測試生成隨機帳號"""
        http_client = MagicMock()
        service = MemberService(http_client)
        account = service.generate_random_account(12)
        self.assertEqual(len(account), 12)
        
    def test_generate_batch_accounts(self):
        """測試批次生成隨機帳號"""
        http_client = MagicMock()
        service = MemberService(http_client)
        accounts = service.generate_batch_accounts(5, 12)
        self.assertEqual(len(accounts), 5)
        for account in accounts:
            self.assertEqual(len(account), 12)
        
class TestAgentService(unittest.TestCase):
    """代理商服務測試"""
    def test_validate_agent_code(self):
        """測試驗證代理商代碼"""
        http_client = MagicMock()
        service = AgentService(http_client)
        self.assertTrue(service.validate_agent_code("test"))
        self.assertFalse(service.validate_agent_code(""))
        self.assertFalse(service.validate_agent_code("  "))
        
if __name__ == '__main__':
    unittest.main()
