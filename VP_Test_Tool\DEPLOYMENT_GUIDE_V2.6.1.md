# VP Test Tool V2.6.1 部署指南

## 📦 部署概述

VP Test Tool V2.6.1 已成功打包為獨立的 Windows 可執行文件，可在目標系統上直接運行，無需安裝 Python 環境。

**版本**: V2.6.1  
**打包日期**: 2025-05-28  
**打包工具**: cx_Freeze  
**目標平台**: Windows 10/11 (x64)  

---

## 📁 部署文件

### 🎯 主要文件
```
dist/cx_freeze/
├── VP_Test_Tool.exe          # 主程式 (35 KB)
├── python311.dll             # Python 運行時 (5.5 MB)
├── config.json               # 配置文件
├── CHANGELOG.md              # 更新日誌
├── assets/                   # 資源文件目錄
│   ├── icons/
│   │   └── vp_test_tool.ico  # 應用程式圖示
│   ├── app_icon.ico          # 備用圖示
│   └── app_icon.png          # PNG 圖示
├── lib/                      # Python 庫目錄 (2,300+ 文件)
│   ├── tkinter/              # GUI 框架
│   ├── PIL/                  # 圖像處理
│   ├── requests/             # HTTP 請求
│   ├── pandas/               # 數據處理
│   ├── numpy/                # 數值計算
│   ├── openpyxl/             # Excel 處理
│   ├── utils/                # 工具模組
│   ├── views/                # 視圖模組
│   ├── controllers/          # 控制器模組
│   ├── widgets/              # 自訂元件
│   └── models/               # 模型模組
├── share/                    # 共享資源 (1,600+ 文件)
└── *.dll                     # 運行時庫 (20+ 個)
```

### 📊 統計信息
- **總文件數**: 3,761 個
- **總大小**: 119.2 MB
- **主程式**: 34.5 KB
- **依賴庫**: 60+ 個模組

---

## 🚀 部署步驟

### 1. 準備部署包
```bash
# 複製整個打包目錄
cp -r dist/cx_freeze/ /path/to/deployment/VP_Test_Tool_V2.6.1/
```

### 2. 驗證部署包
- 確認所有文件完整
- 檢查主程式可執行權限
- 驗證配置文件存在

### 3. 分發方式

#### 方式一：ZIP 壓縮包
```bash
# 創建分發包
cd dist/
zip -r VP_Test_Tool_V2.6.1.zip cx_freeze/
```

#### 方式二：安裝程式
- 使用 NSIS 或 Inno Setup 創建安裝程式
- 包含自動創建桌面快捷方式
- 添加卸載功能

#### 方式三：綠色版
- 直接複製 cx_freeze 目錄
- 重命名為 VP_Test_Tool_V2.6.1
- 提供使用說明

---

## 💻 系統需求

### 最低需求
- **作業系統**: Windows 10 (1903) 或更新版本
- **架構**: x64 (64位元)
- **記憶體**: 4 GB RAM
- **磁碟空間**: 200 MB 可用空間
- **網路**: 需要網路連接使用 API 功能

### 建議需求
- **作業系統**: Windows 11
- **記憶體**: 8 GB RAM 或更多
- **磁碟空間**: 500 MB 可用空間
- **顯示器**: 1920x1080 或更高解析度

### 依賴項
- **Visual C++ 運行時**: 已包含在部署包中
- **Windows Defender**: 建議加入白名單
- **防毒軟體**: 可能需要例外設定

---

## 🔧 安裝指南

### 用戶安裝步驟

1. **下載部署包**
   - 從指定位置下載 VP_Test_Tool_V2.6.1.zip
   - 驗證文件完整性

2. **解壓縮**
   ```
   解壓縮到目標目錄，例如：
   C:\Program Files\VP_Test_Tool\
   或
   C:\Users\<USER>\VP_Test_Tool\
   ```

3. **首次運行**
   - 雙擊 `VP_Test_Tool.exe`
   - 等待程式載入（首次可能較慢）
   - 確認所有功能正常

4. **創建快捷方式**（可選）
   - 右鍵點擊 `VP_Test_Tool.exe`
   - 選擇「創建快捷方式」
   - 移動到桌面或開始選單

---

## ⚙️ 配置說明

### config.json 配置
```json
{
  "api": {
    "base_url": "http://localhost:8080",
    "timeout": 30
  },
  "ui": {
    "theme": "default",
    "font_size": 10,
    "window_width": 1085,
    "window_height": 985
  },
  "log": {
    "level": "INFO",
    "file": "app.log"
  }
}
```

### 環境配置
- 程式會自動創建 `environments.json`
- IP 模板會自動創建 `ip_templates.json`
- 日誌文件會在 `logs/` 目錄下創建

---

## 🛡️ 安全考量

### 防毒軟體設定
某些防毒軟體可能會誤判 cx_Freeze 打包的程式：

1. **Windows Defender**
   ```
   設定 > 更新與安全性 > Windows 安全性 > 病毒與威脅防護
   > 病毒與威脅防護設定 > 新增或移除排除項目
   > 新增排除項目 > 資料夾 > 選擇 VP_Test_Tool 目錄
   ```

2. **其他防毒軟體**
   - 將 VP_Test_Tool.exe 加入白名單
   - 將整個程式目錄設為信任區域

### 網路安全
- 程式需要存取特定 API 端點
- 確保防火牆允許程式網路存取
- 檢查代理伺服器設定

---

## 🔍 故障排除

### 常見問題

#### 1. 程式無法啟動
**症狀**: 雙擊程式無反應或立即關閉  
**解決方案**:
- 檢查 Visual C++ 運行時是否安裝
- 確認所有 DLL 文件完整
- 以管理員權限運行
- 檢查防毒軟體是否阻擋

#### 2. 功能異常
**症狀**: 某些功能無法使用  
**解決方案**:
- 檢查網路連接
- 確認 API 端點可存取
- 檢查 config.json 配置
- 查看日誌文件錯誤訊息

#### 3. 介面顯示問題
**症狀**: 介面元素顯示異常  
**解決方案**:
- 檢查顯示器解析度設定
- 調整系統 DPI 設定
- 確認字型設定正確

#### 4. 效能問題
**症狀**: 程式運行緩慢  
**解決方案**:
- 確保有足夠記憶體
- 關閉不必要的背景程式
- 檢查磁碟空間
- 更新顯示卡驅動程式

---

## 📞 技術支援

### 日誌收集
遇到問題時，請收集以下資訊：
- `logs/app.log` - 應用程式日誌
- `logs/app_error.log` - 錯誤日誌
- 系統資訊（作業系統版本、記憶體等）
- 錯誤截圖

### 聯繫方式
- **技術支援**: VP Test Tool 開發團隊
- **問題回報**: 提供詳細的錯誤描述和日誌
- **功能建議**: 歡迎提供改進建議

---

## 🔄 更新說明

### 更新流程
1. 備份當前配置文件
2. 下載新版本部署包
3. 解壓縮覆蓋舊版本
4. 恢復配置文件
5. 測試功能正常

### 版本相容性
- 配置文件向後相容
- 環境設定自動遷移
- 建議定期備份重要配置

---

## 📋 檢查清單

### 部署前檢查
- [ ] 打包文件完整性驗證
- [ ] 目標系統需求確認
- [ ] 網路環境測試
- [ ] 防毒軟體設定

### 部署後檢查
- [ ] 程式正常啟動
- [ ] 所有功能可用
- [ ] 配置文件正確
- [ ] 日誌記錄正常

---

**VP Test Tool 開發團隊**  
**2025年5月28日**
