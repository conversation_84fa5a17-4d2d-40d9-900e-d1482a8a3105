"""FileManager 單元測試"""
import unittest
import os
import sys
import tempfile
import json
import csv
from unittest.mock import patch, mock_open

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.file_manager import FileManager
from utils.exceptions import FileReadError, FileWriteError, FileFormatError

class TestFileManager(unittest.TestCase):
    """FileManager 單元測試類"""

    def setUp(self):
        """測試前準備"""
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        self.test_file_path = os.path.join(self.temp_dir, "test.txt")
        self.test_json_path = os.path.join(self.temp_dir, "test.json")
        self.test_csv_path = os.path.join(self.temp_dir, "test.csv")

    def tearDown(self):
        """測試後清理"""
        # 刪除臨時目錄及其內容
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_read_write_text_file(self):
        """測試讀寫文本文件"""
        # 寫入文件
        test_content = "Hello, World!"
        FileManager.write_text_file(self.test_file_path, test_content)

        # 讀取文件
        content = FileManager.read_text_file(self.test_file_path)

        # 驗證結果
        self.assertEqual(content, test_content)
        self.assertTrue(os.path.exists(self.test_file_path))

    def test_read_nonexistent_file(self):
        """測試讀取不存在的文件"""
        nonexistent_path = os.path.join(self.temp_dir, "nonexistent.txt")
        
        # 應該拋出 FileReadError
        with self.assertRaises(FileReadError):
            FileManager.read_text_file(nonexistent_path)

    def test_read_write_binary_file(self):
        """測試讀寫二進制文件"""
        # 寫入文件
        test_content = b"Binary Data"
        FileManager.write_binary_file(self.test_file_path, test_content)

        # 讀取文件
        content = FileManager.read_binary_file(self.test_file_path)

        # 驗證結果
        self.assertEqual(content, test_content)
        self.assertTrue(os.path.exists(self.test_file_path))

    def test_read_write_json_file(self):
        """測試讀寫 JSON 文件"""
        # 寫入文件
        test_data = {"name": "test", "value": 123}
        FileManager.write_json_file(self.test_json_path, test_data)

        # 讀取文件
        data = FileManager.read_json_file(self.test_json_path)

        # 驗證結果
        self.assertEqual(data, test_data)
        self.assertTrue(os.path.exists(self.test_json_path))

    def test_read_invalid_json_file(self):
        """測試讀取無效的 JSON 文件"""
        # 寫入無效的 JSON
        with open(self.test_json_path, 'w') as f:
            f.write("Invalid JSON")

        # 應該拋出 FileFormatError
        with self.assertRaises(FileFormatError):
            FileManager.read_json_file(self.test_json_path)

    def test_read_write_csv_file(self):
        """測試讀寫 CSV 文件"""
        # 寫入文件
        test_data = [
            {"name": "Alice", "age": "30"},
            {"name": "Bob", "age": "25"}
        ]
        FileManager.write_csv_file(self.test_csv_path, test_data)

        # 讀取文件
        data = FileManager.read_csv_file(self.test_csv_path)

        # 驗證結果
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]["name"], "Alice")
        self.assertEqual(data[1]["age"], "25")
        self.assertTrue(os.path.exists(self.test_csv_path))

    def test_read_invalid_csv_file(self):
        """測試讀取無效的 CSV 文件"""
        # 寫入無效的 CSV
        with open(self.test_csv_path, 'w') as f:
            f.write("name,age\nAlice,30\nBob,")  # 缺少值

        # 應該能夠讀取，但最後一行的 age 為空
        data = FileManager.read_csv_file(self.test_csv_path)
        self.assertEqual(len(data), 2)
        self.assertEqual(data[1]["age"], "")

    def test_ensure_directory(self):
        """測試確保目錄存在"""
        # 創建新目錄
        new_dir = os.path.join(self.temp_dir, "new_dir")
        FileManager.ensure_directory(new_dir)

        # 驗證結果
        self.assertTrue(os.path.exists(new_dir))
        self.assertTrue(os.path.isdir(new_dir))

        # 再次調用不應該拋出異常
        FileManager.ensure_directory(new_dir)

    def test_file_exists(self):
        """測試檢查文件是否存在"""
        # 創建文件
        with open(self.test_file_path, 'w') as f:
            f.write("Test")

        # 驗證結果
        self.assertTrue(FileManager.file_exists(self.test_file_path))
        self.assertFalse(FileManager.file_exists(os.path.join(self.temp_dir, "nonexistent.txt")))

    def test_directory_exists(self):
        """測試檢查目錄是否存在"""
        # 創建目錄
        new_dir = os.path.join(self.temp_dir, "new_dir")
        os.makedirs(new_dir)

        # 驗證結果
        self.assertTrue(FileManager.directory_exists(new_dir))
        self.assertFalse(FileManager.directory_exists(os.path.join(self.temp_dir, "nonexistent_dir")))

    def test_list_files(self):
        """測試列出目錄中的文件"""
        # 創建文件
        file1 = os.path.join(self.temp_dir, "file1.txt")
        file2 = os.path.join(self.temp_dir, "file2.txt")
        with open(file1, 'w') as f:
            f.write("File 1")
        with open(file2, 'w') as f:
            f.write("File 2")

        # 列出文件
        files = FileManager.list_files(self.temp_dir, "*.txt")

        # 驗證結果
        self.assertEqual(len(files), 2)
        self.assertTrue(file1 in files or os.path.abspath(file1) in files)
        self.assertTrue(file2 in files or os.path.abspath(file2) in files)

    def test_remove_file(self):
        """測試刪除文件"""
        # 創建文件
        with open(self.test_file_path, 'w') as f:
            f.write("Test")

        # 刪除文件
        FileManager.remove_file(self.test_file_path)

        # 驗證結果
        self.assertFalse(os.path.exists(self.test_file_path))

    def test_remove_directory(self):
        """測試刪除目錄"""
        # 創建目錄
        new_dir = os.path.join(self.temp_dir, "new_dir")
        os.makedirs(new_dir)

        # 刪除目錄
        FileManager.remove_directory(new_dir)

        # 驗證結果
        self.assertFalse(os.path.exists(new_dir))

    def test_temp_file(self):
        """測試臨時文件"""
        # 使用臨時文件
        with FileManager.temp_file(content="Test Content") as temp_file:
            # 驗證文件存在且內容正確
            temp_file.seek(0)
            content = temp_file.read()
            self.assertEqual(content, "Test Content")
            
            # 獲取文件路徑
            temp_path = temp_file.name
            self.assertTrue(os.path.exists(temp_path))
        
        # with 語句結束後，文件應該被刪除
        self.assertFalse(os.path.exists(temp_path))

    def test_temp_directory(self):
        """測試臨時目錄"""
        # 使用臨時目錄
        with FileManager.temp_directory() as temp_dir:
            # 驗證目錄存在
            self.assertTrue(os.path.exists(temp_dir))
            self.assertTrue(os.path.isdir(temp_dir))
            
            # 在臨時目錄中創建文件
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("Test")
            self.assertTrue(os.path.exists(test_file))
        
        # with 語句結束後，目錄應該被刪除
        self.assertFalse(os.path.exists(temp_dir))

if __name__ == '__main__':
    unittest.main()
