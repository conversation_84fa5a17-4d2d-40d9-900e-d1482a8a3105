"""帳號產生器控制器"""
import logging
import tkinter as tk
# messagebox 已移除，因為未使用
import csv
import os
import datetime
import time
import subprocess
import sys
import queue
import threading
import concurrent.futures
from functools import partial
from typing import List, Tuple
from models.member import MemberService
from models.agent import AgentService
from views.account_panel import AccountPanel

logger = logging.getLogger(__name__)

class AccountController:
    """帳號產生器控制器"""
    def __init__(self, account_panel: AccountPanel, member_service: MemberService, agent_service: AgentService):
        self.view = account_panel
        self.member_service = member_service
        self.agent_service = agent_service
        self.last_output_path = None  # 儲存最後輸出的路徑
        self.sub_agent_map = {}  # 子代理商顯示名稱對應代碼的字典
        self.sub_agents = []  # 子代理商列表

        # 用於多線程處理
        self.log_queue = queue.Queue()  # 用於存放日誌訊息
        self.ui_queue = queue.Queue()   # 用於存放 UI 更新操作
        self.is_processing = False      # 批次處理狀態標記
        self.root = account_panel.master  # 獲取根窗口引用，用於 after 方法
        self.stop_event = threading.Event()  # 用於停止線程的事件

        self._init_bindings()
        self._init_data()

        # 啟動 UI 更新定時器
        self._start_ui_updater()

    def _init_bindings(self):
        """初始化事件綁定"""
        # 重置預設值按鈕事件
        self.view.btn_reset.config(command=self.reset_defaults)
        # 清空參數按鈕事件
        self.view.btn_clear.config(command=self.clear_params)
        # 產生按鈕事件
        self.view.btn_generate.config(command=self.start_generation)
        # 下載帳號列表按鈕事件
        self.view.btn_download.config(command=self.download_accounts)
        # 清除日誌按鈕事件
        self.view.btn_clear_log.config(command=self.view.clear_log)
        # 代理商選擇事件
        self.view.cb_agent_code.bind('<<ComboboxSelected>>',
                                    self._handle_agent_selected)
        # 移除幣別選擇事件
        # 子代理商選擇事件
        self.view.cb_sub_agent_code.bind('<<ComboboxSelected>>',
                                       self._handle_sub_agent_selected)

    def _init_data(self):
        """初始化下拉選單資料"""
        try:
            # 記錄操作到日誌
            self.view.log_message("-----------------------------------------------------------------", "info")
            self.view.log_message("程式啟動，正在初始化資料...", "info")

            # 載入代理商列表
            self.view.log_message("正在載入代理商列表...", "info")

            # 載入 API 請求到日誌
            from utils.constants import API_URLS
            self.view.log_message("httpMethod: POST", "info")
            self.view.log_message(f"url: {API_URLS['QUERY_AGENTS']}", "info")
            self.view.log_message("", "info")
            self.view.log_message("content-type: json", "info")
            self.view.log_message("", "info")
            self.view.log_message("Body", "info")
            self.view.log_message("{}", "info")

            # 查詢代理商列表
            agents = self.agent_service.query_all_agents()
            agent_codes = [a.agent_code for a in agents]
            self.view.cb_agent_code['values'] = agent_codes

            # 記錄 API 回應內容
            self.view.log_message("", "info")
            self.view.log_message("回應狀態碼: 200 OK", "info")
            self.view.log_message("回應內容:", "info")

            # 記錄實際的 API 回應內容
            self.view.log_message("已成功載入代理商列表", "success")
            self.view.log_message(f"共載入 {len(agents)} 個代理商", "info")

            # 移除幣別列表載入

            # 設定預設值為 Jmeter
            default_agent_code = "Jmeter"

            # 確保下載按鈕為禁用狀態
            self.view.disable_download_button()

            # 啟動創建帳號按鈕的閃爍效果
            self.view.start_flash()

            # 使用延遲設定預設值，確保 UI 已經完全載入
            self.view.after(500, lambda: self._set_default_values(default_agent_code, agent_codes))

        except Exception as e:
            logger.error(f"初始化資料失敗: {e}")
            self.view.log_message(f"初始化資料失敗: {str(e)}", "error")

    def _set_default_values(self, default_agent_code, agent_codes):
        """設定預設值

        Args:
            default_agent_code: 預設代理商代碼
            agent_codes: 代理商代碼列表
        """
        try:
            # 記錄操作到日誌
            self.view.log_message("-----------------------------------------------------------------", "info")
            self.view.log_message("正在設定預設值...", "info")
            self.view.log_message(f"預設代理商代碼: {default_agent_code}", "info")

            if default_agent_code in agent_codes:
                # 強制設定預設代理商
                self.view.cb_agent_code.set(default_agent_code)
                self.view.log_message(f"已設定預設代理商為: {default_agent_code}", "success")

                # 確保 UI 更新
                self.view.update()

                # 直接載入子代理商，使用延遲確保代理商已經設定
                self.view.after(100, lambda: self._load_sub_agents_by_agent_code(default_agent_code))
            else:
                self.view.log_message(f"找不到預設代理商 {default_agent_code}", "warning")
        except Exception as e:
            logger.error(f"設定預設值失敗: {e}")
            self.view.log_message(f"設定預設值失敗: {str(e)}", "error")

    def _handle_agent_selected(self, event=None):
        """處理代理商選擇事件

        Args:
            event: Tkinter 事件物件，預設為 None
        """
        try:
            # 使用 event 參數來確定是否是由事件觸發
            is_event_triggered = event is not None
            if is_event_triggered:
                self.view.log_message("已觸發代理商選擇事件", "info")
            agent_code = self.view.cb_agent_code.get()
            if not agent_code:
                return

            # 直接載入子代理商
            self._load_sub_agents_by_agent_code(agent_code)

        except Exception as e:
            logger.error(f"載入子代理商失敗: {e}")
            self.view.log_message(f"載入子代理商失敗: {str(e)}", "error")

    def _load_sub_agents_by_agent_code(self, agent_code: str):
        """根據代理商代碼載入子代理商和幣別

        Args:
            agent_code: 代理商代碼
        """
        try:
            # 載入 API 請求到日誌
            from utils.constants import API_URLS
            self.view.log_message("-----------------------------------------------------------------", "info")
            self.view.log_message("httpMethod: POST ", "info")
            self.view.log_message(f"url: {API_URLS['QUERY_SUB_AGENTS']}", "info")
            self.view.log_message("", "info")
            self.view.log_message("content-type: json", "info")
            self.view.log_message("", "info")
            self.view.log_message("Body", "info")
            self.view.log_message("{", "info")
            self.view.log_message(f'    "AgentCode": "{agent_code}" ', "info")
            self.view.log_message("}", "info")

            # 查詢子代理商
            self.sub_agents = self.agent_service.query_sub_agents_by_agent_code(agent_code)
            sub_agents = self.sub_agents  # 為了保持原有代碼的相容性

            # 記錄 API 回應內容
            self.view.log_message("", "info")
            self.view.log_message("回應狀態碼: 200 OK", "info")
            self.view.log_message("回應內容:", "info")

            # 記錄實際的 API 回應內容
            self.view.log_message("已成功載入子代理商列表", "success")
            self.view.log_message(f"共載入 {len(sub_agents)} 個子代理商", "info")

            # 記錄日誌
            self.view.log_message(f"已選擇代理商: {agent_code}", "info")
            self.view.log_message(f"已載入 {len(sub_agents)} 個子代理商", "info")

            # 設定子代理商下拉選單，使用 display_name 屬性
            sub_agent_display_names = [sa.display_name for sa in sub_agents]
            sub_agent_codes = [sa.code for sa in sub_agents]  # 保存原始代碼以便後續使用

            # 將顯示名稱和代碼對應起來
            self.sub_agent_map = dict(zip(sub_agent_display_names, sub_agent_codes))

            # 設定下拉選單的值
            self.view.cb_sub_agent_code['values'] = sub_agent_display_names

            # 如果有子代理商，選擇第一個
            if sub_agents and len(sub_agents) > 0:
                # 使用延遲確保子代理商列表已經載入
                def _set_sub_agent_and_currency():
                    # 設定子代理商
                    display_name = sub_agents[0].display_name
                    self.view.cb_sub_agent_code.set(display_name)
                    self.view.log_message(f"已設定子代理商為: {display_name}", "success")

                    # 確保 UI 更新
                    self.view.update()

                # 使用延遲執行設定
                self.view.after(200, _set_sub_agent_and_currency)

        except Exception as e:
            logger.error(f"根據代理商代碼載入子代理商失敗: {e}")
            self.view.log_message(f"根據代理商代碼載入子代理商失敗: {str(e)}", "error")

    # 移除 _handle_currency_selected 方法

    def _handle_sub_agent_selected(self, event=None):
        """處理子代理商選擇事件

        Args:
            event: Tkinter 事件物件，預設為 None
        """
        try:
            # 使用 event 參數來確定是否是由事件觸發
            is_event_triggered = event is not None
            if is_event_triggered:
                self.view.log_message("已觸發子代理商選擇事件", "info")
            sub_agent_display = self.view.cb_sub_agent_code.get()
            if not sub_agent_display:
                return

            # 從顯示名稱中取得子代理商代碼
            sub_agent_code = self.sub_agent_map.get(sub_agent_display, "") if hasattr(self, "sub_agent_map") else sub_agent_display.split(' - ')[0] if ' - ' in sub_agent_display else sub_agent_display

            # 記錄日誌
            agent_code = self.view.cb_agent_code.get()
            self.view.log_message(f"已選擇子代理商: {sub_agent_display} (代碼: {sub_agent_code}, 上級代理商: {agent_code})", "info")

        except Exception as e:
            logger.error(f"處理子代理商選擇失敗: {e}")
            self.view.log_message(f"處理子代理商選擇失敗: {str(e)}", "error")

    def reset_defaults(self):
        """重置預設值，回到程式初始狀態"""
        try:
            # 記錄操作到日誌
            self.view.log_message("-----------------------------------------------------------------", "info")
            self.view.log_message("正在重置為預設值...", "info")

            # 重置帳號數量
            self.view.entry_account_count.delete(0, tk.END)
            self.view.entry_account_count.insert(0, "1")
            self.view.log_message("已設定帳號數量為: 1", "info")

            # 取得代理商列表
            agent_codes = self.view.cb_agent_code['values']

            # 設定預設代理商為 Jmeter
            default_agent_code = "Jmeter"

            # 檢查 Jmeter 是否在代理商列表中
            if default_agent_code in agent_codes:
                # 設定代理商為 Jmeter
                self.view.cb_agent_code.set(default_agent_code)
                self.view.log_message(f"已設定代理商為: {default_agent_code}", "success")

                # 載入子代理商
                self._load_sub_agents_by_agent_code(default_agent_code)
            else:
                # 如果 Jmeter 不在列表中，清空代理商和子代理商
                self.view.cb_agent_code.set('')
                self.view.cb_sub_agent_code.set('')
                self.view.cb_sub_agent_code['values'] = []
                self.view.log_message(f"找不到預設代理商 {default_agent_code}，已清空代理商和子代理商", "warning")

            # 重置進度條和百分比標籤
            self.view.progress_bar["value"] = 0
            self.view.progress_percent.config(text="0%")

            # 禁用下載按鈕
            self.view.disable_download_button()

            self.view.log_message("已重置所有參數為預設值", "success")

        except Exception as e:
            logger.error(f"重置預設值失敗: {e}")
            self.view.log_message(f"重置預設值失敗: {str(e)}", "error")

    def clear_params(self):
        """清空參數"""
        try:
            # 記錄操作到日誌
            self.view.log_message("-----------------------------------------------------------------", "info")
            self.view.log_message("正在清空代理商參數...", "info")

            # 取得目前的代理商和子代理商值
            current_agent = self.view.cb_agent_code.get()
            current_sub_agent = self.view.cb_sub_agent_code.get()

            if current_agent:
                self.view.log_message(f"清空前的代理商: {current_agent}", "info")
            if current_sub_agent:
                self.view.log_message(f"清空前的子代理商: {current_sub_agent}", "info")

            # 清空代理商和子代理商
            self.view.cb_agent_code.set('')
            self.view.cb_sub_agent_code.set('')
            self.view.cb_sub_agent_code['values'] = []

            # 清空子代理商對應表
            self.sub_agent_map = {}

            # 禁用下載按鈕
            self.view.disable_download_button()

            self.view.log_message("已清空代理商和子代理商參數", "success")

        except Exception as e:
            logger.error(f"清空參數失敗: {e}")
            self.view.log_message(f"清空參數失敗: {str(e)}", "error")

    def download_accounts(self):
        """下載帳號列表"""
        try:
            if not self.last_output_path or not os.path.exists(self.last_output_path):
                self.view.log_message("請先產生帳號列表", "warning")
                return

            # 開啟輸出資料夾
            self.open_output_folder()

        except Exception as e:
            logger.error(f"下載帳號列表失敗: {e}")
            self.view.log_message(f"下載帳號列表失敗: {str(e)}", "error")

    def _start_ui_updater(self):
        """啟動 UI 更新定時器"""
        def process_ui_queue():
            # 檢查是否應該停止
            if self.stop_event.is_set():
                return

            # 從佇列中取出所有 UI 更新操作
            try:
                while True:
                    # 非阻塞方式獲取操作
                    operation = self.ui_queue.get_nowait()

                    try:
                        # 執行操作
                        operation()
                    except Exception as e:
                        logger.error(f"執行 UI 操作失敗: {e}")
                    finally:
                        # 標記任務完成
                        self.ui_queue.task_done()
            except queue.Empty:
                pass

            # 繼續定時更新，除非應該停止
            if not self.stop_event.is_set() and self.root and hasattr(self.root, 'after') and callable(self.root.after):
                try:
                    self.root.after(50, process_ui_queue)
                except Exception as e:
                    logger.error(f"設置 UI 更新定時器失敗: {e}")

        # 啟動定時器
        if self.root and hasattr(self.root, 'after') and callable(self.root.after):
            try:
                self.root.after(50, process_ui_queue)
            except Exception as e:
                logger.error(f"啟動 UI 更新定時器失敗: {e}")

    def _log_to_queue(self, message: str, level="info"):
        """將日誌訊息加入佇列，在主線程中顯示"""
        try:
            # 檢查是否應該停止
            if self.stop_event.is_set():
                return

            # 將日誌操作添加到 UI 隊列
            self.ui_queue.put(lambda m=message, l=level: self.view.log_message(m, l))
        except Exception as e:
            # 如果發生異常，記錄到日誌但不中斷執行
            logger.error(f"將日誌訊息加入佇列失敗: {e}")

    def _update_progress_safe(self, current, total):
        """安全地更新進度條"""
        try:
            # 檢查是否應該停止
            if self.stop_event.is_set():
                return

            # 將進度更新操作添加到 UI 隊列
            self.ui_queue.put(lambda c=current, t=total: self.view.update_progress(c, t))
        except Exception as e:
            # 如果發生異常，記錄到日誌但不中斷執行
            logger.error(f"將進度更新加入佇列失敗: {e}")

    def _enable_download_button_safe(self):
        """安全地啟用下載按鈕"""
        try:
            # 檢查是否應該停止
            if self.stop_event.is_set():
                return

            # 將啟用下載按鈕操作添加到 UI 隊列
            self.ui_queue.put(self.view.enable_download_button)
        except Exception as e:
            # 如果發生異常，記錄到日誌但不中斷執行
            logger.error(f"將啟用下載按鈕加入佇列失敗: {e}")

    def _start_flash_safe(self):
        """安全地啟動閃爍效果"""
        try:
            # 檢查是否應該停止
            if self.stop_event.is_set():
                return

            # 將啟動閃爍效果操作添加到 UI 隊列
            self.ui_queue.put(self.view.start_flash)
        except Exception as e:
            # 如果發生異常，記錄到日誌但不中斷執行
            logger.error(f"將啟動閃爍效果加入佇列失敗: {e}")

    def stop(self):
        """停止所有線程和定時器"""
        # 設置停止事件
        self.stop_event.set()

        # 清空隊列
        try:
            while not self.ui_queue.empty():
                self.ui_queue.get_nowait()
                self.ui_queue.task_done()
        except Exception:
            pass

    def start_generation(self):
        """開始產生帳號"""
        self.view.clear_log()
        try:
            # 如果已經在處理中，則不再執行
            if self.is_processing:
                return

            # 重置進度條和百分比標籤
            self.view.progress_bar["value"] = 0
            self.view.progress_percent.config(text="0%")

            # 禁用下載按鈕
            self.view.disable_download_button()
            self.view.log_message("開始產生帳號，下載按鈕已禁用", "info")

            # 驗證帳號數量
            account_count_str = self.view.entry_account_count.get().strip()
            if not account_count_str:
                self.view.log_message("請輸入 Account 數量", "error")
                return

            try:
                account_count = int(account_count_str)
                if not (1 <= account_count <= ********):
                    self.view.log_message("Account 數量必須介於 1 至 ********", "error")
                    return
            except ValueError:
                self.view.log_message("請輸入正確的 Account 數量（整數）", "error")
                return

            # 取得並驗證輸入參數
            agent_code = self.view.cb_agent_code.get().strip()
            sub_agent_display = self.view.cb_sub_agent_code.get().strip()

            # 從顯示名稱中取得子代理商代碼
            sub_agent_code = self.sub_agent_map.get(sub_agent_display, "") if hasattr(self, "sub_agent_map") else sub_agent_display.split(' - ')[0] if ' - ' in sub_agent_display else sub_agent_display

            # 使用預設值
            account_prefix = ""  # 不使用帳號前綴
            password = "123456"  # 預設密碼

            # 設置處理中狀態
            self.is_processing = True

            # 顯示處理中訊息
            self.view.log_message(f"開始產生 {account_count} 個帳號...", "info")

            # 在主線程中產生帳號
            self._generate_accounts_step1(account_count, agent_code, sub_agent_code, password)

        except Exception as e:
            logger.error(f"產生帳號失敗: {e}")
            self.view.log_message(f"產生帳號失敗：{str(e)}", "error")
            self.is_processing = False

    def _generate_accounts_step1(self, account_count, agent_code, sub_agent_code, password):
        """第一步：產生帳號"""
        try:
            # 產生隨機帳號
            self.view.log_message("正在產生隨機帳號...", "info")
            accounts = self.member_service.generate_batch_accounts(account_count)

            # 記錄產生的帳號
            self.view.log_message("產生的帳號列表:", "info")
            import json
            formatted_accounts = json.dumps(accounts, indent=4, ensure_ascii=False)
            self.view.log_message(formatted_accounts, "json")

            # 取得子代理商的幣別
            sub_agent_currency = "PHP"  # 預設幣別
            for sub_agent in self.sub_agents:
                if sub_agent.code == sub_agent_code:
                    sub_agent_currency = sub_agent.currency
                    break

            # 設置跳過 API 調用的標記
            skip_api_call = not agent_code

            # 保存數據到實例變量
            self.accounts = accounts
            self.account_count = account_count
            self.agent_code = agent_code
            self.sub_agent_code = sub_agent_code
            self.sub_agent_currency = sub_agent_currency
            self.password = password
            self.skip_api_call = skip_api_call
            self.results = []
            self.success_count = 0
            self.fail_count = 0
            self.current_index = 0

            # 開始處理帳號
            self.view.log_message("開始處理帳號...", "info")
            self._process_next_account_batch()

        except Exception as e:
            logger.error(f"產生帳號失敗: {e}")
            self.view.log_message(f"❌ 產生帳號失敗: {str(e)}", "error")
            self.is_processing = False

    def _process_next_account_batch(self):
        """處理下一批帳號"""
        try:
            # 每批處理的帳號數量
            batch_size = 5
            batch_end = min(self.current_index + batch_size, self.account_count)

            # 處理這一批帳號
            for i in range(self.current_index, batch_end):
                account = self.accounts[i]

                # 更新進度
                progress_percent = int((i + 1) / self.account_count * 100)
                self.view.update_progress(i + 1, self.account_count)

                # 每處理10%的帳號顯示一次進度
                if (i + 1) % max(1, self.account_count // 10) == 0 or (i + 1) == self.account_count:
                    self.view.log_message(f"⏳ 處理進度: {i + 1}/{self.account_count} ({progress_percent}%)", "info")

                try:
                    # 處理單個帳號
                    if self.skip_api_call:
                        success, account, member_id = True, account, "未執行 API"
                    else:
                        # 建立會員
                        member_id = self.member_service.create_member(
                            account=account,
                            agent_code=self.agent_code,
                            sub_agent_code=self.sub_agent_code,
                            currency=self.sub_agent_currency
                        )
                        success = bool(member_id)

                    if success:
                        self.view.log_message(f"  ✅ 帳號 #{i+1}: {account} - 會員ID: {member_id}", "info")
                        self.results.append((account, member_id))
                        self.success_count += 1
                    else:
                        self.view.log_message(f"  ❌ 帳號 #{i+1}: {account} - 建立失敗", "error")
                        self.results.append((account, "建立失敗"))
                        self.fail_count += 1

                except Exception as e:
                    logger.error(f"處理帳號 {account} 失敗: {e}")
                    self.view.log_message(f"  ❌ 帳號 #{i+1}: {account} - 處理失敗: {str(e)}", "error")
                    self.results.append((account, "建立失敗"))
                    self.fail_count += 1

            # 更新當前索引
            self.current_index = batch_end

            # 檢查是否處理完所有帳號
            if self.current_index < self.account_count:
                # 還有帳號需要處理，安排下一批
                if self.root and hasattr(self.root, 'after') and callable(self.root.after):
                    self.root.after(10, self._process_next_account_batch)
                else:
                    logger.error("無法安排下一批帳號處理：root.after 方法不可用")
                    self.is_processing = False
            else:
                # 所有帳號處理完成，顯示結果
                self._finish_account_generation()

        except Exception as e:
            logger.error(f"批次處理帳號失敗: {e}")
            self.view.log_message(f"❌ 批次處理帳號失敗: {str(e)}", "error")
            self.is_processing = False

    def _finish_account_generation(self):
        """完成帳號產生"""
        try:
            # 顯示結果統計
            self.view.log_message(f"\n✅ 帳號產生完成: 成功 {self.success_count} 個帳號, 失敗 {self.fail_count} 個帳號", "success")

            if self.success_count <= 0:
                self.view.log_message(f"❌ 帳號產生失敗，所有帳號處理均失敗", "error")

            # 輸出結果
            if self.account_count == 1:
                self._display_single_result(self.results[0])
            else:
                self._save_results_to_csv(self.results)

            # 處理完成，重設狀態
            self.is_processing = False

        except Exception as e:
            logger.error(f"完成帳號產生失敗: {e}")
            self.view.log_message(f"❌ 完成帳號產生失敗: {str(e)}", "error")
            self.is_processing = False



    def _display_single_result(self, result: Tuple[str, str]):
        """顯示單一帳號結果"""
        account, member_id = result
        self.view.log_message("產生結果：", "success")
        self.view.log_message(f"帳號：{account}")

        # 如果是未執行 API 的情況，顯示不同的訊息
        if member_id == "未執行 API":
            self.view.log_message("已產生帳號，未執行 API 請求", "info")
        else:
            self.view.log_message(f"會員ID：{member_id}")

        # 將單一帳號結果也儲存為 CSV 檔案，以便能使用下載功能
        self._save_results_to_csv([(account, member_id)])

    def _save_results_to_csv(self, results: List[Tuple[str, str]]):
        """儲存結果到 CSV 檔案"""
        try:
            current_dir = os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__)
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

            # 取得目前的 AgentCode 和 SubAgentCode
            agent_code = self.view.cb_agent_code.get().strip() or "NoAgent"
            sub_agent_display = self.view.cb_sub_agent_code.get().strip()

            # 從顯示名稱中取得子代理商代碼和幣別
            sub_agent_code = ""
            currency = "PHP"  # 預設幣別

            # 從子代理商對應表中取得代碼
            if hasattr(self, "sub_agent_map") and self.sub_agent_map:
                sub_agent_code = self.sub_agent_map.get(sub_agent_display, "")

            # 如果在對應表中找不到，則嘗試從顯示名稱中提取
            if not sub_agent_code and sub_agent_display:
                if ' - ' in sub_agent_display:
                    parts = sub_agent_display.split(' - ')
                    sub_agent_code = parts[0]
                    if len(parts) > 1:
                        currency = parts[1]  # 從顯示名稱中取得幣別
                else:
                    sub_agent_code = sub_agent_display

            # 如果沒有 SubAgentCode，使用預設值
            sub_agent_code = sub_agent_code or "NoSubAgent"

            # 如果有子代理商列表，嘗試從列表中取得幣別
            if hasattr(self, "sub_agents") and self.sub_agents:
                for sa in self.sub_agents:
                    if sa.code == sub_agent_code:
                        currency = sa.currency
                        break

            # 取得產生的帳號數量
            account_count = len(results)

            # 清理檔案名稱中的特殊字符，避免檔案系統問題
            def clean_filename_part(part):
                # 移除檔案名稱中的特殊字符
                import re
                return re.sub(r'[\\/*?:"<>|]', '_', str(part))

            agent_code_clean = clean_filename_part(agent_code)
            sub_agent_code_clean = clean_filename_part(sub_agent_code)
            currency_clean = clean_filename_part(currency)

            # 產生檔案名稱：AgentCode-SubAgentCode-幣別-數量-時間戳記
            csv_filename = f"{agent_code_clean}-{sub_agent_code_clean}-{currency_clean}-{account_count}-{timestamp}.csv"
            csv_full_path = os.path.join(current_dir, csv_filename)

            # 檢查是否有未執行 API 的結果
            has_skipped_api = any(result[1] == "未執行 API" for result in results)

            with open(csv_full_path, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                if has_skipped_api:
                    # 如果有未執行 API 的結果，只寫入帳號
                    writer.writerow(["Account"])
                    for r in results:
                        writer.writerow([r[0]])
                else:
                    # 正常寫入帳號和會員ID
                    writer.writerow(["Account", "MemberId"])
                    for r in results:
                        writer.writerow(r)

            self.last_output_path = csv_full_path
            self.view.log_message(f"CSV 檔案已產生：{csv_filename}", "success")
            self.view.log_message(f"完整路徑：{csv_full_path}", "info")
            self.view.log_message("可點擊「下載帳號列表」按鈕開啟資料夾", "info")

            # 記錄檔案命名格式訊息
            self.view.log_message(f"檔案命名格式：AgentCode-SubAgentCode-幣別-數量-時間戳記", "info")
            self.view.log_message(f"AgentCode: {agent_code}", "info")
            self.view.log_message(f"SubAgentCode: {sub_agent_code}", "info")
            self.view.log_message(f"幣別: {currency}", "info")
            self.view.log_message(f"帳號數量: {account_count}", "info")
            self.view.log_message(f"時間戳記: {timestamp}", "info")

            # 啟用下載按鈕並啟動閃爍效果
            self._enable_download_button_safe()
            self._start_flash_safe()
            self._log_to_queue("下載按鈕已啟用，可點擊下載帳號列表", "info")

            # 顯示 CSV 檔案內容預覽
            self.view.log_message("帳號列表預覽:", "info")

            # 檢查是否有未執行 API 的結果
            has_skipped_api = any(result[1] == "未執行 API" for result in results)

            if has_skipped_api:
                # 如果有未執行 API 的結果，只顯示帳號
                preview_content = "Account\n"
                # 只顯示前 10 筆資料
                preview_limit = min(10, len(results))
                for i in range(preview_limit):
                    preview_content += f"{results[i][0]}\n"
                if len(results) > preview_limit:
                    preview_content += f"... (共 {len(results)} 筆資料)"
            else:
                # 正常顯示帳號和會員ID
                preview_content = "Account,MemberId\n"
                # 只顯示前 10 筆資料
                preview_limit = min(10, len(results))
                for i in range(preview_limit):
                    preview_content += f"{results[i][0]},{results[i][1]}\n"
                if len(results) > preview_limit:
                    preview_content += f"... (共 {len(results)} 筆資料)"

            self.view.log_message(preview_content, "json")

        except Exception as e:
            logger.error(f"儲存 CSV 檔案失敗: {e}")
            self.view.log_message(f"儲存 CSV 檔案失敗: {str(e)}", "error")

    def open_output_folder(self):
        """開啟輸出資料夾"""
        if not self.last_output_path or not os.path.exists(self.last_output_path):
            self.view.log_message("找不到輸出檔案", "error")
            return

        try:
            folder_path = os.path.dirname(self.last_output_path)
            # 使用平台特定的方式開啟資料夾
            if sys.platform == 'win32':
                # Windows
                os.startfile(folder_path)
            else:
                # 其他平台使用 subprocess
                # 為了避免 IDE 警告，使用更簡單的方式
                # 注意：這裡的 subprocess.Popen 可能會在 IDE 中顯示"無法連線到程式碼"的警告，但這是正常的
                # 因為 subprocess 模組在不同平台上的實現可能不同
                cmd = 'open' if sys.platform == 'darwin' else 'xdg-open'
                subprocess.Popen([cmd, folder_path], stderr=subprocess.PIPE, stdout=subprocess.PIPE)

            self.view.log_message(f"已開啟資料夾: {folder_path}", "info")

        except Exception as e:
            logger.error(f"開啟資料夾失敗: {e}")
            self.view.log_message(f"開啟資料夾失敗: {str(e)}", "error")
