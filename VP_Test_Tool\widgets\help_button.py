"""幫助按鈕元件"""
import tkinter as tk
from tkinter import ttk
from utils.theme import ThemeManager
from utils.icon_manager import IconManager

class HelpButton(ttk.Label):
    """幫助按鈕元件

    提供一個帶有問號圖示的按鈕，點擊時顯示幫助訊息。

    Args:
        parent: 父元件
        help_text: 幫助訊息
        **kwargs: 其他參數
    """
    def __init__(
        self,
        parent,
        help_text: str,
        title: str = "幫助資訊",
        **kwargs
    ):
        # 取得主題管理器
        self.theme_manager = ThemeManager()

        # 設定變數
        self.help_text = help_text
        self.title = title
        self.tooltip = None

        # 建立標籤
        super().__init__(
            parent,
            text=IconManager.get('help'),
            font=("TkDefaultFont", 12),
            foreground=self.theme_manager.get_color("info"),
            cursor="hand2",
            **kwargs
        )

        # 綁定事件
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        self.bind("<Button-1>", self._on_click)

    def _on_enter(self, event):
        """滑鼠進入時顯示提示"""
        # 取得滑鼠位置
        x, y = event.x_root, event.y_root

        # 建立提示視窗
        self.tooltip = tk.Toplevel(self)
        self.tooltip.wm_overrideredirect(True)  # 移除視窗邊框
        self.tooltip.wm_geometry(f"+{x + 15}+{y + 10}")  # 設定位置

        # 建立提示內容
        frame = ttk.Frame(self.tooltip, relief=tk.RAISED, borderwidth=1)
        frame.pack(fill=tk.BOTH, expand=True)

        # 設定最大寬度
        max_width = 300

        # 建立標題
        title_label = ttk.Label(
            frame,
            text=self.title,  # 使用傳入的標題
            font=self.theme_manager.get_font("subtitle"),
            foreground=self.theme_manager.get_color("primary"),
            background=self.theme_manager.get_color("surface"),
            padding=(10, 5)
        )
        title_label.pack(fill=tk.X)

        # 建立分隔線
        separator = ttk.Separator(frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X)

        # 建立內容
        content_label = ttk.Label(
            frame,
            text=self.help_text,
            font=self.theme_manager.get_font("text"),
            foreground=self.theme_manager.get_color("text_primary"),
            background=self.theme_manager.get_color("surface"),
            wraplength=max_width,
            justify=tk.LEFT,
            padding=(10, 10)
        )
        content_label.pack(fill=tk.BOTH, expand=True)

    def _on_leave(self, _):
        """滑鼠離開時隱藏提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

    def _on_click(self, _):
        """點擊時顯示幫助對話框"""
        # 如果有提示視窗，先關閉
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

        # 建立對話框
        dialog = tk.Toplevel(self)
        dialog.title(self.title)  # 使用傳入的標題
        dialog.geometry("400x300")
        dialog.minsize(300, 200)

        # 設定模態
        dialog.transient(self.winfo_toplevel())
        dialog.grab_set()

        # 建立內容框架
        content_frame = ttk.Frame(dialog, padding=15)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 建立標題
        title_label = ttk.Label(
            content_frame,
            text=self.title,  # 使用傳入的標題
            font=self.theme_manager.get_font("title"),
            foreground=self.theme_manager.get_color("primary")
        )
        title_label.pack(anchor="w", pady=(0, 10))

        # 建立分隔線
        separator = ttk.Separator(content_frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 10))

        # 建立內容
        content_text = tk.Text(
            content_frame,
            wrap=tk.WORD,
            font=self.theme_manager.get_font("text"),
            padx=10,
            pady=10,
            height=10
        )
        content_text.pack(fill=tk.BOTH, expand=True)
        content_text.insert(tk.END, self.help_text)
        content_text.config(state=tk.DISABLED)  # 設為唯讀

        # 建立滾動條
        scrollbar = ttk.Scrollbar(content_text, orient=tk.VERTICAL, command=content_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        content_text.config(yscrollcommand=scrollbar.set)

        # 建立按鈕框架
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 建立關閉按鈕
        close_button = ttk.Button(
            button_frame,
            text="關閉",
            command=dialog.destroy
        )
        close_button.pack(side=tk.RIGHT)

        # 設定焦點
        close_button.focus_set()

        # 綁定 Escape 鍵關閉對話框
        dialog.bind("<Escape>", lambda _: dialog.destroy())

        # 等待對話框關閉
        self.wait_window(dialog)
