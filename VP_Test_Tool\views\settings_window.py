"""設定檔管理視窗"""
import tkinter as tk
from tkinter import ttk, messagebox

from utils.constants import PADDING
from utils.config import Config
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from views.settings_panel import SettingsPanel
from widgets.modern_button import ModernButton
from widgets.card_frame import CardFrame

class SettingsWindow(tk.Toplevel):
    """設定檔管理視窗"""
    def __init__(self, parent, config: Config):
        super().__init__(parent)
        self.title("設定")
        self.config = config
        self.theme_manager = ThemeManager()

        # 設定視窗屬性
        self.geometry("800x600")
        self.resizable(True, True)
        self.minsize(600, 400)
        self.transient(parent)  # 設定為父視窗的子視窗
        self.grab_set()  # 設定為模態視窗

        # 套用主題
        self.theme_manager.apply_theme_to_widgets(self)

        # 初始化介面
        self._init_ui()

        # 置中視窗
        self._center_window()

    def _center_window(self):
        """置中視窗"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def _init_ui(self):
        """初始化介面"""
        # 建立底部按鈕區域 - 先建立但不顯示
        self._create_bottom_buttons()

        # 建立主要的 Notebook
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(expand=True, fill='both', padx=PADDING, pady=PADDING)

        # 設定其他 Notebook 樣式
        self._setup_notebook_style()

        # 主題設定頁面
        self.theme_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.theme_frame, text=f"  {IconManager.get('settings')}  主題設定  ")

        # 建立設定面板
        self.settings_panel = SettingsPanel(
            self.theme_frame,
            self.config,
            on_apply=self._on_settings_apply
        )
        self.settings_panel.pack(expand=True, fill='both')

        # API 設定頁面
        self.api_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.api_frame, text=f"  {IconManager.get('link')}  API 設定  ")

        # 建立 API 設定卡片
        self.api_card = CardFrame(self.api_frame, title="API 設定", icon=IconManager.get('link'))
        self.api_card.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)

        api_content = self.api_card.get_content_frame()

        # 基礎 URL
        base_url_frame = ttk.Frame(api_content)
        base_url_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        ttk.Label(
            base_url_frame,
            text="基礎 URL:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).pack(anchor="w")

        self.entry_base_url = ttk.Entry(
            base_url_frame,
            font=("Microsoft JhengHei UI", 10),
            width=50
        )
        self.entry_base_url.pack(fill="x", pady=(5, 0))

        # 超時設定
        timeout_frame = ttk.Frame(api_content)
        timeout_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        ttk.Label(
            timeout_frame,
            text="超時設定(秒):",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).pack(anchor="w")

        self.entry_timeout = ttk.Entry(
            timeout_frame,
            font=("Microsoft JhengHei UI", 10),
            width=10
        )
        self.entry_timeout.pack(anchor="w", pady=(5, 0))

        # 資料庫設定頁面
        self.db_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.db_frame, text=f"  {IconManager.get('database')}  資料庫設定  ")

        # Git 設定頁面
        self.git_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.git_frame, text=f"  {IconManager.get('cloud-download')}  Git 設定  ")

        # 建立 Git 設定卡片
        self.git_card = CardFrame(self.git_frame, title="Git 設定", icon=IconManager.get('cloud-download'))
        self.git_card.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)

        git_content = self.git_card.get_content_frame()

        # 使用網格布局
        git_grid = ttk.Frame(git_content)
        git_grid.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)

        # API URL
        ttk.Label(
            git_grid,
            text="API URL:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=0, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_api_url = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=40
        )
        self.entry_git_api_url.grid(row=0, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 備用 API URL
        ttk.Label(
            git_grid,
            text="備用 API URL:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=1, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_api_url_alt = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=40
        )
        self.entry_git_api_url_alt.grid(row=1, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 專案路徑
        ttk.Label(
            git_grid,
            text="專案路徑:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=2, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_project_path = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=30
        )
        self.entry_git_project_path.grid(row=2, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 分支
        ttk.Label(
            git_grid,
            text="分支:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=3, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_branch = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=20
        )
        self.entry_git_branch.grid(row=3, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 資料夾路徑
        ttk.Label(
            git_grid,
            text="資料夾路徑:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=4, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_folder_path = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=30
        )
        self.entry_git_folder_path.grid(row=4, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 存取權杖
        ttk.Label(
            git_grid,
            text="存取權杖:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=5, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_git_token = ttk.Entry(
            git_grid,
            font=("Microsoft JhengHei UI", 10),
            width=40,
            show="*"
        )
        self.entry_git_token.grid(row=5, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 顯示權杖按鈕
        self.show_token_var = tk.BooleanVar(value=False)
        self.show_token_check = ttk.Checkbutton(
            git_grid,
            text="顯示權杖",
            variable=self.show_token_var,
            command=self._toggle_token_visibility
        )
        self.show_token_check.grid(row=5, column=2, sticky="w", padx=PADDING, pady=PADDING)

        # 建立資料庫設定卡片
        self.db_card = CardFrame(self.db_frame, title="資料庫設定", icon=IconManager.get('database'))
        self.db_card.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)

        db_content = self.db_card.get_content_frame()

        # 使用網格布局
        db_grid = ttk.Frame(db_content)
        db_grid.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)

        # 主機
        ttk.Label(
            db_grid,
            text="主機:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=0, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_host = ttk.Entry(
            db_grid,
            font=("Microsoft JhengHei UI", 10),
            width=30
        )
        self.entry_host.grid(row=0, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 埠
        ttk.Label(
            db_grid,
            text="埠:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=1, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_port = ttk.Entry(
            db_grid,
            font=("Microsoft JhengHei UI", 10),
            width=10
        )
        self.entry_port.grid(row=1, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 使用者
        ttk.Label(
            db_grid,
            text="使用者:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=2, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_user = ttk.Entry(
            db_grid,
            font=("Microsoft JhengHei UI", 10),
            width=20
        )
        self.entry_user.grid(row=2, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 密碼
        ttk.Label(
            db_grid,
            text="密碼:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=3, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_password = ttk.Entry(
            db_grid,
            font=("Microsoft JhengHei UI", 10),
            width=20,
            show="*"
        )
        self.entry_password.grid(row=3, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 資料庫
        ttk.Label(
            db_grid,
            text="資料庫:",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).grid(row=4, column=0, sticky="w", padx=PADDING, pady=PADDING)

        self.entry_database = ttk.Entry(
            db_grid,
            font=("Microsoft JhengHei UI", 10),
            width=20
        )
        self.entry_database.grid(row=4, column=1, sticky="w", padx=PADDING, pady=PADDING)

        # 載入設定
        self._load_settings()

    def _create_bottom_buttons(self):
        """建立底部按鈕區域"""
        # 按鈕區域 - 使用 Frame 作為底部容器
        self.bottom_frame = ttk.Frame(self)
        self.bottom_frame.pack(side="bottom", fill="x", padx=PADDING, pady=PADDING)

        # 分隔線
        separator = ttk.Separator(self, orient="horizontal")
        separator.pack(side="bottom", fill="x", padx=PADDING)

        # 按鈕區域
        btn_frame = ttk.Frame(self.bottom_frame)
        btn_frame.pack(fill="x")

        # 儲存按鈕
        self.save_button = ModernButton(
            btn_frame,
            text="儲存設定",
            icon=IconManager.get('save'),
            command=self._save_settings,
            button_type="primary"
        )
        self.save_button.pack(side="right", padx=PADDING)

        # 取消按鈕
        self.cancel_button = ModernButton(
            btn_frame,
            text="取消",
            icon=IconManager.get('close'),
            command=self.destroy,
            button_type="secondary"
        )
        self.cancel_button.pack(side="right", padx=PADDING)

    def _on_settings_apply(self):
        """當設定套用時"""
        # 重新套用主題
        self.theme_manager.apply_theme_to_widgets(self)

    def _load_settings(self):
        """載入設定值"""
        api_config = self.config.get("api", {})
        self.entry_base_url.insert(0, api_config.get("base_url", ""))
        self.entry_timeout.insert(0, str(api_config.get("timeout", 30)))

        db_config = self.config.get("database", {})
        self.entry_host.insert(0, db_config.get("host", ""))
        self.entry_port.insert(0, str(db_config.get("port", 3306)))
        self.entry_user.insert(0, db_config.get("user", ""))
        self.entry_password.insert(0, db_config.get("password", ""))
        self.entry_database.insert(0, db_config.get("database", ""))

        # 載入 Git 設定
        git_config = self.config.get("git", {})
        self.entry_git_api_url.insert(0, git_config.get("api_url", ""))
        self.entry_git_api_url_alt.insert(0, git_config.get("api_url_alt", ""))
        self.entry_git_project_path.insert(0, git_config.get("project_path", ""))
        self.entry_git_branch.insert(0, git_config.get("branch", ""))
        self.entry_git_folder_path.insert(0, git_config.get("folder_path", ""))
        self.entry_git_token.insert(0, git_config.get("token", ""))

    def _setup_notebook_style(self):
        """設定 Notebook 樣式"""
        # 使用主視窗中已經設定好的樣式
        # 不需要再次創建主題
        pass

    def _toggle_token_visibility(self):
        """切換權杖顯示狀態"""
        if self.show_token_var.get():
            self.entry_git_token.config(show="")
        else:
            self.entry_git_token.config(show="*")

    def _save_settings(self):
        """儲存設定"""
        try:
            # 驗證並更新設定
            timeout = int(self.entry_timeout.get())
            port = int(self.entry_port.get())

            if timeout <= 0:
                raise ValueError("超時設定必須大於 0")
            if port <= 0 or port > 65535:
                raise ValueError("埠號必須介於 1 至 65535 之間")

            # 儲存 API 設定
            self.config.set("api", {
                "base_url": self.entry_base_url.get().strip(),
                "timeout": timeout
            })

            # 儲存資料庫設定
            self.config.set("database", {
                "host": self.entry_host.get().strip(),
                "port": port,
                "user": self.entry_user.get().strip(),
                "password": self.entry_password.get().strip(),
                "database": self.entry_database.get().strip()
            })

            # 儲存 Git 設定
            self.config.set("git", {
                "api_url": self.entry_git_api_url.get().strip(),
                "api_url_alt": self.entry_git_api_url_alt.get().strip(),
                "project_path": self.entry_git_project_path.get().strip(),
                "branch": self.entry_git_branch.get().strip(),
                "folder_path": self.entry_git_folder_path.get().strip(),
                "token": self.entry_git_token.get().strip()
            })

            # 儲存 UI 設定
            # 確保設定檔中有 ui 節點
            if "ui" not in self.config.config:
                self.config.config["ui"] = {}

            # 從設定面板取得 UI 設定
            ui_config = self.config.config["ui"]
            ui_config["theme"] = self.settings_panel.theme_combobox.get()
            ui_config["font_size"] = self.settings_panel.font_size_var.get()
            ui_config["window_width"] = self.settings_panel.window_width_var.get()
            ui_config["window_height"] = self.settings_panel.window_height_var.get()

            # 儲存設定
            self.config.save_config()

            # 重新套用主題
            self.theme_manager.apply_theme_to_widgets(self)

            messagebox.showinfo("成功", "設定已儲存\n\n部分設定需要重新啟動應用程式才能生效。")
            self.destroy()

        except ValueError as e:
            messagebox.showerror("錯誤", str(e))
