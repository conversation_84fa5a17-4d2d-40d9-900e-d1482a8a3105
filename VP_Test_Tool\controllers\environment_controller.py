"""環境配置管理控制器"""

import tkinter as tk
from tkinter import messagebox, filedialog, simpledialog
import logging
from typing import Dict, Any, List
import requests
import threading
from utils.environment_config import env_config
from views.environment_panel import EnvironmentPanel

logger = logging.getLogger(__name__)

class EnvironmentController:
    """環境配置管理控制器"""
    
    def __init__(self, environment_panel: EnvironmentPanel):
        self.view = environment_panel
        self.env_config = env_config
        
        # 綁定事件
        self.setup_bindings()
        
        # 初始化界面
        self.refresh_environments()
    
    def setup_bindings(self):
        """設置事件綁定"""
        self.view.refresh_environments = self.refresh_environments
        self.view.switch_environment = self.switch_environment
        self.view.add_environment = self.add_environment
        self.view.edit_environment = self.edit_environment
        self.view.delete_environment = self.delete_environment
        self.view.edit_server = self.edit_server
        self.view.test_server_connection = self.test_server_connection
        self.view.backup_config = self.backup_config
        self.view.restore_config = self.restore_config
        self.view.validate_config = self.validate_config
        self.view.on_environment_select = self.on_environment_select
    
    def refresh_environments(self):
        """重新整理環境列表"""
        try:
            self.view.log_message("正在重新整理環境列表...")
            
            # 重新載入環境配置
            self.env_config.load_environments()
            
            # 更新當前環境顯示
            current_env = self.env_config.get_current_environment()
            self.view.set_current_environment(current_env)
            
            # 更新環境列表
            environments = self.env_config.list_environments()
            self.view.update_environment_list(environments)
            
            # 更新伺服器列表（顯示當前環境的伺服器配置）
            current_env_info = self.env_config.get_environment_info()
            servers = current_env_info.get("api_servers", {})
            self.view.update_server_list(servers)
            
            self.view.log_message("環境列表重新整理完成")
            
        except Exception as e:
            logger.error(f"重新整理環境列表失敗: {e}")
            self.view.log_message(f"重新整理失敗: {e}", "ERROR")
    
    def switch_environment(self):
        """切換環境"""
        try:
            selection = self.view.env_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "請選擇要切換的環境")
                return
            
            # 取得選中的環境名稱
            item = self.view.env_tree.item(selection[0])
            env_name = item["values"][0]
            
            # 確認切換
            result = messagebox.askyesno(
                "確認切換",
                f"確定要切換到環境 '{env_name}' 嗎？\n\n"
                "切換環境後，所有 API 請求將使用新環境的伺服器配置。"
            )
            
            if result:
                success = self.env_config.switch_environment(env_name)
                if success:
                    self.view.log_message(f"已切換到環境: {env_name}")
                    self.refresh_environments()
                    
                    # 重新載入 API URLs
                    self._reload_api_urls()
                    
                    messagebox.showinfo("成功", f"已成功切換到環境: {env_name}")
                else:
                    self.view.log_message(f"切換環境失敗: {env_name}", "ERROR")
                    messagebox.showerror("錯誤", f"切換環境失敗: {env_name}")
        
        except Exception as e:
            logger.error(f"切換環境失敗: {e}")
            self.view.log_message(f"切換環境失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"切換環境失敗: {e}")
    
    def add_environment(self):
        """新增環境"""
        try:
            # 創建新增環境對話框
            dialog = EnvironmentDialog(self.view, "新增環境")
            result = dialog.show()
            
            if result:
                env_name = result["name"]
                env_config_data = {
                    "name": result["display_name"],
                    "description": result["description"],
                    "api_servers": result["api_servers"]
                }
                
                success = self.env_config.add_environment(env_name, env_config_data)
                if success:
                    self.view.log_message(f"已新增環境: {env_name}")
                    self.refresh_environments()
                    messagebox.showinfo("成功", f"已成功新增環境: {env_name}")
                else:
                    self.view.log_message(f"新增環境失敗: {env_name}", "ERROR")
                    messagebox.showerror("錯誤", f"新增環境失敗: {env_name}")
        
        except Exception as e:
            logger.error(f"新增環境失敗: {e}")
            self.view.log_message(f"新增環境失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"新增環境失敗: {e}")
    
    def edit_environment(self):
        """編輯環境"""
        try:
            selection = self.view.env_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "請選擇要編輯的環境")
                return
            
            # 取得選中的環境名稱
            item = self.view.env_tree.item(selection[0])
            env_name = item["values"][0]
            
            # 取得環境資訊
            env_info = self.env_config.get_environment_info(env_name)
            if not env_info:
                messagebox.showerror("錯誤", f"找不到環境: {env_name}")
                return
            
            # 創建編輯環境對話框
            dialog = EnvironmentDialog(self.view, "編輯環境", env_name, env_info)
            result = dialog.show()
            
            if result:
                env_config_data = {
                    "name": result["display_name"],
                    "description": result["description"],
                    "api_servers": result["api_servers"],
                    "created_at": env_info.get("created_at"),
                    "last_used": env_info.get("last_used")
                }
                
                success = self.env_config.add_environment(env_name, env_config_data)
                if success:
                    self.view.log_message(f"已更新環境: {env_name}")
                    self.refresh_environments()
                    messagebox.showinfo("成功", f"已成功更新環境: {env_name}")
                else:
                    self.view.log_message(f"更新環境失敗: {env_name}", "ERROR")
                    messagebox.showerror("錯誤", f"更新環境失敗: {env_name}")
        
        except Exception as e:
            logger.error(f"編輯環境失敗: {e}")
            self.view.log_message(f"編輯環境失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"編輯環境失敗: {e}")
    
    def delete_environment(self):
        """刪除環境"""
        try:
            selection = self.view.env_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "請選擇要刪除的環境")
                return
            
            # 取得選中的環境名稱
            item = self.view.env_tree.item(selection[0])
            env_name = item["values"][0]
            
            # 確認刪除
            result = messagebox.askyesno(
                "確認刪除",
                f"確定要刪除環境 '{env_name}' 嗎？\n\n"
                "此操作無法復原！"
            )
            
            if result:
                success = self.env_config.remove_environment(env_name)
                if success:
                    self.view.log_message(f"已刪除環境: {env_name}")
                    self.refresh_environments()
                    messagebox.showinfo("成功", f"已成功刪除環境: {env_name}")
                else:
                    self.view.log_message(f"刪除環境失敗: {env_name}", "ERROR")
                    messagebox.showerror("錯誤", f"刪除環境失敗: {env_name}")
        
        except Exception as e:
            logger.error(f"刪除環境失敗: {e}")
            self.view.log_message(f"刪除環境失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"刪除環境失敗: {e}")
    
    def edit_server(self, event=None):
        """編輯伺服器配置"""
        try:
            selection = self.view.server_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "請選擇要編輯的伺服器")
                return
            
            # 取得選中的伺服器類型
            item = self.view.server_tree.item(selection[0])
            server_type = item["values"][0]
            current_url = item["values"][1]
            
            # 輸入新的 URL
            new_url = simpledialog.askstring(
                "編輯伺服器",
                f"請輸入 {server_type} 的新 URL:",
                initialvalue=current_url
            )
            
            if new_url and new_url != current_url:
                success = self.env_config.update_api_server(server_type, new_url)
                if success:
                    self.view.log_message(f"已更新 {server_type} 伺服器: {new_url}")
                    self.refresh_environments()
                    
                    # 重新載入 API URLs
                    self._reload_api_urls()
                    
                    messagebox.showinfo("成功", f"已成功更新 {server_type} 伺服器")
                else:
                    self.view.log_message(f"更新伺服器失敗: {server_type}", "ERROR")
                    messagebox.showerror("錯誤", f"更新伺服器失敗: {server_type}")
        
        except Exception as e:
            logger.error(f"編輯伺服器失敗: {e}")
            self.view.log_message(f"編輯伺服器失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"編輯伺服器失敗: {e}")
    
    def test_server_connection(self):
        """測試伺服器連線"""
        try:
            selection = self.view.server_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "請選擇要測試的伺服器")
                return
            
            # 取得選中的伺服器
            item = self.view.server_tree.item(selection[0])
            server_type = item["values"][0]
            server_url = item["values"][1]
            
            self.view.log_message(f"正在測試 {server_type} 連線...")
            
            # 在背景執行連線測試
            def test_connection():
                try:
                    response = requests.get(server_url, timeout=5)
                    if response.status_code == 200:
                        self.view.log_message(f"{server_type} 連線測試成功")
                        messagebox.showinfo("成功", f"{server_type} 連線測試成功")
                    else:
                        self.view.log_message(f"{server_type} 連線測試失敗: HTTP {response.status_code}", "WARNING")
                        messagebox.showwarning("警告", f"{server_type} 連線測試失敗: HTTP {response.status_code}")
                except Exception as e:
                    self.view.log_message(f"{server_type} 連線測試失敗: {e}", "ERROR")
                    messagebox.showerror("錯誤", f"{server_type} 連線測試失敗: {e}")
            
            # 在背景執行測試
            threading.Thread(target=test_connection, daemon=True).start()
        
        except Exception as e:
            logger.error(f"測試伺服器連線失敗: {e}")
            self.view.log_message(f"測試伺服器連線失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"測試伺服器連線失敗: {e}")
    
    def backup_config(self):
        """備份配置"""
        try:
            # 選擇備份檔案路徑
            file_path = filedialog.asksaveasfilename(
                title="備份環境配置",
                defaultextension=".json",
                filetypes=[("JSON 檔案", "*.json"), ("所有檔案", "*.*")]
            )
            
            if file_path:
                backup_path = self.env_config.backup_environments(file_path)
                if backup_path:
                    self.view.log_message(f"配置已備份到: {backup_path}")
                    messagebox.showinfo("成功", f"配置已成功備份到:\n{backup_path}")
                else:
                    self.view.log_message("備份配置失敗", "ERROR")
                    messagebox.showerror("錯誤", "備份配置失敗")
        
        except Exception as e:
            logger.error(f"備份配置失敗: {e}")
            self.view.log_message(f"備份配置失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"備份配置失敗: {e}")
    
    def restore_config(self):
        """恢復配置"""
        try:
            # 選擇備份檔案
            file_path = filedialog.askopenfilename(
                title="恢復環境配置",
                filetypes=[("JSON 檔案", "*.json"), ("所有檔案", "*.*")]
            )
            
            if file_path:
                # 確認恢復
                result = messagebox.askyesno(
                    "確認恢復",
                    "確定要從備份檔案恢復環境配置嗎？\n\n"
                    "此操作將覆蓋現有的所有環境配置！"
                )
                
                if result:
                    success = self.env_config.restore_environments(file_path)
                    if success:
                        self.view.log_message(f"已從備份恢復配置: {file_path}")
                        self.refresh_environments()
                        messagebox.showinfo("成功", "配置已成功恢復")
                    else:
                        self.view.log_message("恢復配置失敗", "ERROR")
                        messagebox.showerror("錯誤", "恢復配置失敗")
        
        except Exception as e:
            logger.error(f"恢復配置失敗: {e}")
            self.view.log_message(f"恢復配置失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"恢復配置失敗: {e}")
    
    def validate_config(self):
        """驗證配置"""
        try:
            self.view.log_message("正在驗證環境配置...")
            
            current_env = self.env_config.get_current_environment()
            validation_result = self.env_config.validate_environment(current_env)
            
            if validation_result["valid"]:
                self.view.log_message("環境配置驗證通過")
                messagebox.showinfo("驗證結果", "環境配置驗證通過")
            else:
                error_msg = "\n".join(validation_result["errors"])
                warning_msg = "\n".join(validation_result["warnings"])
                
                message = "環境配置驗證失敗:\n\n"
                if error_msg:
                    message += f"錯誤:\n{error_msg}\n\n"
                if warning_msg:
                    message += f"警告:\n{warning_msg}"
                
                self.view.log_message("環境配置驗證失敗", "ERROR")
                messagebox.showerror("驗證結果", message)
        
        except Exception as e:
            logger.error(f"驗證配置失敗: {e}")
            self.view.log_message(f"驗證配置失敗: {e}", "ERROR")
            messagebox.showerror("錯誤", f"驗證配置失敗: {e}")
    
    def on_environment_select(self, event):
        """環境選擇事件"""
        try:
            selection = self.view.env_tree.selection()
            if selection:
                # 取得選中的環境名稱
                item = self.view.env_tree.item(selection[0])
                env_name = item["values"][0]
                
                # 更新伺服器配置顯示
                env_info = self.env_config.get_environment_info(env_name)
                servers = env_info.get("api_servers", {})
                self.view.update_server_list(servers)
        
        except Exception as e:
            logger.error(f"處理環境選擇事件失敗: {e}")
    
    def _reload_api_urls(self):
        """重新載入 API URLs"""
        try:
            # 重新載入 constants 模組中的 API_URLS
            from utils import constants
            constants.API_URLS = constants.get_api_urls()
            self.view.log_message("API URLs 已重新載入")
        except Exception as e:
            logger.error(f"重新載入 API URLs 失敗: {e}")
            self.view.log_message(f"重新載入 API URLs 失敗: {e}", "ERROR")


class EnvironmentDialog:
    """環境配置對話框"""
    
    def __init__(self, parent, title: str, env_name: str = "", env_info: Dict[str, Any] = None):
        self.parent = parent
        self.title = title
        self.env_name = env_name
        self.env_info = env_info or {}
        self.result = None
        
        self.dialog = None
        self.name_var = tk.StringVar(value=env_name)
        self.display_name_var = tk.StringVar(value=env_info.get("name", ""))
        self.description_var = tk.StringVar(value=env_info.get("description", ""))
        
        # 伺服器配置變數
        api_servers = env_info.get("api_servers", {})
        self.mysql_var = tk.StringVar(value=api_servers.get("mysql_operator", ""))
        self.gamebridge_var = tk.StringVar(value=api_servers.get("gamebridge", ""))
        self.tokenguard_var = tk.StringVar(value=api_servers.get("tokenguard", ""))
        self.lottery_var = tk.StringVar(value=api_servers.get("lottery", ""))
        self.simulation_var = tk.StringVar(value=api_servers.get("simulation", ""))
    
    def show(self) -> Dict[str, Any]:
        """顯示對話框並返回結果"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        # 居中顯示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        # 等待對話框關閉
        self.dialog.wait_window()
        
        return self.result
    
    def create_widgets(self):
        """創建對話框元件"""
        # 基本資訊
        basic_frame = ttk.LabelFrame(self.dialog, text="基本資訊", padding=10)
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(basic_frame, text="環境名稱:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.name_var, width=40).grid(row=0, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(basic_frame, text="顯示名稱:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.display_name_var, width=40).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(basic_frame, text="描述:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.description_var, width=40).grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # 伺服器配置
        server_frame = ttk.LabelFrame(self.dialog, text="伺服器配置", padding=10)
        server_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        ttk.Label(server_frame, text="MySQL 操作:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.mysql_var, width=50).grid(row=0, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(server_frame, text="遊戲橋接:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.gamebridge_var, width=50).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(server_frame, text="令牌守護:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.tokenguard_var, width=50).grid(row=2, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(server_frame, text="樂透服務:").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.lottery_var, width=50).grid(row=3, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(server_frame, text="模擬服務:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.simulation_var, width=50).grid(row=4, column=1, sticky=tk.W, pady=2)
        
        # 按鈕
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="確定", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT)
    
    def ok_clicked(self):
        """確定按鈕點擊事件"""
        # 驗證輸入
        if not self.name_var.get().strip():
            messagebox.showerror("錯誤", "請輸入環境名稱")
            return
        
        if not self.display_name_var.get().strip():
            messagebox.showerror("錯誤", "請輸入顯示名稱")
            return
        
        # 收集結果
        self.result = {
            "name": self.name_var.get().strip(),
            "display_name": self.display_name_var.get().strip(),
            "description": self.description_var.get().strip(),
            "api_servers": {
                "mysql_operator": self.mysql_var.get().strip(),
                "gamebridge": self.gamebridge_var.get().strip(),
                "tokenguard": self.tokenguard_var.get().strip(),
                "lottery": self.lottery_var.get().strip(),
                "simulation": self.simulation_var.get().strip()
            }
        }
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按鈕點擊事件"""
        self.result = None
        self.dialog.destroy()
