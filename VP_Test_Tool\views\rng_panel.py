"""RNG 設置面板"""
import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import filedialog
from typing import List
from pathlib import Path
from utils.constants import PADDING
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from widgets.modern_button import ModernButton

class RNGPanel(tk.Frame):
    """RNG 設置面板"""
    def __init__(self, parent):
        # 初始化主題管理器
        self.theme_manager = ThemeManager()

        # 創建 UI 更新隊列和處理器
        import queue
        self.ui_queue = queue.Queue()
        self.is_ui_processor_running = True

        # 使用 tk.Frame 而非 ttk.Frame
        super().__init__(parent, bg=self.theme_manager.get_color("surface"))

        self._init_ui()

        # 啟動 UI 更新處理器
        self.after(100, self._process_ui_queue)

    def _init_ui(self):
        """初始化 UI"""
        # 使用卡片式設計
        from widgets.card_frame import CardFrame
        # 從幫助按鈕模組導入 HelpButton
        from widgets.help_button import HelpButton

        # 建立主容器框架
        container_frame = tk.Frame(self, bg=self.theme_manager.get_color("surface"))
        container_frame.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)

        # 左側設定區域 - 佔空間的 50%
        left_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING/2))

        # 右側日誌區域 - 佔空間的 50%
        right_frame = tk.Frame(container_frame, bg=self.theme_manager.get_color("surface"))
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING/2, 0))

        # 添加幫助按鈕
        help_text = """Slot Set RNG 工具可以幫助您設定老虎機的隨機數生成器參數。

使用步驟：
1. 在「檔案匯入」區域選擇要匯入的設定檔案
2. 點擊「匯入 RNG 檔案」按鈕選擇並匯入檔案
3. 在「遊戲設定」區域選擇遊戲和 RNG 設定
4. 在「會員設定」區域輸入會員資訊
5. 點擊「執行 RNG」按鈕應用 RNG 設定

所有操作日誌將顯示在右側的「操作日誌與說明」區域。"""
        self.help_button = HelpButton(left_frame, help_text=help_text, title="Slot Set RNG 工具幫助")
        self.help_button.pack(side=tk.TOP, anchor=tk.NW, padx=PADDING, pady=PADDING)

        # 檔案匯入區域 - 使用卡片式設計
        import_card = CardFrame(left_frame, title="檔案匯入", icon=IconManager.get('import'))
        import_card.pack(fill="x", padx=PADDING, pady=PADDING)
        import_frame = import_card.get_content_frame()

        # Git 下載按鈕
        self.btn_import_gitlab = ModernButton(
            import_frame,
            text="Git 下載",
            icon=IconManager.get('download'),  # 使用下載圖示
            button_type="info",
            command=self.import_from_gitlab
        )
        self.btn_import_gitlab.pack(side="left", padx=PADDING)

        # 匯入 RNG 檔案按鈕
        self.btn_import = ModernButton(
            import_frame,
            text="匯入 RNG 檔案",
            icon=IconManager.get('import'),  # 使用匯入圖示
            button_type="primary",
            command=self.select_files
        )
        self.btn_import.pack(side="left", padx=PADDING)

        # 移除按鈕閃爍效果

        # 會員設定區域 - 使用卡片式設計
        member_card = CardFrame(left_frame, title="會員設定", icon=IconManager.get('user'))
        member_card.pack(fill="x", padx=PADDING, pady=PADDING)
        member_frame = member_card.get_content_frame()

        # 創建一個框架來容納水平排列的欄位
        fields_frame = tk.Frame(member_frame, bg=ThemeManager().get_color("surface"))
        fields_frame.pack(fill="x", padx=5, pady=5)

        # 設定列的權重，使三個欄位平均分配空間
        fields_frame.columnconfigure(0, weight=1)  # 資料庫來源
        fields_frame.columnconfigure(1, weight=1)  # 平台帳號
        fields_frame.columnconfigure(2, weight=1)  # VP Member ID

        # 資料庫來源選擇 - 第一列第一個
        db_source_frame = tk.Frame(fields_frame, bg=ThemeManager().get_color("surface"))
        db_source_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        tk.Label(
            db_source_frame,
            text="資料庫來源",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=ThemeManager().get_color("surface"),
            fg=ThemeManager().get_color("text_primary")
        ).pack(side=tk.TOP, anchor="w")

        self.db_source = ttk.Combobox(
            db_source_frame,
            values=["OCMS", "OCIntegrator"],
            width=15,
            state="readonly",
            font=("Microsoft JhengHei UI", 10)
        )
        self.db_source.set("OCMS")  # 預設值
        self.db_source.pack(side=tk.TOP, fill="x", pady=(2, 0))

        # 平台帳號輸入 - 第一列第二個
        account_frame = tk.Frame(fields_frame, bg=ThemeManager().get_color("surface"))
        account_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        tk.Label(
            account_frame,
            text="平台帳號",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=ThemeManager().get_color("surface"),
            fg=ThemeManager().get_color("text_primary")
        ).pack(side=tk.TOP, anchor="w")

        self.entry_account = ttk.Entry(account_frame, width=15, font=("Microsoft JhengHei UI", 10))
        self.entry_account.pack(side=tk.TOP, fill="x", pady=(2, 0))

        # VP Member ID 輸入 - 第一列第三個
        member_id_frame = tk.Frame(fields_frame, bg=ThemeManager().get_color("surface"))
        member_id_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        tk.Label(
            member_id_frame,
            text="VP Member ID",
            font=("Microsoft JhengHei UI", 11, "bold"),
            bg=ThemeManager().get_color("surface"),
            fg=ThemeManager().get_color("text_primary")
        ).pack(side=tk.TOP, anchor="w")

        self.entry_member_id = ttk.Entry(member_id_frame, width=15, font=("Microsoft JhengHei UI", 10))
        self.entry_member_id.pack(side=tk.TOP, fill="x", pady=(2, 0))

        # 按鈕框架 - 放在欄位下方
        button_container = tk.Frame(member_frame, bg=ThemeManager().get_color("surface"))
        button_container.pack(fill="x", padx=5, pady=(10, 5))

        # 創建一個空的框架來實現按鈕居中
        button_center_frame = tk.Frame(button_container, bg=ThemeManager().get_color("surface"))
        button_center_frame.pack(side=tk.TOP, anchor=tk.CENTER)

        # 查詢按鈕
        self.btn_query = ModernButton(
            button_center_frame,
            text="查詢會員",
            icon=IconManager.get('search'),
            button_type="primary",
            command=self.query_member
        )
        self.btn_query.pack(side="left", padx=5)

        # 清除資料按鈕
        self.btn_clear_member = ModernButton(
            button_center_frame,
            text="清除資料",
            icon=IconManager.get('delete'),
            button_type="danger",
            command=self.clear_settings
        )
        self.btn_clear_member.pack(side="left", padx=5)

        # 遊戲選擇區域 - 使用卡片式設計
        game_card = CardFrame(left_frame, title="遊戲設定", icon=IconManager.get('game'))
        game_card.pack(fill="x", padx=PADDING, pady=PADDING)
        game_frame = game_card.get_content_frame()

        # 創建一個主框架來容納2x2網格
        game_main_frame = tk.Frame(game_frame, bg=self.theme_manager.get_color("surface"))
        game_main_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        # 上排左側 - 遊戲選擇
        game_select_frame = tk.Frame(game_main_frame, bg=self.theme_manager.get_color("surface"))
        game_select_frame.grid(row=0, column=0, padx=PADDING, pady=PADDING, sticky="ew")

        ttk.Label(
            game_select_frame,
            text="遊戲",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).pack(side=tk.LEFT, padx=PADDING)
        self.cb_game = ttk.Combobox(game_select_frame, state="readonly", width=20, font=("Microsoft JhengHei UI", 10))
        self.cb_game.pack(side=tk.LEFT, padx=PADDING, fill=tk.X, expand=True)

        # 上排右側 - RNG 選擇
        rng_select_frame = tk.Frame(game_main_frame, bg=self.theme_manager.get_color("surface"))
        rng_select_frame.grid(row=0, column=1, padx=PADDING, pady=PADDING, sticky="ew")

        ttk.Label(
            rng_select_frame,
            text="RNG",
            font=("Microsoft JhengHei UI", 11, "bold")
        ).pack(side=tk.LEFT, padx=PADDING)
        self.cb_rng = ttk.Combobox(rng_select_frame, state="readonly", width=20, font=("Microsoft JhengHei UI", 10))
        self.cb_rng.pack(side=tk.LEFT, padx=PADDING, fill=tk.X, expand=True)

        # 設定列的權重，使兩列等寬
        game_main_frame.columnconfigure(0, weight=1)
        game_main_frame.columnconfigure(1, weight=1)

        # 創建一個隱藏的 Entry 來存儲遊戲 ID，但不顯示在界面上
        self.entry_game_id = ttk.Entry(self)
        self.entry_game_id.pack_forget()  # 不顯示在界面上

        # 描述區域 - 使用卡片式設計
        desc_card = CardFrame(left_frame, title="描述", icon=IconManager.get('info'))
        desc_card.pack(fill="both", expand=True, padx=PADDING, pady=PADDING)
        desc_frame = desc_card.get_content_frame()

        # 創建一個容器框架來控制 ScrolledText 的寬度
        desc_container = tk.Frame(desc_frame, bg=self.theme_manager.get_color("surface"))
        desc_container.pack(fill="both", expand=True)

        # 使用權重方式控制寬度比例
        desc_container.columnconfigure(0, weight=2)  # 左側空白 20%
        desc_container.columnconfigure(1, weight=6)  # 中間內容 60%
        desc_container.columnconfigure(2, weight=2)  # 右側空白 20%

        # 左側空白框架
        left_spacer = tk.Frame(desc_container, bg=self.theme_manager.get_color("surface"))
        left_spacer.grid(row=0, column=0, sticky="ns")

        # 中間描述文字區域
        desc_text_frame = tk.Frame(desc_container, bg=self.theme_manager.get_color("surface"))
        desc_text_frame.grid(row=0, column=1, sticky="nsew")

        # 右側空白框架
        right_spacer = tk.Frame(desc_container, bg=self.theme_manager.get_color("surface"))
        right_spacer.grid(row=0, column=2, sticky="ns")

        # 使用 ScrolledText 而非普通的 Text，以支援上下捲動
        from tkinter.scrolledtext import ScrolledText
        self.text_description = ScrolledText(
            desc_text_frame,
            wrap="word",
            font=("Microsoft JhengHei UI", 10),  # 使用更清晰的字體
            height=10
        )
        self.text_description.pack(fill="both", expand=True)

        # 按鈕區域
        button_frame = tk.Frame(left_frame, bg=self.theme_manager.get_color("surface"))
        button_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        self.btn_update = ModernButton(
            button_frame,
            text="執行 RNG",
            icon=IconManager.get('update'),
            button_type="primary",
            command=self.update_rng
        )
        self.btn_update.pack(side="left", padx=PADDING)

        self.btn_clear = ModernButton(
            button_frame,
            text="清除",
            icon=IconManager.get('delete'),
            button_type="danger",
            command=self.clear_all
        )
        self.btn_clear.pack(side="left", padx=PADDING)

        # ===== 右側日誌區域 =====
        # 日誌區域 - 使用卡片式設計
        log_card = CardFrame(right_frame, title="操作日誌與說明", icon=IconManager.get('comment'))
        log_card.pack(fill=tk.BOTH, expand=True, padx=PADDING, pady=PADDING)
        log_frame = log_card.get_content_frame()

        # 使用 ScrolledText 而非普通的 Text
        from tkinter.scrolledtext import ScrolledText
        self.log_text = ScrolledText(log_frame, width=40, height=20, font=("Microsoft JhengHei UI", 10))
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 初始日誌內容
        self.log_text.insert(tk.END, "👋 歡迎使用 Slot Set RNG 工具\n")
        self.log_text.insert(tk.END, "⚠️ 請先匯入 RNG 檔案，再進行設置操作\n")
        self.log_text.insert(tk.END, "📝 操作流程：\n")
        self.log_text.insert(tk.END, "  1. Git 下載 / 匯入 RNG 檔案 → 2. 選擇遊戲與 RNG → 3. 查詢會員 → 4. 執行 RNG\n")

        # 清除日誌按鈕
        self.btn_clear_log = ModernButton(
            log_frame,
            text="清除日誌",
            icon=IconManager.get('delete'),
            button_type="secondary",
            command=lambda: self.log_text.delete(1.0, tk.END)
        )
        self.btn_clear_log.pack(side=tk.RIGHT, padx=PADDING, pady=PADDING)

    def select_files(self) -> List[Path]:
        """選擇檔案"""
        file_paths = filedialog.askopenfilenames(
            title="選擇 RNG 檔案",
            filetypes=[("Excel files", "*.xlsx"), ("Excel files", "*.xls")]
        )
        return [Path(path) for path in file_paths]



    def update_description(self, text: str):
        """更新描述文字"""
        self.text_description.delete(1.0, tk.END)
        self.text_description.insert(1.0, text)

    def clear_all(self):
        """清除所有內容"""
        self.cb_game.set('')
        self.cb_game['values'] = []
        self.cb_rng.set('')
        self.cb_rng['values'] = []
        self.text_description.delete(1.0, tk.END)

    def clear_settings(self):
        """僅清除會員設定與遊戲設定，不清除已匯入的檔案"""
        # 清除會員設定
        self.entry_account.delete(0, tk.END)
        self.entry_member_id.delete(0, tk.END)

        # 清除遊戲設定
        self.cb_game.set('')
        self.cb_rng.set('')
        self.text_description.delete(1.0, tk.END)

        # 記錄日誌
        self.log_message("已清除會員設定與遊戲設定", level="INFO")

    def log(self, message):
        """記錄日誌"""
        try:
            # 檢查是否在主線程中
            import threading
            if threading.current_thread() is threading.main_thread():
                # 在主線程中，直接更新 UI
                self._do_log(message)
            else:
                # 在子線程中，將任務放入隊列
                self.ui_queue.put((self._do_log, (message,), {}))
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"記錄日誌失敗: {e}")

    def _do_log(self, message):
        """實際執行日誌記錄的方法"""
        try:
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)
            self.update_idletasks()
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"執行日誌記錄失敗: {e}")

    def log_message(self, message: str, level: str = "INFO"):
        """記錄訊息到日誌區域"""
        # 取得當前時間
        from datetime import datetime
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 根據日誌級別設定前綴
        if level == "ERROR":
            prefix = "❌ 錯誤"
        elif level == "WARNING":
            prefix = "⚠️ 警告"
        elif level == "SUCCESS":
            prefix = "✅ 成功"
        else:  # INFO
            prefix = "ℹ️ 資訊"

        # 格式化訊息並記錄
        log_entry = f"[{now}] {prefix}: {message}"
        self.log(log_entry)

    # 移除 _start_button_blink 方法，因為我們不再需要它

    def _blink_gitlab_button(self):
        """啟動 Git 下載按鈕閃爍效果"""
        # 創建閃爍狀態變數
        self._gitlab_blink_state = True
        self._gitlab_blink_count = 0

        # 定義閃爍函數
        def blink():
            # 切換閃爍狀態
            self._gitlab_blink_state = not self._gitlab_blink_state

            # 根據閃爍狀態設定按鈕樣式
            if self._gitlab_blink_state:
                # 使用主題管理器的顏色
                self.btn_import_gitlab.config(bg=self.theme_manager.get_color("accent"))
            else:
                # 恢復原始顏色
                style = self.theme_manager.get_button_style("info")
                self.btn_import_gitlab.config(bg=style.get("bg", "#17a2b8"))

            # 增加閃爍計數
            self._gitlab_blink_count += 1

            # 閃爍 5 次後停止
            if self._gitlab_blink_count < 10:  # 5 次完整閃爍需要 10 次狀態切換
                self.after(400, blink)  # 每 400 毫秒切換一次，比一般閃爍快

        # 開始閃爍
        blink()

    def show_error(self, message: str):
        """顯示錯誤訊息"""
        # 記錄到系統日誌
        import logging
        logging.getLogger(__name__).error(message)
        # 使用線程安全的方式記錄到 UI
        self.log_message(message, level="ERROR")

    def show_success(self, message: str):
        """顯示成功訊息"""
        # 記錄到系統日誌
        import logging
        logging.getLogger(__name__).info(f"成功: {message}")
        # 使用線程安全的方式記錄到 UI
        self.log_message(message, level="SUCCESS")

    def show_info(self, message: str):
        """顯示一般訊息"""
        # 記錄到系統日誌
        import logging
        logging.getLogger(__name__).info(message)
        # 使用線程安全的方式記錄到 UI
        self.log_message(message, level="INFO")

    def import_from_gitlab(self):
        """從 Git 下載 RNG 檔案"""
        # 這個方法將由控制器實現
        # 啟動按鈕閃爍效果
        self._blink_gitlab_button()
        # 記錄日誌
        self.log_message("正在從 Git 下載 RNG 檔案...", level="INFO")
        # 顯示確定性進度條
        self.show_progress_bar(determinate=True)
        self.update_progress_value(0)
        pass

    def show_progress_bar(self, determinate=False):
        """顯示進度條

        Args:
            determinate: 是否為確定性進度條（顯示百分比）
        """
        # 如果已經有進度條，先移除
        if hasattr(self, 'progress_frame'):
            self.progress_frame.destroy()

        # 創建進度條框架
        self.progress_frame = tk.Frame(self, bg=self.theme_manager.get_color("surface"))
        self.progress_frame.pack(fill="x", padx=PADDING, pady=PADDING)

        # 添加進度條標籤
        self.progress_label = tk.Label(
            self.progress_frame,
            text="正在下載 Git 儲存庫...",
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        )
        self.progress_label.pack(side="top", anchor="w", padx=PADDING, pady=(PADDING, 0))

        # 創建進度條框架和百分比標籤的容器
        progress_container = tk.Frame(self.progress_frame, bg=self.theme_manager.get_color("surface"))
        progress_container.pack(side="top", fill="x", padx=PADDING, pady=(0, PADDING))

        # 添加進度條
        mode = "determinate" if determinate else "indeterminate"
        self.progress_bar = ttk.Progressbar(
            progress_container,
            orient="horizontal",
            length=300,
            mode=mode
        )
        self.progress_bar.pack(side="left", fill="x", expand=True)

        # 添加百分比標籤
        self.progress_percent = tk.Label(
            progress_container,
            text="0%",
            width=5,
            bg=self.theme_manager.get_color("surface"),
            fg=self.theme_manager.get_color("text_primary")
        )

        # 只有在確定性模式下才顯示百分比標籤
        if determinate:
            self.progress_percent.pack(side="right", padx=(5, 0))

        # 啟動進度條
        if not determinate:
            self.progress_bar.start(15)  # 每 15 毫秒更新一次

    def hide_progress_bar(self):
        """隱藏進度條"""
        try:
            # 檢查是否在主線程中
            import threading
            if threading.current_thread() is threading.main_thread():
                # 在主線程中，直接更新 UI
                self._do_hide_progress_bar()
            else:
                # 在子線程中，將任務放入隊列
                self.ui_queue.put((self._do_hide_progress_bar, (), {}))
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"隱藏進度條失敗: {e}")

    def _do_hide_progress_bar(self):
        """實際執行隱藏進度條的方法"""
        try:
            if hasattr(self, 'progress_frame'):
                # 停止進度條
                self.progress_bar.stop()
                # 移除進度條框架
                self.progress_frame.destroy()
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"執行隱藏進度條失敗: {e}")

    def _process_ui_queue(self):
        """處理 UI 更新隊列"""
        try:
            # 嘗試從隊列中獲取任務
            while not self.ui_queue.empty():
                task = self.ui_queue.get(block=False)
                if task:
                    func, args, kwargs = task
                    try:
                        func(*args, **kwargs)
                    except Exception as e:
                        # 記錄到系統日誌
                        import logging
                        logging.getLogger(__name__).error(f"執行 UI 任務失敗: {e}")
                self.ui_queue.task_done()
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"處理 UI 隊列失敗: {e}")
        finally:
            # 安排下一次處理
            if self.is_ui_processor_running:
                self.after(100, self._process_ui_queue)

    def update_progress_label(self, text):
        """更新進度條標籤"""
        try:
            # 檢查是否在主線程中
            import threading
            if threading.current_thread() is threading.main_thread():
                # 在主線程中，直接更新 UI
                self._do_update_progress_label(text)
            else:
                # 在子線程中，將任務放入隊列
                self.ui_queue.put((self._do_update_progress_label, (text,), {}))
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"更新進度標籤失敗: {e}")

    def _do_update_progress_label(self, text):
        """實際執行更新進度標籤的方法"""
        try:
            if hasattr(self, 'progress_label'):
                self.progress_label.config(text=text)
                self.update_idletasks()  # 強制更新 UI
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"執行更新進度標籤失敗: {e}")

    def update_progress_value(self, value):
        """更新進度條的值（百分比）

        Args:
            value: 進度值（0-100）
        """
        try:
            # 檢查是否在主線程中
            import threading
            if threading.current_thread() is threading.main_thread():
                # 在主線程中，直接更新 UI
                self._do_update_progress_value(value)
            else:
                # 在子線程中，將任務放入隊列
                self.ui_queue.put((self._do_update_progress_value, (value,), {}))
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"更新進度值失敗: {e}")

    def _do_update_progress_value(self, value):
        """實際執行更新進度值的方法"""
        try:
            if hasattr(self, 'progress_bar') and hasattr(self, 'progress_percent'):
                # 確保值在 0-100 範圍內
                value = max(0, min(100, value))
                # 更新進度條
                self.progress_bar['value'] = value
                # 更新百分比標籤
                self.progress_percent.config(text=f"{value}%")
                self.update_idletasks()  # 強制更新 UI
        except Exception as e:
            # 記錄到系統日誌
            import logging
            logging.getLogger(__name__).error(f"執行更新進度值失敗: {e}")

    def update_rng(self):
        """更新 RNG 設置"""
        # 這個方法將由控制器實現
        pass

    def query_member(self):
        """查詢會員資訊"""
        # 這個方法將由控制器實現
        # 記錄日誌
        self.log_message("正在查詢會員資訊...", level="INFO")
        pass
