"""
使用 cx_Freeze 打包 VP Test Tool 為 exe 文件
簡化版本，避免被防毒軟體誤判為病毒
"""
import sys
import os
from cx_Freeze import setup, Executable

# 應用程式信息
APP_NAME = "VP_Test_Tool"
APP_VERSION = "2.6.1"
APP_DESCRIPTION = "VP Test Tool"
APP_AUTHOR = "VP Test Tool Team"

# 圖示路徑
icon_path = os.path.abspath(os.path.join("assets", "icons", "vp_test_tool.ico"))
if not os.path.exists(icon_path):
    print(f"Warning: Icon file not found at {icon_path}")
    icon_path = None

# 基本設置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 使用 GUI 模式，不顯示控制台窗口

# 包含的文件和目錄
include_files = [
    ("assets", "assets"),  # 包含資源文件
    ("config.json", "config.json"),  # 包含配置文件
    ("CHANGELOG.md", "CHANGELOG.md"),  # 包含更新日誌
]

# 構建選項
build_options = {
    "build_exe": "dist/cx_freeze_simple",  # 輸出目錄
    "include_files": include_files,  # 包含的文件和目錄
}

# 可執行文件選項
executables = [
    Executable(
        "main.py",  # 主程序文件
        base=base,
        target_name=APP_NAME,  # 輸出文件名
        icon=icon_path,  # 圖示
    )
]

# 設置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_options},
    executables=executables
)

print(f"打包完成！exe 文件位於: {os.path.abspath(os.path.join('dist', 'cx_freeze_simple', APP_NAME + '.exe'))}")
