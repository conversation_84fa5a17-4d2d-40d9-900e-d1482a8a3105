"""UI 樣式定義

此模組提供統一的 UI 樣式定義，用於確保所有頁面使用一致的設計風格。
"""
from typing import Dict, Any
from .theme import ThemeManager

# 初始化主題管理器
theme_manager = ThemeManager()

# 元件間距
PADDING = {
    "small": 5,    # 小間距，用於相關元素之間
    "medium": 10,  # 中間距，用於一般元素之間
    "large": 15,   # 大間距，用於不相關元素之間
    "xlarge": 20   # 特大間距，用於區塊之間
}

# 元件大小
SIZES = {
    "button": {
        "width": 12,     # 按鈕寬度
        "height": 1      # 按鈕高度
    },
    "entry": {
        "width": 20      # 輸入框寬度
    },
    "combobox": {
        "width": 15      # 下拉選單寬度
    },
    "text": {
        "width": 40,     # 文字區域寬度
        "height": 10     # 文字區域高度
    }
}

# 字體設定
FONTS = {
    "title": theme_manager.get_font("title"),
    "subtitle": theme_manager.get_font("subtitle"),
    "label": theme_manager.get_font("label"),
    "text": theme_manager.get_font("text"),
    "small": theme_manager.get_font("small"),
    "code": theme_manager.get_font("code")
}

# 顏色設定
COLORS = {
    "primary": theme_manager.get_color("primary"),
    "secondary": theme_manager.get_color("secondary"),
    "accent": theme_manager.get_color("accent"),
    "danger": theme_manager.get_color("danger"),
    "warning": theme_manager.get_color("warning"),
    "info": theme_manager.get_color("info"),
    "success": theme_manager.get_color("success"),
    "background": theme_manager.get_color("background"),
    "background_secondary": theme_manager.get_color("background_secondary"),  # 添加次要背景色
    "surface": theme_manager.get_color("surface"),
    "text_primary": theme_manager.get_color("text_primary"),
    "text_secondary": theme_manager.get_color("text_secondary"),
    "border": theme_manager.get_color("border"),
    "hover": theme_manager.get_color("hover"),
    "active": theme_manager.get_color("active"),
    "disabled": theme_manager.get_color("disabled")
}

# 按鈕樣式
BUTTON_STYLES = {
    "primary": theme_manager.get_button_style("primary"),
    "secondary": theme_manager.get_button_style("secondary"),
    "danger": theme_manager.get_button_style("danger"),
    "info": theme_manager.get_button_style("info"),
    "warning": theme_manager.get_button_style("warning"),
    "success": theme_manager.get_button_style("success")
}

# 卡片樣式
CARD_STYLE = {
    "background": COLORS["surface"],
    "border_color": COLORS["border"],
    "border_width": 1,
    "relief": "solid",
    "padding": PADDING["medium"]
}

# 表單樣式
FORM_STYLE = {
    "label_width": 15,
    "entry_width": 25,
    "padding": PADDING["medium"],
    "row_padding": PADDING["small"]
}

# 表格樣式
TABLE_STYLE = {
    "header_background": COLORS["primary"],
    "header_foreground": "#FFFFFF",
    "row_height": 25,
    "alternate_row_color": "#F5F5F5"
}

# 錯誤提示樣式
ERROR_STYLE = {
    "background": "#FFEBEE",  # 淺紅色背景
    "foreground": COLORS["danger"],
    "border_color": COLORS["danger"],
    "border_width": 1,
    "padding": PADDING["medium"],
    "font": FONTS["small"]
}

# 成功提示樣式
SUCCESS_STYLE = {
    "background": "#E8F5E9",  # 淺綠色背景
    "foreground": COLORS["success"],
    "border_color": COLORS["success"],
    "border_width": 1,
    "padding": PADDING["medium"],
    "font": FONTS["small"]
}

# 警告提示樣式
WARNING_STYLE = {
    "background": "#FFF8E1",  # 淺黃色背景
    "foreground": COLORS["warning"],
    "border_color": COLORS["warning"],
    "border_width": 1,
    "padding": PADDING["medium"],
    "font": FONTS["small"]
}

# 資訊提示樣式
INFO_STYLE = {
    "background": "#E3F2FD",  # 淺藍色背景
    "foreground": COLORS["info"],
    "border_color": COLORS["info"],
    "border_width": 1,
    "padding": PADDING["medium"],
    "font": FONTS["small"]
}

# 日誌樣式
LOG_STYLE = {
    "background": COLORS["surface"],
    "foreground": COLORS["text_primary"],
    "font": FONTS["code"],
    "padding": PADDING["small"]
}

# 狀態列樣式
STATUS_BAR_STYLE = {
    "background": COLORS["background_secondary"],  # 使用次要背景色
    "foreground": COLORS["text_primary"],
    "font": FONTS["small"],
    "padding": PADDING["small"]
}

# 工具列樣式
TOOLBAR_STYLE = {
    "background": COLORS["background"],
    "padding": PADDING["medium"]
}

# 頁籤樣式
TAB_STYLE = {
    "padding": PADDING["medium"],
    "font": FONTS["label"]
}

# 會員資訊區域樣式
MEMBER_INFO_STYLE = {
    "background": COLORS["surface"],
    "border_color": COLORS["border"],
    "border_width": 1,
    "relief": "solid",
    "padding": PADDING["medium"],
    "label_width": 15,
    "entry_width": 20,
    "row_padding": PADDING["small"]
}

# 資源設定區域樣式
RESOURCE_SETTING_STYLE = {
    "background": COLORS["surface"],
    "border_color": COLORS["border"],
    "border_width": 1,
    "relief": "solid",
    "padding": PADDING["medium"],
    "label_width": 15,
    "entry_width": 20,
    "row_padding": PADDING["small"]
}

# 批次處理區域樣式
BATCH_PROCESSING_STYLE = {
    "background": COLORS["surface"],
    "border_color": COLORS["border"],
    "border_width": 1,
    "relief": "solid",
    "padding": PADDING["medium"],
    "label_width": 15,
    "entry_width": 20,
    "row_padding": PADDING["small"]
}

def get_style(style_name: str) -> Dict[str, Any]:
    """取得指定樣式

    Args:
        style_name: 樣式名稱

    Returns:
        Dict[str, Any]: 樣式設定
    """
    styles = {
        "card": CARD_STYLE,
        "form": FORM_STYLE,
        "table": TABLE_STYLE,
        "error": ERROR_STYLE,
        "success": SUCCESS_STYLE,
        "warning": WARNING_STYLE,
        "info": INFO_STYLE,
        "log": LOG_STYLE,
        "status_bar": STATUS_BAR_STYLE,
        "toolbar": TOOLBAR_STYLE,
        "tab": TAB_STYLE,
        "member_info": MEMBER_INFO_STYLE,
        "resource_setting": RESOURCE_SETTING_STYLE,
        "batch_processing": BATCH_PROCESSING_STYLE
    }
    return styles.get(style_name, {})
