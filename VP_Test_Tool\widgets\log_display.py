"""日誌顯示元件"""
import tkinter as tk
from tkinter.scrolledtext import ScrolledText
import datetime
from typing import Optional
from utils.theme import ThemeManager

class LogDisplay(ScrolledText):
    """增強型日誌顯示元件
    
    提供更豐富的日誌顯示功能，支援不同類型的日誌訊息和時間戳記。
    
    Args:
        parent: 父元件
        **kwargs: 其他 ScrolledText 參數
    """
    
    def __init__(self, parent, **kwargs):
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定預設樣式
        default_style = {
            "font": self.theme_manager.get_font("code"),
            "bg": self.theme_manager.get_color("surface"),
            "fg": self.theme_manager.get_color("text_primary"),
            "wrap": tk.WORD,
            "padx": 5,
            "pady": 5
        }
        
        # 合併樣式和其他參數
        text_kwargs = {**default_style, **kwargs}
        
        super().__init__(parent, **text_kwargs)
        
        # 設定唯讀
        self.config(state=tk.DISABLED)
        
        # 設定文字標籤
        self._setup_tags()
        
    def _setup_tags(self):
        """設定文字標籤"""
        self.tag_configure(
            "info", 
            foreground=self.theme_manager.get_color("info"),
            font=self.theme_manager.get_font("code")
        )
        
        self.tag_configure(
            "success", 
            foreground=self.theme_manager.get_color("success"),
            font=self.theme_manager.get_font("code")
        )
        
        self.tag_configure(
            "warning", 
            foreground=self.theme_manager.get_color("warning"),
            font=self.theme_manager.get_font("code")
        )
        
        self.tag_configure(
            "error", 
            foreground=self.theme_manager.get_color("danger"),
            font=self.theme_manager.get_font("code")
        )
        
        self.tag_configure(
            "timestamp", 
            foreground=self.theme_manager.get_color("text_secondary"),
            font=("Consolas", 8)
        )
        
    def log(self, message: str, level: str = "info"):
        """記錄日誌訊息
        
        Args:
            message: 日誌訊息
            level: 日誌等級，可以是 'info', 'success', 'warning', 'error'
        """
        # 取得時間戳記
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 取得圖示
        icons = {
            "info": "ℹ️ ",
            "success": "✅ ",
            "warning": "⚠️ ",
            "error": "❌ "
        }
        
        # 啟用編輯
        self.config(state=tk.NORMAL)
        
        # 插入時間戳記
        self.insert(tk.END, f"[{timestamp}] ", "timestamp")
        
        # 插入訊息
        self.insert(tk.END, f"{icons.get(level, '')} {message}\n", level)
        
        # 滾動到最新訊息
        self.see(tk.END)
        
        # 禁用編輯
        self.config(state=tk.DISABLED)
        
    def clear(self):
        """清除所有日誌"""
        # 啟用編輯
        self.config(state=tk.NORMAL)
        
        # 清除所有內容
        self.delete(1.0, tk.END)
        
        # 禁用編輯
        self.config(state=tk.DISABLED)
