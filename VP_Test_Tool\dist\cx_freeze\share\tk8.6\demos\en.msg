::msgcat::mcset en "Widget Demonstration"
::msgcat::mcset en "tkWidgetDemo"
::msgcat::mcset en "&File"
::msgcat::mcset en "About..."
::msgcat::mcset en "&About..."
::msgcat::mcset en "<F1>"
::msgcat::mcset en "&Quit"
::msgcat::mcset en "Meta+Q"		;# Displayed hotkey
::msgcat::mcset en "Meta-q"		;# Actual binding sequence
::msgcat::mcset en "Ctrl+Q"		;# Displayed hotkey
::msgcat::mcset en "Control-q"		;# Actual binding sequence
::msgcat::mcset en "Variable values"
::msgcat::mcset en "Variable values:"
::msgcat::mcset en "OK"
::msgcat::mcset en "Run the \"%s\" sample program"
::msgcat::mcset en "Dismiss"
::msgcat::mcset en "Rerun Demo"
::msgcat::mcset en "Demo code: %s"
::msgcat::mcset en "About Widget Demo"
::msgcat::mcset en "Tk widget demonstration application"
::msgcat::mcset en "Copyright © %s"
::msgcat::mcset en "
    @@title
    Tk Widget Demonstrations
    @@newline
    @@normal
    @@newline

    This application provides a front end for several short scripts
    that demonstrate what you can do with Tk widgets.  Each of the
    numbered lines below describes a demonstration;  you can click on
    it to invoke the demonstration.  Once the demonstration window
    appears, you can click the
    @@bold
    See Code
    @@normal
    button to see the Tcl/Tk code that created the demonstration.  If
    you wish, you can edit the code and click the
    @@bold
    Rerun Demo
    @@normal
    button in the code window to reinvoke the demonstration with the
    modified code.
    @@newline
"
::msgcat::mcset en "Labels, buttons, checkbuttons, and radiobuttons"
::msgcat::mcset en "Labels (text and bitmaps)"
::msgcat::mcset en "Labels and UNICODE text"
::msgcat::mcset en "Buttons"
::msgcat::mcset en "Check-buttons (select any of a group)"
::msgcat::mcset en "Radio-buttons (select one of a group)"
::msgcat::mcset en "A 15-puzzle game made out of buttons"
::msgcat::mcset en "Iconic buttons that use bitmaps"
::msgcat::mcset en "Two labels displaying images"
::msgcat::mcset en "A simple user interface for viewing images"
::msgcat::mcset en "Labelled frames"
::msgcat::mcset en "Listboxes"
::msgcat::mcset en "The 50 states"
::msgcat::mcset en "Colors: change the color scheme for the application"
::msgcat::mcset en "A collection of famous and infamous sayings"
::msgcat::mcset en "Entries and Spin-boxes"
::msgcat::mcset en "Entries without scrollbars"
::msgcat::mcset en "Entries with scrollbars"
::msgcat::mcset en "Validated entries and password fields"
::msgcat::mcset en "Spin-boxes"
::msgcat::mcset en "Simple Rolodex-like form"
::msgcat::mcset en "Text"
::msgcat::mcset en "Basic editable text"
::msgcat::mcset en "Text display styles"
::msgcat::mcset en "Hypertext (tag bindings)"
::msgcat::mcset en "A text widget with embedded windows"
::msgcat::mcset en "A search tool built with a text widget"
::msgcat::mcset en "Canvases"
::msgcat::mcset en "The canvas item types"
::msgcat::mcset en "A simple 2-D plot"
::msgcat::mcset en "Text items in canvases"
::msgcat::mcset en "An editor for arrowheads on canvas lines"
::msgcat::mcset en "A ruler with adjustable tab stops"
::msgcat::mcset en "A building floor plan"
::msgcat::mcset en "A simple scrollable canvas"
::msgcat::mcset en "Scales"
::msgcat::mcset en "Horizontal scale"
::msgcat::mcset en "Vertical scale"
::msgcat::mcset en "Paned Windows"
::msgcat::mcset en "Horizontal paned window"
::msgcat::mcset en "Vertical paned window"
::msgcat::mcset en "Menus"
::msgcat::mcset en "Menus and cascades (sub-menus)"
::msgcat::mcset en "Menu-buttons"
::msgcat::mcset en "Common Dialogs"
::msgcat::mcset en "Message boxes"
::msgcat::mcset en "File selection dialog"
::msgcat::mcset en "Color picker"
::msgcat::mcset en "Miscellaneous"
::msgcat::mcset en "The built-in bitmaps"
::msgcat::mcset en "A dialog box with a local grab"
::msgcat::mcset en "A dialog box with a global grab"
