"""圖示管理器

此模組提供應用程式的圖示管理功能，包含常用圖示的定義。
"""
from typing import Dict, Optional

class IconManager:
    """圖示管理器類別

    負責管理應用程式的圖示，提供統一的圖示存取介面。
    目前使用 Unicode 字元作為圖示，未來可擴充為支援圖片檔案。
    """

    # 常用圖示的 Unicode 字元
    ICONS = {
        # 操作圖示
        "search": "🔍",
        "add": "➕",
        "delete": "🗑️",
        "edit": "✏️",
        "save": "💾",
        "refresh": "🔄",
        "update": "🔄",
        "settings": "⚙️",
        "download": "⬇️",
        "upload": "⬆️",
        "import": "📥",
        "export": "📤",
        "copy": "📋",
        "paste": "📄",
        "cut": "✂️",
        "undo": "↩️",
        "redo": "↪️",
        "print": "🖨️",
        "share": "📤",
        "filter": "🔍",
        "sort": "↕️",
        "unlock": "🔓",
        "pin": "📌",
        "unpin": "📍",
        "favorite": "⭐",
        "unfavorite": "☆",
        "bookmark": "🔖",
        "unbookmark": "📑",
        "attach": "📎",
        "detach": "🧷",
        "zoom_in": "🔍",
        "zoom_out": "🔎",
        "fullscreen": "⛶",
        "exit_fullscreen": "⛶",
        "close": "❌",
        "minimize": "🗕",
        "maximize": "🗖",
        "restore": "🗗",
        "ok": "✓",
        "cancel": "✗",
        "apply": "✓",
        "reset": "↺",
        "clear": "🧹",
        "back": "⬅️",
        "forward": "➡️",
        "up": "⬆️",
        "down": "⬇️",
        "left": "⬅️",
        "right": "➡️",
        "menu": "☰",
        "more": "⋮",
        "more_horiz": "⋯",
        "drag": "⠿",
        "move": "↔️",
        "resize": "⤡",
        "rotate": "↻",
        "flip": "⇄",
        "expand": "⤢",
        "collapse": "⤫",
        "show": "👁️",
        "hide": "👁️‍🗨️",
        "visible": "👁️",
        "invisible": "👁️‍🗨️",
        "lock": "🔒",

        # 狀態圖示
        "info": "ℹ️",
        "warning": "⚠️",
        "error": "❌",
        "success": "✅",
        "help": "❓",
        "question": "❔",
        "loading": "⏳",
        "sync": "🔄",
        "offline": "📴",
        "online": "📶",
        "notification": "🔔",
        "mute": "🔕",
        "volume": "🔊",
        "mute_volume": "🔇",

        # 內容圖示
        "user": "👤",
        "users": "👥",
        "game": "🎮",
        "dice": "🎲",
        "calendar": "📅",
        "clock": "🕒",
        "home": "🏠",
        "mail": "📧",
        "phone": "📱",
        "location": "📍",
        "folder": "📁",
        "file": "📄",
        "image": "🖼️",
        "video": "🎬",
        "audio": "🎵",
        "document": "📃",
        "spreadsheet": "📊",
        "presentation": "📊",
        "chart": "📈",
        "graph": "📉",
        "table": "🗃️",
        "list": "📋",
        "check": "✓",
        "checkbox": "☑️",
        "radio": "⚪",
        "star": "⭐",
        "heart": "❤️",
        "like": "👍",
        "dislike": "👎",
        "comment": "💬",
        "chat": "💭",
        "tag": "🏷️",
        "money": "💰",
        "credit_card": "💳",
        "cart": "🛒",
        "shop": "🛍️",
        "truck": "🚚",
        "globe": "🌐",
        "cloud": "☁️",
        "database": "🗄️",
        "server": "🖥️",
        "wifi": "📶",
        "bluetooth": "📶",
        "battery": "🔋",
        "power": "⚡",
        "light": "💡",
        "dark": "🌙",
        "temperature": "🌡️",
        "weather": "☀️",
        "rain": "🌧️",
        "snow": "❄️",
        "wind": "💨",
        "fire": "🔥",
        "water": "💧",
        "earth": "🌍",
        "moon": "🌙",
        "sun": "☀️",
        "time": "⏰",
        "alarm": "⏰",
        "stopwatch": "⏱️",
        "hourglass": "⌛",
        "compass": "🧭",
        "map": "🗺️",
        "world": "🌎",
        "flag": "🚩",
        "link": "🔗",
        "paperclip": "📎",
        "scissors": "✂️",
        "key": "🔑",
        "unlock": "🔓",
        "shield": "🛡️",
        "crown": "👑",
        "trophy": "🏆",
        "medal": "🏅",
        "ribbon": "🎀",
        "gift": "🎁",
        "cake": "🎂",
        "party": "🎉",
        "balloon": "🎈",
        "puzzle": "🧩",
        "chess": "♟️",
        "cards": "🃏",
        "sports": "🏀",
        "football": "⚽",
        "baseball": "⚾",
        "tennis": "🎾",
        "basketball": "🏀",
        "golf": "⛳",
        "racing": "🏎️",
        "bicycle": "🚲",
        "motorcycle": "🏍️",
        "car": "🚗",
        "bus": "🚌",
        "train": "🚆",
        "airplane": "✈️",
        "rocket": "🚀",
        "satellite": "🛰️",
        "ufo": "🛸",
        "alien": "👽",
        "robot": "🤖",
        "ghost": "👻",
        "skull": "💀",
        "poop": "💩",
        "monkey": "🐵",
        "dog": "🐶",
        "cat": "🐱",
        "mouse": "🐭",
        "hamster": "🐹",
        "rabbit": "🐰",
        "fox": "🦊",
        "bear": "🐻",
        "panda": "🐼",
        "koala": "🐨",
        "tiger": "🐯",
        "lion": "🦁",
        "cow": "🐮",
        "pig": "🐷",
        "frog": "🐸",
        "chicken": "🐔",
        "penguin": "🐧",
        "bird": "🐦",
        "child": "🧒",
        "boy": "👦",
        "girl": "👧",
        "man": "👨",
        "woman": "👩",
        "person": "🧑",
        "older_person": "🧓",
        "cop": "👮",
        "detective": "🕵️",
        "guard": "💂",
        "construction_worker": "👷",
        "prince": "🤴",
        "princess": "👸",
        "person_with_turban": "👳",
        "person_with_veil": "👰",
        "pregnant_woman": "🤰",
        "breast_feeding": "🤱",
        "angel": "👼",
        "santa": "🎅",
        "superhero": "🦸",
        "supervillain": "🦹",
        "mage": "🧙",
        "fairy": "🧚",
        "vampire": "🧛",
        "merperson": "🧜",
        "elf": "🧝",
        "genie": "🧞",
        "zombie": "🧟",
        "person_getting_massage": "💆",
        "person_getting_haircut": "💇",
        "person_in_steamy_room": "🧖",
        "person_climbing": "🧗",
        "person_in_lotus_position": "🧘",
        "person_taking_bath": "🛀",
        "person_in_bed": "🛌",

        # 特殊圖示
        "empty": "",
        "space": " ",
        "newline": "\n",
        "tab": "\t",
        "copyright": "©️",
        "registered": "®️",
        "trademark": "™️",
        "degree": "°",
        "plus_minus": "±",
        "infinity": "∞",
        "pi": "π",
        "micro": "µ",
        "ohm": "Ω",
        "sum": "∑",
        "product": "∏",
        "partial": "∂",
        "integral": "∫",
        "not_equal": "≠",
        "approx": "≈",
        "less_equal": "≤",
        "greater_equal": "≥",
        "division": "÷",
        "multiplication": "×",
        "square_root": "√",
        "cube_root": "∛",
        "fourth_root": "∜",
        "empty_set": "∅",
        "intersection": "∩",
        "union": "∪",
        "element": "∈",
        "not_element": "∉",
        "subset": "⊂",
        "superset": "⊃",
        "subset_equal": "⊆",
        "superset_equal": "⊇",
        "circled_plus": "⊕",
        "circled_minus": "⊖",
        "circled_times": "⊗",
        "circled_division": "⊘",
        "logical_and": "∧",
        "logical_or": "∨",
        "logical_not": "¬",
        "implies": "⇒",
        "if_and_only_if": "⇔",
        "therefore": "∴",
        "because": "∵",
        "all": "∀",
        "exists": "∃",
        "not_exists": "∄",
        "end_of_proof": "∎",
        "alef": "ℵ",
        "beth": "ℶ",
        "gimel": "ℷ",
        "daleth": "ℸ",
    }

    @classmethod
    def get(cls, name: str, fallback: Optional[str] = None) -> str:
        """取得圖示

        Args:
            name: 圖示名稱
            fallback: 找不到圖示時的預設值

        Returns:
            str: 圖示字元
        """
        return cls.ICONS.get(name, fallback if fallback is not None else name)

    @classmethod
    def get_all(cls) -> Dict[str, str]:
        """取得所有圖示

        Returns:
            Dict[str, str]: 所有圖示的字典
        """
        return cls.ICONS.copy()
