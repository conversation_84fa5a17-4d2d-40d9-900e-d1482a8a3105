"""API 錯誤處理模組"""
import logging
from typing import Dict, Any, Optional, Tuple, List
import json

logger = logging.getLogger(__name__)

class ApiErrorHandler:
    """API 錯誤處理類，用於處理不同 API 的特定錯誤"""

    @staticmethod
    def handle_mysql_operator_error(response: Dict[str, Any]) -> Tuple[bool, str]:
        """處理 MySQL 操作 API 的錯誤

        Args:
            response: API 回應

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        if not response:
            return False, "API 回應為空"

        if not response.get("success", False):
            error_msg = response.get("error", "未知錯誤")
            if "Access denied" in error_msg:
                return False, f"資料庫存取被拒絕: {error_msg}"
            elif "Unknown database" in error_msg:
                return False, f"資料庫不存在: {error_msg}"
            elif "Table" in error_msg and "doesn't exist" in error_msg:
                return False, f"資料表不存在: {error_msg}"
            elif "Duplicate entry" in error_msg:
                return False, f"資料重複: {error_msg}"
            else:
                return False, f"資料庫操作失敗: {error_msg}"

        if not response.get("data"):
            return False, "查詢結果為空"

        return True, ""

    @staticmethod
    def handle_resource_update_error(response: Dict[str, Any], resource_type: str) -> Tuple[bool, str]:
        """處理資源更新 API 的錯誤

        Args:
            response: API 回應
            resource_type: 資源類型 (coin, vip, gem, lottery)

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        if not response:
            return False, f"更新{resource_type}失敗: API 回應為空"

        # 檢查金幣更新 API 的特殊回應格式
        if resource_type == "金幣" and "StatusMsg" in response:
            if response.get("StatusMsg") == "Success":
                return True, ""
            else:
                error_msg = response.get("StatusMsg", "未知錯誤")
                return False, f"更新{resource_type}失敗: {error_msg}"

        # 檢查回應格式
        if "success" in response and not response["success"]:
            error_msg = response.get("error", "未知錯誤")
            return False, f"更新{resource_type}失敗: {error_msg}"

        # 檢查 retStatus
        if "retStatus" in response:
            status_code = response["retStatus"].get("StatusCode")
            status_msg = response["retStatus"].get("StatusMsg", "")

            if status_code != 10000:
                if status_code == 10001:
                    return False, f"更新{resource_type}失敗: 參數錯誤 ({status_msg})"
                elif status_code == 10002:
                    return False, f"更新{resource_type}失敗: 會員不存在 ({status_msg})"
                elif status_code == 10003:
                    return False, f"更新{resource_type}失敗: 餘額不足 ({status_msg})"
                elif status_code == 10004:
                    return False, f"更新{resource_type}失敗: 資源不存在 ({status_msg})"
                else:
                    return False, f"更新{resource_type}失敗: 錯誤碼 {status_code} ({status_msg})"
            else:
                # 狀態碼為 10000 表示成功
                return True, ""

        # 如果沒有明確的成功標誌，但也沒有錯誤，則視為成功
        return True, ""

    @staticmethod
    def handle_member_query_error(response: Dict[str, Any], account: str = "", member_id: str = "") -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """處理會員查詢 API 的錯誤

        Args:
            response: API 回應
            account: 會員帳號
            member_id: 會員ID

        Returns:
            Tuple[bool, str, Optional[Dict[str, Any]]]: 是否成功、錯誤訊息和會員資料
        """
        if not response:
            return False, "API 回應為空", None

        if not response.get("success", False):
            error_msg = response.get("error", "未知錯誤")
            return False, f"查詢會員失敗: {error_msg}", None

        if not response.get("data"):
            query_target = account if account else member_id
            return False, f"查無會員: {query_target}", None

        # 成功查詢到會員
        return True, "", response["data"][0]

    @staticmethod
    def handle_agent_query_error(response: Dict[str, Any]) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """處理代理商查詢 API 的錯誤

        Args:
            response: API 回應

        Returns:
            Tuple[bool, str, List[Dict[str, Any]]]: 是否成功、錯誤訊息和代理商列表
        """
        if not response:
            return False, "API 回應為空", []

        # 檢查新 API 格式
        if "code" in response:
            if response["code"] != 0:
                error_msg = response.get("msg", "未知錯誤")
                return False, f"查詢代理商失敗: {error_msg}", []

            if not response.get("data"):
                return False, "查無代理商", []

            return True, "", response["data"]

        # 檢查舊 API 格式
        if not response.get("success", False):
            error_msg = response.get("error", "未知錯誤")
            return False, f"查詢代理商失敗: {error_msg}", []

        if not response.get("data"):
            return False, "查無代理商", []

        return True, "", response["data"]

    @staticmethod
    def handle_rng_set_error(response: Dict[str, Any]) -> Tuple[bool, str]:
        """處理 RNG 設定 API 的錯誤

        Args:
            response: API 回應

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        if not response:
            return False, "API 回應為空"

        # 檢查 retStatus
        if "retStatus" in response:
            status_code = response["retStatus"].get("StatusCode")
            status_msg = response["retStatus"].get("StatusMsg", "")

            if status_code != 10000:
                if status_code == 10001:
                    return False, f"設定 RNG 失敗: 參數錯誤 ({status_msg})"
                elif status_code == 10002:
                    return False, f"設定 RNG 失敗: 會員不存在 ({status_msg})"
                elif status_code == 10003:
                    return False, f"設定 RNG 失敗: 遊戲不存在 ({status_msg})"
                elif status_code == 10004:
                    return False, f"設定 RNG 失敗: RNG 格式錯誤 ({status_msg})"
                else:
                    return False, f"設定 RNG 失敗: 錯誤碼 {status_code} ({status_msg})"

        # 如果沒有明確的成功標誌，但也沒有錯誤，則視為成功
        return True, ""

    @staticmethod
    def handle_create_member_error(response: Dict[str, Any]) -> Tuple[bool, str, Optional[str]]:
        """處理建立會員 API 的錯誤

        Args:
            response: API 回應

        Returns:
            Tuple[bool, str, Optional[str]]: 是否成功、錯誤訊息和會員ID
        """
        if not response:
            return False, "API 回應為空", None

        # 檢查回應中是否包含會員ID
        if "memberId" not in response:
            # 檢查 retStatus
            if "retStatus" in response:
                status_code = response["retStatus"].get("StatusCode")
                status_msg = response["retStatus"].get("StatusMsg", "")

                if status_code != 10000:
                    if status_code == 10001:
                        return False, f"建立會員失敗: 參數錯誤 ({status_msg})", None
                    elif status_code == 10002:
                        return False, f"建立會員失敗: 帳號已存在 ({status_msg})", None
                    else:
                        return False, f"建立會員失敗: 錯誤碼 {status_code} ({status_msg})", None

            return False, "建立會員失敗: 回應中無會員ID", None

        # 成功建立會員
        return True, "", response["memberId"]

    @staticmethod
    def handle_add_cards_error(response: Dict[str, Any]) -> Tuple[bool, str]:
        """處理新增卡片 API 的錯誤

        Args:
            response: API 回應

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        if not response:
            return False, "API 回應為空"

        # 檢查 retStatus
        if "retStatus" in response:
            status_code = response["retStatus"].get("StatusCode")
            status_msg = response["retStatus"].get("StatusMsg", "")

            if status_code != 10000:
                if status_code == 10001:
                    return False, f"新增卡片失敗: 參數錯誤 ({status_msg})"
                elif status_code == 10002:
                    return False, f"新增卡片失敗: 會員不存在 ({status_msg})"
                elif status_code == 10003:
                    return False, f"新增卡片失敗: 卡片不存在 ({status_msg})"
                else:
                    return False, f"新增卡片失敗: 錯誤碼 {status_code} ({status_msg})"

        # 如果沒有明確的成功標誌，但也沒有錯誤，則視為成功
        return True, ""

    @staticmethod
    def format_error_message(error_msg: str, api_name: str, response: Dict[str, Any]) -> str:
        """格式化錯誤訊息

        Args:
            error_msg: 錯誤訊息
            api_name: API 名稱
            response: API 回應

        Returns:
            str: 格式化後的錯誤訊息
        """
        try:
            response_str = json.dumps(response, ensure_ascii=False, indent=2)
            return f"{error_msg}\n\nAPI: {api_name}\n回應: {response_str}"
        except Exception as e:
            logger.error(f"格式化錯誤訊息失敗: {e}")
            return error_msg
