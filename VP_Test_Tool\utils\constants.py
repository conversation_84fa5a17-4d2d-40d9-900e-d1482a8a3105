"""常數定義"""
from .theme import ThemeManager
from .version import APP_TITLE, VERSION

# 應用程式資訊
# APP_TITLE 從 version.py 導入
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700

# UI 常數
PADDING = 15  # 增加內邊距以提供更好的視覺空間
SMALL_PADDING = 8  # 較小的內邊距，用於相關元素之間
LARGE_PADDING = 20  # 較大的內邊距，用於不相關元素之間

# 初始化主題管理器
theme_manager = ThemeManager()

# 字體設定 (從主題管理器取得)
FONT_TITLE = theme_manager.get_font("title")
FONT_LABEL = theme_manager.get_font("label")
FONT_TEXT = theme_manager.get_font("text")
FONT_CODE = theme_manager.get_font("code")

# 動態 API URLs - 使用環境配置管理器
def get_api_urls():
    """動態取得 API URLs，根據當前環境配置"""
    # 延遲導入避免循環依賴
    try:
        from .environment_config import env_config
    except ImportError:
        # 如果環境配置不可用，使用預設值
        env_config = None

    # 取得當前環境的伺服器配置
    if env_config:
        mysql_server = env_config.get_api_server("mysql_operator")
        gamebridge_server = env_config.get_api_server("gamebridge")
        tokenguard_server = env_config.get_api_server("tokenguard")
        lottery_server = env_config.get_api_server("lottery")
        simulation_server = env_config.get_api_server("simulation")
    else:
        # 預設配置
        mysql_server = "http://10.10.104.33:5000"
        gamebridge_server = "http://gamebridge:8080"
        tokenguard_server = "https://gp001-qa1-tokenguard.xwautc.online"
        lottery_server = "http://lottery:8080"
        simulation_server = "http://simulationweb-go:8080"

    return {
        # 資料庫操作 API
        "MYSQL_OPERATOR": f"{mysql_server}/tools/db/mysql-operator",  # MySQL 資料庫操作 API，支援 query/insert/update/delete 操作
                                                                      # 參數: action, serverTag, sql
                                                                      # 回應: {"success": true/false, "data": [...], "error": "錯誤訊息"}

        "QUERY_MEMBER": f"{mysql_server}/tools/db/mysql-operator",    # 會員查詢 API，與 MYSQL_OPERATOR 相同，用於查詢會員資訊
                                                                      # 參數: action="query", serverTag="igaming-mysql-main", sql="SELECT * FROM..."
                                                                      # 回應: {"success": true/false, "data": [...], "error": "錯誤訊息"}

        # 資源更新 API
        "UPDATE_COIN": f"{gamebridge_server}/private/modifyBalance",        # 更新會員金幣 API
                                                                            # 參數: {"agent": "代理商代碼", "account": "會員帳號", "newBalance": "新金幣數量"}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        "UPDATE_VIP": f"{tokenguard_server}/debug/common/ToolService/DebugUpdateMemberVip",  # 更新會員 VIP 等級 API
                                                                            # 參數: {"accountInfo": {...}, "debugUpdateVip": VIP等級}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        "UPDATE_GEM": f"{tokenguard_server}/debug/common/RewardService/RewardOperateRequest/RedeemRewardNode",  # 更新會員寶石 API
                                                                            # 參數: {"MemberId": 會員ID, "AccountInfo": {...}, "source": "來源", "description": "描述", "rewardNodeList": [...]}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        "UPDATE_LOTTERY": f"{lottery_server}/private/redeem",               # 更新會員樂透券 API
                                                                            # 參數: {"memberId": 會員ID, "service": "服務名稱", "rewardNode": [...]}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        # 會員相關 API
        "CREATE_MEMBER": f"{tokenguard_server}/debug/common/LoginService/OperateRequest/Login",  # 建立新會員 API
                                                                            # 參數: {"accountInfo": {"account": "帳號", "agentCode": "代理商代碼", "subAgentCode": "子代理商代碼", "currency": "貨幣"}, "gameId": "遊戲ID"}
                                                                            # 回應: {"memberId": "會員ID", ...}

        "ADD_CARDS": f"{tokenguard_server}/debug/common/RewardService/RewardOperateRequest/RedeemRewardNode",  # 新增卡片 API
                                                                            # 參數: {"MemberId": 會員ID, "AccountInfo": {...}, "source": "來源", "description": "描述", "rewardNodeList": [...]}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        # 代理商相關 API
        "QUERY_AGENTS": f"{simulation_server}/api/common/getAgentSimpleList",  # 查詢代理商列表 API
                                                                            # 參數: {}
                                                                            # 回應: {"code": 0, "data": [...], "msg": "success"}

        "QUERY_SUB_AGENTS": f"{simulation_server}/api/common/getSubAgentSimpleList",  # 查詢子代理商列表 API
                                                                            # 參數: {"AgentCode": "代理商代碼"}
                                                                            # 回應: {"code": 0, "data": [...], "msg": "success"}

        # RNG 相關 API
        "SET_RNG": f"{tokenguard_server}/debug/slotgame/DeveloperService/setOnceRng",  # 設定 RNG API
                                                                            # 參數: {"accountInfo": {...}, "gameId": "遊戲ID", "betModeId": 投注模式ID, "betLineId": 投注線ID, "rngList": [...]}
                                                                            # 回應: {"retStatus": {"StatusCode": 10000, "StatusMsg": "Success"}}

        # Gitlab API (這些通常不會變動，保持靜態)
        "GITLAB_API": "https://git-qa.yile808.com/api/v4",                    # Gitlab API URL，用於與 Gitlab API 交互
        "GITLAB_API_ALT": "https://git-qa.yile808.com",                       # 備用 Gitlab API URL
        "GITLAB_SSH_URL": "ssh://**********************:1222",                # SSH URL，用於 Git 操作
        "GITLAB_HTTPS_URL": "https://git-qa.yile808.com/igaming/igaming-web-tool.git",  # HTTPS URL，用於 Git 操作
        "GITLAB_PROJECT": "igaming%2Figaming-web-tool",                       # 使用 URL 編碼後的專案路徑
        "GITLAB_BRANCH": "master",                                            # 使用 master 分支而非 main
        "GITLAB_FOLDER": "data/slot_rng",                                     # Gitlab 專案中的資料夾路徑
        "GITLAB_TOKEN": "**************************"                          # Gitlab API 訪問令牌
    }

# 全域 API URLs 實例 - 使用動態配置
API_URLS = get_api_urls()

# 按鈕樣式 (從主題管理器取得)
BUTTON_PRIMARY = theme_manager.get_button_style("primary")
BUTTON_SECONDARY = theme_manager.get_button_style("secondary")
BUTTON_DANGER = theme_manager.get_button_style("danger")
BUTTON_INFO = theme_manager.get_button_style("info")
BUTTON_WARNING = theme_manager.get_button_style("warning")
BUTTON_SUCCESS = theme_manager.get_button_style("success")

# Excel 欄位名稱常數
COL_RNG_NAME = "RNGName"
COL_DESCRIPTION = "Description"
COL_RNG = "RNG"
