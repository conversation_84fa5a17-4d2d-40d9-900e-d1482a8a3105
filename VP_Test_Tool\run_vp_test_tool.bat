@echo off
echo VP Test Tool 啟動選項
echo ==========================================
echo 1. 正常模式 (main.py)
echo 2. 診斷模式 (debug_startup.py)
echo 3. 簡單模式 (simple_startup.py)
echo 4. 網絡測試模式 (network_test.py)
echo 5. 退出
echo ==========================================
set /p choice=請選擇啟動模式 (1-5):

if "%choice%"=="1" (
    echo 正在啟動正常模式...
    python main.py
) else if "%choice%"=="2" (
    echo 正在啟動診斷模式...
    python debug_startup.py
) else if "%choice%"=="3" (
    echo 正在啟動簡單模式...
    python simple_startup.py
) else if "%choice%"=="4" (
    echo 正在啟動網絡測試模式...
    python network_test.py
) else if "%choice%"=="5" (
    echo 退出程式
    exit
) else (
    echo 無效的選擇，請重新運行批處理文件
)

pause
