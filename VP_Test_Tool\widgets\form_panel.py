"""表單面板元件"""
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List, Optional, Callable, Union
from utils.theme import ThemeManager
from widgets.modern_entry import ModernEntry
from widgets.modern_combobox import ModernCombobox
from widgets.modern_button import ModernButton

class FormPanel(ttk.Frame):
    """表單面板元件
    
    提供更現代化的表單輸入功能，支援不同類型的輸入欄位和驗證。
    
    Args:
        parent: 父元件
        fields: 欄位定義，格式為 [{"id": "field1", "label": "欄位1", "type": "text", ...}, ...]
        on_submit: 提交表單時的回調函數
        on_reset: 重置表單時的回調函數
        **kwargs: 其他 ttk.Frame 參數
    """
    
    def __init__(
        self, 
        parent, 
        fields: List[Dict[str, Any]], 
        on_submit: Optional[Callable[[Dict[str, Any]], None]] = None, 
        on_reset: Optional[Callable[[], None]] = None, 
        **kwargs
    ):
        super().__init__(parent, **kwargs)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.fields = fields
        self.on_submit = on_submit
        self.on_reset = on_reset
        self.field_widgets = {}
        
        # 初始化 UI
        self._init_ui()
        
    def _init_ui(self):
        """初始化 UI"""
        # 建立表單框架
        form_frame = ttk.Frame(self)
        form_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 建立欄位
        for i, field in enumerate(self.fields):
            field_id = field["id"]
            field_type = field.get("type", "text")
            field_label = field.get("label", field_id)
            field_required = field.get("required", False)
            field_default = field.get("default", "")
            field_options = field.get("options", [])
            field_validator = field.get("validator", None)
            field_error_message = field.get("error_message", "輸入無效")
            
            # 建立欄位標籤
            label_text = f"{field_label}{'*' if field_required else ''}"
            
            # 建立欄位元件
            if field_type == "text":
                widget = ModernEntry(
                    form_frame,
                    label=label_text,
                    placeholder=field.get("placeholder", ""),
                    validation_func=field_validator,
                    error_message=field_error_message
                )
                widget.set(field_default)
                
            elif field_type == "password":
                widget = ModernEntry(
                    form_frame,
                    label=label_text,
                    placeholder=field.get("placeholder", ""),
                    validation_func=field_validator,
                    error_message=field_error_message
                )
                widget.entry.config(show="*")
                widget.set(field_default)
                
            elif field_type == "select":
                widget = ModernCombobox(
                    form_frame,
                    label=label_text,
                    values=[opt["text"] for opt in field_options],
                    validation_func=field_validator,
                    error_message=field_error_message
                )
                
                # 設定預設值
                if field_default:
                    for opt in field_options:
                        if opt["value"] == field_default:
                            widget.set(opt["text"])
                            break
                
            elif field_type == "checkbox":
                widget = ttk.Frame(form_frame)
                var = tk.BooleanVar(value=field_default)
                checkbox = ttk.Checkbutton(
                    widget,
                    text=label_text,
                    variable=var,
                    onvalue=True,
                    offvalue=False
                )
                checkbox.pack(anchor="w")
                widget.var = var
                
            elif field_type == "radio":
                widget = ttk.Frame(form_frame)
                var = tk.StringVar(value=field_default)
                
                for opt in field_options:
                    radio = ttk.Radiobutton(
                        widget,
                        text=opt["text"],
                        variable=var,
                        value=opt["value"]
                    )
                    radio.pack(anchor="w", pady=2)
                
                widget.var = var
                
            else:
                # 預設使用文字輸入欄位
                widget = ModernEntry(
                    form_frame,
                    label=label_text,
                    placeholder=field.get("placeholder", ""),
                    validation_func=field_validator,
                    error_message=field_error_message
                )
                widget.set(field_default)
            
            # 儲存欄位元件
            self.field_widgets[field_id] = widget
            
            # 排列欄位元件
            widget.pack(fill="x", pady=5)
        
        # 建立按鈕區域
        button_frame = ttk.Frame(self)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        # 建立提交按鈕
        self.submit_button = ModernButton(
            button_frame,
            text="提交",
            icon="✓",
            command=self._on_submit,
            button_type="primary"
        )
        self.submit_button.pack(side="right", padx=5)
        
        # 建立重置按鈕
        self.reset_button = ModernButton(
            button_frame,
            text="重置",
            icon="🔄",
            command=self._on_reset,
            button_type="secondary"
        )
        self.reset_button.pack(side="right", padx=5)
        
    def _on_submit(self):
        """提交表單"""
        # 驗證所有欄位
        is_valid = True
        for field in self.fields:
            field_id = field["id"]
            field_type = field.get("type", "text")
            field_required = field.get("required", False)
            
            widget = self.field_widgets[field_id]
            
            # 檢查必填欄位
            if field_required:
                if field_type in ["text", "password", "select"]:
                    if not widget.get():
                        is_valid = False
                        # 顯示錯誤訊息
                        if hasattr(widget, "error_label"):
                            widget.error_label.config(text="此欄位為必填")
        
        if not is_valid:
            return
        
        # 收集表單資料
        data = {}
        for field in self.fields:
            field_id = field["id"]
            field_type = field.get("type", "text")
            
            widget = self.field_widgets[field_id]
            
            if field_type in ["text", "password", "select"]:
                data[field_id] = widget.get()
            elif field_type in ["checkbox", "radio"]:
                data[field_id] = widget.var.get()
        
        # 呼叫回調函數
        if self.on_submit:
            self.on_submit(data)
            
    def _on_reset(self):
        """重置表單"""
        for field in self.fields:
            field_id = field["id"]
            field_type = field.get("type", "text")
            field_default = field.get("default", "")
            field_options = field.get("options", [])
            
            widget = self.field_widgets[field_id]
            
            if field_type in ["text", "password"]:
                widget.set(field_default)
            elif field_type == "select":
                widget.set("")
                if field_default:
                    for opt in field_options:
                        if opt["value"] == field_default:
                            widget.set(opt["text"])
                            break
            elif field_type in ["checkbox", "radio"]:
                widget.var.set(field_default)
        
        # 呼叫回調函數
        if self.on_reset:
            self.on_reset()
            
    def get_values(self) -> Dict[str, Any]:
        """取得表單值
        
        Returns:
            Dict[str, Any]: 表單值
        """
        data = {}
        for field in self.fields:
            field_id = field["id"]
            field_type = field.get("type", "text")
            
            widget = self.field_widgets[field_id]
            
            if field_type in ["text", "password", "select"]:
                data[field_id] = widget.get()
            elif field_type in ["checkbox", "radio"]:
                data[field_id] = widget.var.get()
                
        return data
        
    def set_values(self, data: Dict[str, Any]):
        """設定表單值
        
        Args:
            data: 表單值
        """
        for field in self.fields:
            field_id = field["id"]
            if field_id not in data:
                continue
                
            field_type = field.get("type", "text")
            field_options = field.get("options", [])
            
            widget = self.field_widgets[field_id]
            value = data[field_id]
            
            if field_type in ["text", "password"]:
                widget.set(value)
            elif field_type == "select":
                widget.set("")
                for opt in field_options:
                    if opt["value"] == value:
                        widget.set(opt["text"])
                        break
            elif field_type in ["checkbox", "radio"]:
                widget.var.set(value)
                
    def clear(self):
        """清除表單"""
        for field in self.fields:
            field_id = field["id"]
            field_type = field.get("type", "text")
            
            widget = self.field_widgets[field_id]
            
            if field_type in ["text", "password", "select"]:
                widget.clear()
            elif field_type in ["checkbox", "radio"]:
                widget.var.set(False if field_type == "checkbox" else "")
