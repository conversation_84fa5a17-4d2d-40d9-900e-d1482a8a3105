"""數據驗證器

此模組提供數據驗證和格式化功能。
"""
import re
import logging
from typing import Any, Optional, Dict, List, Callable, Union, Tuple, Pattern
from datetime import datetime

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """驗證錯誤"""
    pass

class DataValidator:
    """數據驗證器
    
    提供數據驗證和格式化功能。
    """
    
    @staticmethod
    def validate_required(value: Any, field_name: str = "欄位") -> Any:
        """驗證必填欄位
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            
        Returns:
            Any: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        if value is None or (isinstance(value, str) and value.strip() == ""):
            raise ValidationError(f"{field_name}不能為空")
        return value
        
    @staticmethod
    def validate_integer(
        value: Any,
        field_name: str = "欄位",
        min_value: Optional[int] = None,
        max_value: Optional[int] = None,
        required: bool = True
    ) -> Optional[int]:
        """驗證整數
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            min_value: 最小值
            max_value: 最大值
            required: 是否必填
            
        Returns:
            Optional[int]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 轉換為整數
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name}必須是整數")
            
        # 檢查範圍
        if min_value is not None and int_value < min_value:
            raise ValidationError(f"{field_name}不能小於 {min_value}")
            
        if max_value is not None and int_value > max_value:
            raise ValidationError(f"{field_name}不能大於 {max_value}")
            
        return int_value
        
    @staticmethod
    def validate_float(
        value: Any,
        field_name: str = "欄位",
        min_value: Optional[float] = None,
        max_value: Optional[float] = None,
        required: bool = True
    ) -> Optional[float]:
        """驗證浮點數
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            min_value: 最小值
            max_value: 最大值
            required: 是否必填
            
        Returns:
            Optional[float]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 轉換為浮點數
        try:
            float_value = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name}必須是數字")
            
        # 檢查範圍
        if min_value is not None and float_value < min_value:
            raise ValidationError(f"{field_name}不能小於 {min_value}")
            
        if max_value is not None and float_value > max_value:
            raise ValidationError(f"{field_name}不能大於 {max_value}")
            
        return float_value
        
    @staticmethod
    def validate_string(
        value: Any,
        field_name: str = "欄位",
        min_length: Optional[int] = None,
        max_length: Optional[int] = None,
        pattern: Optional[Union[str, Pattern]] = None,
        required: bool = True
    ) -> Optional[str]:
        """驗證字符串
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            min_length: 最小長度
            max_length: 最大長度
            pattern: 正則表達式模式
            required: 是否必填
            
        Returns:
            Optional[str]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 轉換為字符串
        str_value = str(value).strip()
        
        # 檢查長度
        if min_length is not None and len(str_value) < min_length:
            raise ValidationError(f"{field_name}長度不能小於 {min_length} 個字符")
            
        if max_length is not None and len(str_value) > max_length:
            raise ValidationError(f"{field_name}長度不能大於 {max_length} 個字符")
            
        # 檢查模式
        if pattern is not None:
            if isinstance(pattern, str):
                pattern = re.compile(pattern)
                
            if not pattern.match(str_value):
                raise ValidationError(f"{field_name}格式不正確")
                
        return str_value
        
    @staticmethod
    def validate_email(
        value: Any,
        field_name: str = "電子郵件",
        required: bool = True
    ) -> Optional[str]:
        """驗證電子郵件
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            required: 是否必填
            
        Returns:
            Optional[str]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 轉換為字符串
        str_value = str(value).strip()
        
        # 檢查格式
        email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_pattern, str_value):
            raise ValidationError(f"{field_name}格式不正確")
            
        return str_value
        
    @staticmethod
    def validate_phone(
        value: Any,
        field_name: str = "電話號碼",
        required: bool = True
    ) -> Optional[str]:
        """驗證電話號碼
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            required: 是否必填
            
        Returns:
            Optional[str]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 轉換為字符串
        str_value = str(value).strip()
        
        # 檢查格式
        phone_pattern = r"^[0-9+\-() ]{8,20}$"
        if not re.match(phone_pattern, str_value):
            raise ValidationError(f"{field_name}格式不正確")
            
        return str_value
        
    @staticmethod
    def validate_date(
        value: Any,
        field_name: str = "日期",
        format_str: str = "%Y-%m-%d",
        min_date: Optional[datetime] = None,
        max_date: Optional[datetime] = None,
        required: bool = True
    ) -> Optional[datetime]:
        """驗證日期
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            format_str: 日期格式
            min_date: 最小日期
            max_date: 最大日期
            required: 是否必填
            
        Returns:
            Optional[datetime]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 如果已經是 datetime 對象，直接使用
        if isinstance(value, datetime):
            date_value = value
        else:
            # 轉換為字符串
            str_value = str(value).strip()
            
            # 轉換為日期
            try:
                date_value = datetime.strptime(str_value, format_str)
            except ValueError:
                raise ValidationError(f"{field_name}格式不正確，應為 {format_str}")
                
        # 檢查範圍
        if min_date is not None and date_value < min_date:
            raise ValidationError(f"{field_name}不能早於 {min_date.strftime(format_str)}")
            
        if max_date is not None and date_value > max_date:
            raise ValidationError(f"{field_name}不能晚於 {max_date.strftime(format_str)}")
            
        return date_value
        
    @staticmethod
    def validate_boolean(
        value: Any,
        field_name: str = "布爾值",
        required: bool = True
    ) -> Optional[bool]:
        """驗證布爾值
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            required: 是否必填
            
        Returns:
            Optional[bool]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 如果已經是布爾值，直接使用
        if isinstance(value, bool):
            return value
            
        # 轉換為字符串
        str_value = str(value).strip().lower()
        
        # 轉換為布爾值
        if str_value in ("true", "yes", "y", "1", "是", "真"):
            return True
        elif str_value in ("false", "no", "n", "0", "否", "假"):
            return False
        else:
            raise ValidationError(f"{field_name}必須是布爾值")
            
    @staticmethod
    def validate_choice(
        value: Any,
        choices: List[Any],
        field_name: str = "選項",
        required: bool = True
    ) -> Optional[Any]:
        """驗證選項
        
        Args:
            value: 要驗證的值
            choices: 可選值列表
            field_name: 欄位名稱
            required: 是否必填
            
        Returns:
            Optional[Any]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, str) and value.strip() == ""):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 檢查是否在選項中
        if value not in choices:
            raise ValidationError(f"{field_name}必須是以下選項之一: {', '.join(map(str, choices))}")
            
        return value
        
    @staticmethod
    def validate_list(
        value: Any,
        field_name: str = "列表",
        min_length: Optional[int] = None,
        max_length: Optional[int] = None,
        item_validator: Optional[Callable[[Any], Any]] = None,
        required: bool = True
    ) -> Optional[List[Any]]:
        """驗證列表
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            min_length: 最小長度
            max_length: 最大長度
            item_validator: 項目驗證函數
            required: 是否必填
            
        Returns:
            Optional[List[Any]]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, (list, tuple)) and len(value) == 0):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 如果是字符串，嘗試解析為列表
        if isinstance(value, str):
            if value.strip() == "":
                if required:
                    raise ValidationError(f"{field_name}不能為空")
                return None
                
            # 嘗試解析為列表
            try:
                import json
                list_value = json.loads(value)
                if not isinstance(list_value, list):
                    raise ValidationError(f"{field_name}必須是列表")
            except json.JSONDecodeError:
                # 嘗試使用逗號分隔
                list_value = [item.strip() for item in value.split(",") if item.strip()]
        elif isinstance(value, (list, tuple)):
            list_value = list(value)
        else:
            raise ValidationError(f"{field_name}必須是列表")
            
        # 檢查長度
        if min_length is not None and len(list_value) < min_length:
            raise ValidationError(f"{field_name}長度不能小於 {min_length}")
            
        if max_length is not None and len(list_value) > max_length:
            raise ValidationError(f"{field_name}長度不能大於 {max_length}")
            
        # 驗證項目
        if item_validator is not None:
            validated_items = []
            for i, item in enumerate(list_value):
                try:
                    validated_item = item_validator(item)
                    validated_items.append(validated_item)
                except ValidationError as e:
                    raise ValidationError(f"{field_name}的第 {i+1} 項: {str(e)}")
                    
            list_value = validated_items
            
        return list_value
        
    @staticmethod
    def validate_dict(
        value: Any,
        field_name: str = "字典",
        required_keys: Optional[List[str]] = None,
        key_validators: Optional[Dict[str, Callable[[Any], Any]]] = None,
        required: bool = True
    ) -> Optional[Dict[str, Any]]:
        """驗證字典
        
        Args:
            value: 要驗證的值
            field_name: 欄位名稱
            required_keys: 必須的鍵列表
            key_validators: 鍵驗證函數字典
            required: 是否必填
            
        Returns:
            Optional[Dict[str, Any]]: 驗證後的值
            
        Raises:
            ValidationError: 驗證失敗時拋出
        """
        # 檢查是否為空
        if value is None or (isinstance(value, dict) and len(value) == 0):
            if required:
                raise ValidationError(f"{field_name}不能為空")
            return None
            
        # 如果是字符串，嘗試解析為字典
        if isinstance(value, str):
            if value.strip() == "":
                if required:
                    raise ValidationError(f"{field_name}不能為空")
                return None
                
            # 嘗試解析為字典
            try:
                import json
                dict_value = json.loads(value)
                if not isinstance(dict_value, dict):
                    raise ValidationError(f"{field_name}必須是字典")
            except json.JSONDecodeError:
                raise ValidationError(f"{field_name}必須是有效的 JSON 字典")
        elif isinstance(value, dict):
            dict_value = value
        else:
            raise ValidationError(f"{field_name}必須是字典")
            
        # 檢查必須的鍵
        if required_keys is not None:
            for key in required_keys:
                if key not in dict_value:
                    raise ValidationError(f"{field_name}缺少必須的鍵: {key}")
                    
        # 驗證鍵值
        if key_validators is not None:
            validated_dict = {}
            for key, validator in key_validators.items():
                if key in dict_value:
                    try:
                        validated_dict[key] = validator(dict_value[key])
                    except ValidationError as e:
                        raise ValidationError(f"{field_name}的鍵 {key}: {str(e)}")
                        
            # 添加未驗證的鍵
            for key, value in dict_value.items():
                if key not in validated_dict:
                    validated_dict[key] = value
                    
            dict_value = validated_dict
            
        return dict_value
        
    @staticmethod
    def format_number(
        value: Union[int, float, str],
        decimal_places: int = 2,
        thousands_separator: str = ",",
        decimal_separator: str = "."
    ) -> str:
        """格式化數字
        
        Args:
            value: 要格式化的值
            decimal_places: 小數位數
            thousands_separator: 千位分隔符
            decimal_separator: 小數分隔符
            
        Returns:
            str: 格式化後的值
        """
        # 轉換為浮點數
        try:
            float_value = float(value)
        except (ValueError, TypeError):
            return str(value)
            
        # 格式化
        format_str = f"{{:,.{decimal_places}f}}"
        formatted = format_str.format(float_value)
        
        # 替換分隔符
        if thousands_separator != ",":
            formatted = formatted.replace(",", thousands_separator)
            
        if decimal_separator != ".":
            formatted = formatted.replace(".", decimal_separator)
            
        return formatted
        
    @staticmethod
    def format_date(
        value: Union[datetime, str],
        format_str: str = "%Y-%m-%d",
        input_format: Optional[str] = None
    ) -> str:
        """格式化日期
        
        Args:
            value: 要格式化的值
            format_str: 輸出格式
            input_format: 輸入格式
            
        Returns:
            str: 格式化後的值
        """
        # 如果已經是 datetime 對象，直接使用
        if isinstance(value, datetime):
            date_value = value
        else:
            # 轉換為字符串
            str_value = str(value).strip()
            
            # 轉換為日期
            try:
                if input_format:
                    date_value = datetime.strptime(str_value, input_format)
                else:
                    # 嘗試常見格式
                    formats = [
                        "%Y-%m-%d",
                        "%Y/%m/%d",
                        "%d-%m-%Y",
                        "%d/%m/%Y",
                        "%m-%d-%Y",
                        "%m/%d/%Y",
                        "%Y-%m-%d %H:%M:%S",
                        "%Y/%m/%d %H:%M:%S"
                    ]
                    
                    for fmt in formats:
                        try:
                            date_value = datetime.strptime(str_value, fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        return str_value
            except ValueError:
                return str_value
                
        # 格式化
        return date_value.strftime(format_str)
        
    @staticmethod
    def format_phone(
        value: str,
        country_code: Optional[str] = None,
        format_str: Optional[str] = None
    ) -> str:
        """格式化電話號碼
        
        Args:
            value: 要格式化的值
            country_code: 國家代碼
            format_str: 格式字符串
            
        Returns:
            str: 格式化後的值
        """
        # 轉換為字符串
        str_value = str(value).strip()
        
        # 移除非數字字符
        digits = re.sub(r"\D", "", str_value)
        
        # 如果沒有指定格式，使用默認格式
        if not format_str:
            if len(digits) == 10:  # 美國/加拿大格式
                format_str = "({}{}{}) {}{}{}-{}{}{}{}"
            elif len(digits) == 11 and digits.startswith("1"):  # 美國/加拿大帶國家代碼
                format_str = "+{} ({}{}{}) {}{}{}-{}{}{}{}"
            elif len(digits) == 8:  # 香港格式
                format_str = "{}{}{}{} {}{}{}{}"
            else:
                # 無法確定格式，返回原始值
                return str_value
                
        # 格式化
        try:
            # 添加國家代碼
            if country_code and not str_value.startswith("+"):
                formatted = "+" + country_code + " " + str_value
            else:
                # 使用格式字符串
                formatted = format_str.format(*digits)
        except (IndexError, TypeError):
            # 格式化失敗，返回原始值
            return str_value
            
        return formatted

# 創建全局數據驗證器實例
data_validator = DataValidator()
