"""批量處理器

此模組提供批量處理功能，支援暫停、恢復、取消和進度報告。
內存優化版本：添加了內存監控和優化功能。
"""
import threading
import time
import logging
import queue
import gc
import os
import json
from typing import List, Dict, Any, Callable, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from .thread_manager import thread_manager
from .exceptions import ThreadCreateError, ThreadTerminateError
try:
    from .memory_monitor import memory_monitor
except ImportError:
    # 如果無法導入 memory_monitor，創建一個模擬對象
    class MemoryMonitorMock:
        def __init__(self):
            self.threshold_mb = 200.0
            self.on_threshold_exceeded = None
            self.on_memory_status = None

        def start_monitoring(self):
            logger.warning("內存監控功能未啟用，使用模擬對象")

        def stop_monitoring(self):
            pass

        def get_memory_report(self):
            return "內存監控功能未啟用"

    memory_monitor = MemoryMonitorMock()

logger = logging.getLogger(__name__)

class BatchProcessor:
    """批量處理器

    提供批量處理功能，支援暫停、恢復、取消和進度報告。

    Args:
        max_workers: 最大工作線程數
        on_progress: 進度回調函數，接收 (current, total, percentage) 參數
        on_item_complete: 項目完成回調函數，接收 (item, result, success) 參數
        on_complete: 完成回調函數，接收 (results, success_count, fail_count) 參數
        on_error: 錯誤回調函數，接收 (error) 參數
    """

    def __init__(
        self,
        max_workers: int = 5,
        on_progress: Optional[Callable[[int, int, float], None]] = None,
        on_item_complete: Optional[Callable[[Any, Any, bool], None]] = None,
        on_complete: Optional[Callable[[List[Dict[str, Any]], int, int], None]] = None,
        on_error: Optional[Callable[[Exception], None]] = None,
        memory_threshold_mb: float = 200.0,
        result_batch_size: int = 100,
        enable_memory_optimization: bool = True
    ):
        self.max_workers = max_workers
        self.on_progress = on_progress
        self.on_item_complete = on_item_complete
        self.on_complete = on_complete
        self.on_error = on_error
        self.memory_threshold_mb = memory_threshold_mb
        self.result_batch_size = result_batch_size
        self.enable_memory_optimization = enable_memory_optimization

        self.is_running = False
        self.is_paused = False
        self.is_cancelled = False
        self.thread_id = None
        self.results = []
        self.success_count = 0
        self.fail_count = 0
        self.processed_count = 0
        self.total_count = 0
        self.start_time = 0
        self.pause_time = 0
        self.total_pause_time = 0

        # 用於暫停和恢復的事件
        self.pause_event = threading.Event()
        self.pause_event.set()  # 初始狀態為未暫停

        # 用於存儲待處理項目的隊列
        self.item_queue = queue.Queue()

        # 用於存儲處理結果的隊列
        self.result_queue = queue.Queue()

        # 用於更新進度的定時器
        self.progress_timer = None

        # 用於保存結果的臨時文件
        self.temp_result_file = None
        self.result_file_count = 0
        self.result_files = []

        # 內存監控
        if self.enable_memory_optimization:
            # 設置內存監控回調
            memory_monitor.threshold_mb = self.memory_threshold_mb
            memory_monitor.on_threshold_exceeded = self._on_memory_threshold_exceeded
            memory_monitor.on_memory_status = self._on_memory_status

    def process(
        self,
        items: List[Any],
        process_func: Callable[[Any], Any],
        batch_size: int = 20,
        update_interval: float = 0.1
    ) -> Tuple[List[Dict[str, Any]], int, int]:
        """處理項目

        Args:
            items: 待處理項目列表
            process_func: 處理函數，接收項目並返回處理結果
            batch_size: 批次大小，每次從隊列中取出的項目數量
            update_interval: 進度更新間隔（秒）

        Returns:
            Tuple[List[Dict[str, Any]], int, int]: 處理結果列表、成功數量和失敗數量
        """
        if self.is_running:
            logger.warning("批量處理器已在運行中")
            return [], 0, 0

        # 重置狀態
        self.is_running = True
        self.is_paused = False
        self.is_cancelled = False
        self.results = []
        self.success_count = 0
        self.fail_count = 0
        self.processed_count = 0
        self.total_count = len(items)
        self.start_time = time.time()
        self.pause_time = 0
        self.total_pause_time = 0
        self.pause_event.set()
        self.result_file_count = 0
        self.result_files = []

        # 清空隊列
        while not self.item_queue.empty():
            self.item_queue.get()
        while not self.result_queue.empty():
            self.result_queue.get()

        # 將項目添加到隊列
        for item in items:
            self.item_queue.put(item)

        # 創建臨時結果目錄
        if self.enable_memory_optimization:
            self._create_temp_result_dir()

        # 啟動內存監控
        if self.enable_memory_optimization:
            memory_monitor.start_monitoring()

        # 創建處理線程
        try:
            self.thread_id = thread_manager.create_thread(
                name="batch_processor",
                target=self._process_thread,
                args=(process_func, batch_size, update_interval),
                daemon=True
            )
            logger.info(f"批量處理線程已啟動: {self.thread_id}")
        except ThreadCreateError as e:
            self.is_running = False
            logger.error(f"創建批量處理線程失敗: {e}")
            if self.on_error:
                self.on_error(e)
            return [], 0, 0

        # 等待處理完成
        thread_manager.wait_for_thread(self.thread_id)

        # 停止內存監控
        if self.enable_memory_optimization:
            memory_monitor.stop_monitoring()

        # 從臨時文件加載結果
        if self.enable_memory_optimization and self.result_files:
            self._load_results_from_files()

        # 返回結果
        return self.results, self.success_count, self.fail_count

    def _create_temp_result_dir(self):
        """創建臨時結果目錄"""
        try:
            # 創建臨時目錄
            temp_dir = os.path.join(os.getcwd(), "temp_results")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 清理舊的臨時文件
            for file in os.listdir(temp_dir):
                if file.startswith("batch_result_") and file.endswith(".json"):
                    try:
                        os.remove(os.path.join(temp_dir, file))
                    except Exception as e:
                        logger.warning(f"清理臨時文件失敗: {e}")

            logger.info(f"已創建臨時結果目錄: {temp_dir}")

        except Exception as e:
            logger.error(f"創建臨時結果目錄失敗: {e}")

    def _save_results_to_file(self):
        """將結果保存到臨時文件"""
        if not self.results:
            return

        try:
            # 創建臨時文件
            temp_dir = os.path.join(os.getcwd(), "temp_results")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 生成文件名
            self.result_file_count += 1
            file_name = f"batch_result_{self.result_file_count}_{int(time.time())}.json"
            file_path = os.path.join(temp_dir, file_name)

            # 保存結果
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(self.results, f, ensure_ascii=False)

            # 記錄文件路徑
            self.result_files.append(file_path)

            # 清空結果列表，釋放內存
            result_count = len(self.results)
            self.results = []

            # 強制執行垃圾回收
            gc.collect()

            logger.info(f"已將 {result_count} 個結果保存到臨時文件: {file_path}")

        except Exception as e:
            logger.error(f"保存結果到臨時文件失敗: {e}")

    def _load_results_from_files(self):
        """從臨時文件加載結果"""
        if not self.result_files:
            return

        try:
            # 清空結果列表
            self.results = []

            # 加載所有文件
            for file_path in self.result_files:
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        file_results = json.load(f)
                        self.results.extend(file_results)

                    # 刪除臨時文件
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"刪除臨時文件失敗: {e}")

                except Exception as e:
                    logger.error(f"加載結果文件失敗: {file_path}, {e}")

            logger.info(f"已從 {len(self.result_files)} 個臨時文件加載 {len(self.results)} 個結果")

            # 清空文件列表
            self.result_files = []

        except Exception as e:
            logger.error(f"從臨時文件加載結果失敗: {e}")

    def _on_memory_threshold_exceeded(self, memory_usage_mb: float):
        """內存閾值超過回調函數

        Args:
            memory_usage_mb: 當前內存使用量（MB）
        """
        logger.warning(f"內存使用超過閾值: {memory_usage_mb:.2f} MB > {self.memory_threshold_mb} MB，正在保存結果並釋放內存")

        # 保存結果到臨時文件
        self._save_results_to_file()

    def _on_memory_status(self, memory_info: Dict[str, Any]):
        """內存狀態回調函數

        Args:
            memory_info: 內存狀態信息
        """
        # 如果結果數量超過批次大小，保存到臨時文件
        if len(self.results) >= self.result_batch_size:
            logger.info(f"結果數量 ({len(self.results)}) 超過批次大小 ({self.result_batch_size})，正在保存到臨時文件")
            self._save_results_to_file()

    def _process_thread(
        self,
        process_func: Callable[[Any], Any],
        batch_size: int,
        update_interval: float
    ):
        """處理線程

        Args:
            process_func: 處理函數
            batch_size: 批次大小
            update_interval: 進度更新間隔
        """
        try:
            # 啟動進度更新定時器
            self._start_progress_timer(update_interval)

            # 使用線程池處理項目
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                while not self.item_queue.empty() and not self.is_cancelled:
                    # 檢查是否暫停
                    self.pause_event.wait()

                    # 取出一批項目
                    batch_items = []
                    for _ in range(min(batch_size, self.item_queue.qsize())):
                        if not self.item_queue.empty():
                            batch_items.append(self.item_queue.get())

                    if not batch_items:
                        break

                    # 提交任務
                    future_to_item = {
                        executor.submit(self._process_item, process_func, item): item
                        for item in batch_items
                    }

                    # 處理結果
                    for future in as_completed(future_to_item):
                        item = future_to_item[future]
                        try:
                            result, success = future.result()

                            # 更新計數器
                            self.processed_count += 1
                            if success:
                                self.success_count += 1
                            else:
                                self.fail_count += 1

                            # 添加到結果列表
                            result_entry = {
                                "item": item,
                                "result": result,
                                "success": success,
                                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                            }
                            self.results.append(result_entry)

                            # 調用項目完成回調
                            if self.on_item_complete:
                                self.on_item_complete(item, result, success)

                            # 內存優化：檢查結果數量，如果超過批次大小，保存到臨時文件
                            if self.enable_memory_optimization and len(self.results) >= self.result_batch_size:
                                self._save_results_to_file()

                        except Exception as e:
                            logger.error(f"處理項目 {item} 失敗: {e}")

                            # 更新計數器
                            self.processed_count += 1
                            self.fail_count += 1

                            # 添加到結果列表
                            result_entry = {
                                "item": item,
                                "result": str(e),
                                "success": False,
                                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                            }
                            self.results.append(result_entry)

                            # 調用項目完成回調
                            if self.on_item_complete:
                                self.on_item_complete(item, str(e), False)

                            # 內存優化：檢查結果數量，如果超過批次大小，保存到臨時文件
                            if self.enable_memory_optimization and len(self.results) >= self.result_batch_size:
                                self._save_results_to_file()

                    # 每處理一批次後暫停一下，讓UI有時間更新
                    time.sleep(0.05)

                    # 內存優化：強制執行垃圾回收
                    if self.enable_memory_optimization and self.processed_count % (batch_size * 5) == 0:
                        gc.collect()

            # 內存優化：保存剩餘結果到臨時文件
            if self.enable_memory_optimization and self.results:
                self._save_results_to_file()

            # 停止進度更新定時器
            self._stop_progress_timer()

            # 計算總耗時
            elapsed_time = time.time() - self.start_time - self.total_pause_time

            # 內存優化：從臨時文件加載結果
            if self.enable_memory_optimization and self.result_files:
                self._load_results_from_files()

            # 調用完成回調
            if self.on_complete:
                self.on_complete(self.results, self.success_count, self.fail_count)

            # 記錄內存使用報告
            if self.enable_memory_optimization:
                memory_report = memory_monitor.get_memory_report()
                logger.info(f"內存使用報告:\n{memory_report}")

            logger.info(f"批量處理完成: 成功 {self.success_count} 項, 失敗 {self.fail_count} 項, 耗時 {elapsed_time:.2f} 秒")

        except Exception as e:
            logger.error(f"批量處理線程失敗: {e}")

            # 停止進度更新定時器
            self._stop_progress_timer()

            # 調用錯誤回調
            if self.on_error:
                self.on_error(e)

        finally:
            # 重置狀態
            self.is_running = False
            self.is_paused = False
            self.is_cancelled = False

            # 清理臨時文件
            if self.enable_memory_optimization:
                for file_path in self.result_files:
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        logger.warning(f"清理臨時文件失敗: {file_path}, {e}")

                self.result_files = []

    def _process_item(
        self,
        process_func: Callable[[Any], Any],
        item: Any
    ) -> Tuple[Any, bool]:
        """處理單個項目

        Args:
            process_func: 處理函數
            item: 項目

        Returns:
            Tuple[Any, bool]: 處理結果和是否成功
        """
        try:
            result = process_func(item)

            # 檢查結果是否為元組，如果是，則第二個元素表示是否成功
            if isinstance(result, tuple) and len(result) >= 2:
                return result[0], result[1]

            # 否則，假設處理成功
            return result, True

        except Exception as e:
            logger.error(f"處理項目 {item} 失敗: {e}")
            return str(e), False

    def _start_progress_timer(self, interval: float):
        """啟動進度更新定時器

        Args:
            interval: 更新間隔
        """
        def update_progress():
            if not self.is_running or self.is_cancelled:
                return

            # 計算進度百分比
            if self.total_count > 0:
                percentage = self.processed_count / self.total_count * 100
            else:
                percentage = 0

            # 調用進度回調
            # 注意：這裡的回調函數可能在非主執行緒中被調用，
            # 回調函數應該負責確保 UI 更新在主執行緒中進行
            if self.on_progress:
                try:
                    self.on_progress(self.processed_count, self.total_count, percentage)
                except Exception as e:
                    # 捕獲回調函數中的異常，避免中斷進度更新
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"進度回調函數異常: {e}")

            # 重新啟動定時器
            if self.is_running and not self.is_cancelled:
                self.progress_timer = threading.Timer(interval, update_progress)
                self.progress_timer.daemon = True
                self.progress_timer.start()

        # 啟動定時器
        self.progress_timer = threading.Timer(interval, update_progress)
        self.progress_timer.daemon = True
        self.progress_timer.start()

    def _stop_progress_timer(self):
        """停止進度更新定時器"""
        if self.progress_timer:
            self.progress_timer.cancel()
            self.progress_timer = None

    def pause(self):
        """暫停處理"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            self.pause_event.clear()
            self.pause_time = time.time()
            logger.info("批量處理已暫停")

    def resume(self):
        """恢復處理"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            self.pause_event.set()
            self.total_pause_time += time.time() - self.pause_time
            logger.info("批量處理已恢復")

    def cancel(self):
        """取消處理"""
        if self.is_running:
            self.is_cancelled = True
            self.pause_event.set()  # 確保線程不會被暫停
            logger.info("批量處理已取消")

    def get_progress(self) -> Tuple[int, int, float]:
        """獲取進度

        Returns:
            Tuple[int, int, float]: 當前處理數量、總數量和進度百分比
        """
        if self.total_count > 0:
            percentage = self.processed_count / self.total_count * 100
        else:
            percentage = 0

        return self.processed_count, self.total_count, percentage

    def get_status(self) -> Dict[str, Any]:
        """獲取狀態

        Returns:
            Dict[str, Any]: 狀態信息
        """
        elapsed_time = 0
        if self.start_time > 0:
            if self.is_paused:
                elapsed_time = self.pause_time - self.start_time - self.total_pause_time
            else:
                elapsed_time = time.time() - self.start_time - self.total_pause_time

        return {
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "is_cancelled": self.is_cancelled,
            "processed_count": self.processed_count,
            "total_count": self.total_count,
            "success_count": self.success_count,
            "fail_count": self.fail_count,
            "elapsed_time": elapsed_time,
            "start_time": self.start_time,
            "pause_time": self.pause_time,
            "total_pause_time": self.total_pause_time
        }

# 創建全局批量處理器實例
batch_processor = BatchProcessor()
