"""功能檢測器

此模組提供功能檢測功能，用於檢測系統中可用的功能。
"""
import sys
import os
import logging
import importlib
import platform
from typing import Dict, Any, List, Tuple, Optional

logger = logging.getLogger(__name__)

class FeatureDetector:
    """功能檢測器
    
    提供功能檢測功能，用於檢測系統中可用的功能。
    """
    
    def __init__(self):
        self.features = {
            "memory_monitor": {
                "name": "內存監控",
                "description": "監控和優化程序的內存使用",
                "available": False,
                "module": "psutil",
                "version": None,
                "error": None,
                "required_for": ["資源監控", "批量處理優化"]
            },
            "network_recovery": {
                "name": "網絡恢復",
                "description": "自動檢測和恢復網絡連接問題",
                "available": False,
                "module": "requests",
                "version": None,
                "error": None,
                "required_for": ["網絡連接恢復", "斷點續傳"]
            },
            "enhanced_logger": {
                "name": "增強日誌",
                "description": "提供更詳細的日誌記錄和導出功能",
                "available": False,
                "module": None,
                "version": None,
                "error": None,
                "required_for": ["日誌導出", "錯誤診斷"]
            },
            "resource_monitor": {
                "name": "資源監控",
                "description": "顯示系統資源使用情況",
                "available": False,
                "module": "psutil",
                "version": None,
                "error": None,
                "required_for": ["CPU 監控", "內存監控", "磁盤監控", "網絡監控"]
            },
            "auto_updater": {
                "name": "自動更新",
                "description": "檢查和下載程序更新",
                "available": False,
                "module": "requests",
                "version": None,
                "error": None,
                "required_for": ["版本檢查", "更新下載"]
            },
            "excel_support": {
                "name": "Excel 支持",
                "description": "支持 Excel 文件的讀寫",
                "available": False,
                "module": "openpyxl",
                "version": None,
                "error": None,
                "required_for": ["批量導入", "批量導出", "報表生成"]
            },
            "data_processing": {
                "name": "數據處理",
                "description": "提供高級數據處理功能",
                "available": False,
                "module": "pandas",
                "version": None,
                "error": None,
                "required_for": ["數據分析", "數據過濾", "數據轉換"]
            },
            "gui": {
                "name": "圖形界面",
                "description": "提供圖形用戶界面",
                "available": False,
                "module": "tkinter",
                "version": None,
                "error": None,
                "required_for": ["主界面", "對話框", "表單"]
            },
            "image_processing": {
                "name": "圖像處理",
                "description": "支持圖像處理功能",
                "available": False,
                "module": "PIL",
                "version": None,
                "error": None,
                "required_for": ["圖標顯示", "圖像縮放", "圖像轉換"]
            }
        }
        
    def detect_features(self) -> Dict[str, Dict[str, Any]]:
        """檢測可用功能
        
        Returns:
            Dict[str, Dict[str, Any]]: 功能檢測結果
        """
        # 檢測內存監控功能
        self._detect_feature("memory_monitor")
        
        # 檢測網絡恢復功能
        self._detect_feature("network_recovery")
        
        # 檢測增強日誌功能
        try:
            from utils.enhanced_logger import EnhancedLogger
            self.features["enhanced_logger"]["available"] = True
            self.features["enhanced_logger"]["version"] = "1.0.0"  # 假設版本
        except ImportError as e:
            self.features["enhanced_logger"]["available"] = False
            self.features["enhanced_logger"]["error"] = str(e)
            
        # 檢測資源監控功能
        try:
            from widgets.resource_monitor import ResourceMonitor
            self.features["resource_monitor"]["available"] = self.features["memory_monitor"]["available"]
            self.features["resource_monitor"]["version"] = "1.0.0"  # 假設版本
            if not self.features["memory_monitor"]["available"]:
                self.features["resource_monitor"]["error"] = "需要 psutil 模塊"
        except ImportError as e:
            self.features["resource_monitor"]["available"] = False
            self.features["resource_monitor"]["error"] = str(e)
            
        # 檢測自動更新功能
        try:
            from utils.auto_updater import AutoUpdater
            self.features["auto_updater"]["available"] = True
            self.features["auto_updater"]["version"] = "1.0.0"  # 假設版本
        except ImportError as e:
            self.features["auto_updater"]["available"] = False
            self.features["auto_updater"]["error"] = str(e)
            
        # 檢測 Excel 支持
        self._detect_feature("excel_support")
        
        # 檢測數據處理功能
        self._detect_feature("data_processing")
        
        # 檢測圖形界面功能
        self._detect_feature("gui")
        
        # 檢測圖像處理功能
        try:
            import PIL
            self.features["image_processing"]["available"] = True
            self.features["image_processing"]["version"] = PIL.__version__
        except ImportError as e:
            self.features["image_processing"]["available"] = False
            self.features["image_processing"]["error"] = str(e)
            
        return self.features
        
    def _detect_feature(self, feature_name: str):
        """檢測單個功能
        
        Args:
            feature_name: 功能名稱
        """
        feature = self.features.get(feature_name)
        if not feature:
            return
            
        module_name = feature.get("module")
        if not module_name:
            return
            
        try:
            module = importlib.import_module(module_name)
            feature["available"] = True
            feature["version"] = getattr(module, "__version__", "未知")
        except ImportError as e:
            feature["available"] = False
            feature["error"] = str(e)
            
    def get_available_features(self) -> List[str]:
        """獲取可用功能列表
        
        Returns:
            List[str]: 可用功能列表
        """
        return [name for name, feature in self.features.items() if feature["available"]]
        
    def get_unavailable_features(self) -> List[str]:
        """獲取不可用功能列表
        
        Returns:
            List[str]: 不可用功能列表
        """
        return [name for name, feature in self.features.items() if not feature["available"]]
        
    def get_feature_status(self, feature_name: str) -> Dict[str, Any]:
        """獲取功能狀態
        
        Args:
            feature_name: 功能名稱
            
        Returns:
            Dict[str, Any]: 功能狀態
        """
        return self.features.get(feature_name, {})
        
    def get_system_info(self) -> Dict[str, Any]:
        """獲取系統信息
        
        Returns:
            Dict[str, Any]: 系統信息
        """
        system_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "os_release": platform.release(),
            "python_version": platform.python_version(),
            "python_implementation": platform.python_implementation(),
            "processor": platform.processor(),
            "machine": platform.machine(),
            "node": platform.node()
        }
        
        # 添加 psutil 信息
        try:
            import psutil
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")
            
            system_info.update({
                "memory_total": memory.total,
                "memory_available": memory.available,
                "memory_percent": memory.percent,
                "disk_total": disk.total,
                "disk_free": disk.free,
                "disk_percent": disk.percent,
                "cpu_count": psutil.cpu_count(),
                "cpu_percent": psutil.cpu_percent(interval=0.1)
            })
        except ImportError:
            pass
            
        return system_info
        
    def get_feature_report(self) -> str:
        """獲取功能報告
        
        Returns:
            str: 功能報告
        """
        # 檢測功能
        self.detect_features()
        
        # 獲取系統信息
        system_info = self.get_system_info()
        
        # 構建報告
        report = [
            "===== 功能檢測報告 =====",
            f"操作系統: {system_info.get('os')} {system_info.get('os_release')} {system_info.get('os_version')}",
            f"Python 版本: {system_info.get('python_version')} ({system_info.get('python_implementation')})",
            f"處理器: {system_info.get('processor')}",
            ""
        ]
        
        if "memory_total" in system_info:
            report.extend([
                f"內存: {system_info.get('memory_total') / (1024 * 1024 * 1024):.2f} GB 總計, "
                f"{system_info.get('memory_available') / (1024 * 1024 * 1024):.2f} GB 可用, "
                f"{system_info.get('memory_percent')}% 使用率",
                f"磁盤: {system_info.get('disk_total') / (1024 * 1024 * 1024):.2f} GB 總計, "
                f"{system_info.get('disk_free') / (1024 * 1024 * 1024):.2f} GB 可用, "
                f"{system_info.get('disk_percent')}% 使用率",
                f"CPU: {system_info.get('cpu_count')} 核心, {system_info.get('cpu_percent')}% 使用率",
                ""
            ])
            
        report.append("可用功能:")
        for name in self.get_available_features():
            feature = self.features[name]
            report.append(f"  ✅ {feature['name']} ({feature['description']}) - 版本: {feature['version']}")
            
        report.append("")
        report.append("不可用功能:")
        for name in self.get_unavailable_features():
            feature = self.features[name]
            report.append(f"  ❌ {feature['name']} ({feature['description']}) - 錯誤: {feature['error']}")
            
        report.append("")
        report.append("===== 功能依賴關係 =====")
        for name, feature in self.features.items():
            if feature["required_for"]:
                status = "✅" if feature["available"] else "❌"
                report.append(f"{status} {feature['name']} 是以下功能的依賴: {', '.join(feature['required_for'])}")
                
        report.append("")
        report.append("===========================")
        
        return "\n".join(report)
        
    def show_feature_report_dialog(self, parent=None):
        """顯示功能報告對話框
        
        Args:
            parent: 父窗口
        """
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            report = self.get_feature_report()
            
            if parent is None:
                root = tk.Tk()
                root.withdraw()
                parent = root
                
            # 創建對話框
            dialog = tk.Toplevel(parent)
            dialog.title("功能檢測報告")
            dialog.geometry("800x600")
            dialog.minsize(600, 400)
            
            # 創建文本框
            text = tk.Text(dialog, wrap=tk.WORD, font=("Consolas", 10))
            text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 添加滾動條
            scrollbar = tk.Scrollbar(text)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            text.config(yscrollcommand=scrollbar.set)
            scrollbar.config(command=text.yview)
            
            # 插入報告
            text.insert(tk.END, report)
            text.config(state=tk.DISABLED)
            
            # 創建按鈕
            button_frame = tk.Frame(dialog)
            button_frame.pack(fill=tk.X, padx=10, pady=10)
            
            # 關閉按鈕
            close_button = tk.Button(button_frame, text="關閉", command=dialog.destroy)
            close_button.pack(side=tk.RIGHT)
            
            # 複製按鈕
            def copy_to_clipboard():
                dialog.clipboard_clear()
                dialog.clipboard_append(report)
                messagebox.showinfo("複製成功", "報告已複製到剪貼板")
                
            copy_button = tk.Button(button_frame, text="複製到剪貼板", command=copy_to_clipboard)
            copy_button.pack(side=tk.RIGHT, padx=10)
            
            # 保存按鈕
            def save_to_file():
                from tkinter import filedialog
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".txt",
                    filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                    title="保存功能報告"
                )
                
                if file_path:
                    try:
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write(report)
                        messagebox.showinfo("保存成功", f"報告已保存到: {file_path}")
                    except Exception as e:
                        messagebox.showerror("保存失敗", f"保存報告失敗: {e}")
                        
            save_button = tk.Button(button_frame, text="保存到文件", command=save_to_file)
            save_button.pack(side=tk.RIGHT, padx=10)
            
            # 居中顯示
            dialog.update_idletasks()
            width = dialog.winfo_width()
            height = dialog.winfo_height()
            x = (dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (dialog.winfo_screenheight() // 2) - (height // 2)
            dialog.geometry(f"{width}x{height}+{x}+{y}")
            
            # 設置模態
            dialog.transient(parent)
            dialog.grab_set()
            parent.wait_window(dialog)
            
        except ImportError as e:
            logger.error(f"顯示功能報告對話框失敗: {e}")
            print(self.get_feature_report())

# 創建全局功能檢測器實例
feature_detector = FeatureDetector()
