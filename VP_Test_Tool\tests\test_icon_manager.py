"""IconManager 單元測試"""
import unittest
import os
import sys

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.icon_manager import IconManager

class TestIconManager(unittest.TestCase):
    """IconManager 單元測試類"""

    def test_get_icon(self):
        """測試取得圖示"""
        # 測試取得已定義的圖示
        self.assertEqual(IconManager.get("info"), "ℹ️")
        self.assertEqual(IconManager.get("warning"), "⚠️")
        self.assertEqual(IconManager.get("error"), "❌")
        self.assertEqual(IconManager.get("success"), "✅")
        self.assertEqual(IconManager.get("home"), "🏠")
        self.assertEqual(IconManager.get("user"), "👤")
        self.assertEqual(IconManager.get("settings"), "⚙️")
        self.assertEqual(IconManager.get("search"), "🔍")
        self.assertEqual(IconManager.get("add"), "➕")
        self.assertEqual(IconManager.get("delete"), "🗑️")
        self.assertEqual(IconManager.get("edit"), "✏️")
        self.assertEqual(IconManager.get("save"), "💾")
        self.assertEqual(IconManager.get("refresh"), "🔄")
        self.assertEqual(IconManager.get("update"), "🔄")
        self.assertEqual(IconManager.get("download"), "⬇️")
        self.assertEqual(IconManager.get("upload"), "⬆️")
        self.assertEqual(IconManager.get("import"), "📥")
        self.assertEqual(IconManager.get("export"), "📤")
        self.assertEqual(IconManager.get("copy"), "📋")
        self.assertEqual(IconManager.get("paste"), "📄")
        self.assertEqual(IconManager.get("cut"), "✂️")
        self.assertEqual(IconManager.get("undo"), "↩️")
        self.assertEqual(IconManager.get("redo"), "↪️")
        self.assertEqual(IconManager.get("print"), "🖨️")
        self.assertEqual(IconManager.get("share"), "📤")
        self.assertEqual(IconManager.get("filter"), "🔍")
        self.assertEqual(IconManager.get("sort"), "↕️")
        self.assertEqual(IconManager.get("unlock"), "🔓")
        self.assertEqual(IconManager.get("pin"), "📌")
        self.assertEqual(IconManager.get("unpin"), "📍")
        self.assertEqual(IconManager.get("favorite"), "⭐")
        self.assertEqual(IconManager.get("unfavorite"), "☆")
        self.assertEqual(IconManager.get("bookmark"), "🔖")
        self.assertEqual(IconManager.get("unbookmark"), "📑")
        self.assertEqual(IconManager.get("attach"), "📎")
        self.assertEqual(IconManager.get("detach"), "🧷")
        self.assertEqual(IconManager.get("zoom_in"), "🔍")
        self.assertEqual(IconManager.get("zoom_out"), "🔎")
        self.assertEqual(IconManager.get("fullscreen"), "⛶")
        self.assertEqual(IconManager.get("exit_fullscreen"), "⛶")
        self.assertEqual(IconManager.get("close"), "❌")
        self.assertEqual(IconManager.get("minimize"), "🗕")
        self.assertEqual(IconManager.get("maximize"), "🗖")
        self.assertEqual(IconManager.get("restore"), "🗗")
        self.assertEqual(IconManager.get("ok"), "✓")
        self.assertEqual(IconManager.get("cancel"), "✗")
        self.assertEqual(IconManager.get("apply"), "✓")
        self.assertEqual(IconManager.get("reset"), "↺")
        self.assertEqual(IconManager.get("clear"), "🧹")
        self.assertEqual(IconManager.get("back"), "⬅️")
        self.assertEqual(IconManager.get("forward"), "➡️")
        self.assertEqual(IconManager.get("up"), "⬆️")
        self.assertEqual(IconManager.get("down"), "⬇️")
        self.assertEqual(IconManager.get("left"), "⬅️")
        self.assertEqual(IconManager.get("right"), "➡️")
        self.assertEqual(IconManager.get("menu"), "☰")
        self.assertEqual(IconManager.get("more"), "⋮")
        self.assertEqual(IconManager.get("more_horiz"), "⋯")
        self.assertEqual(IconManager.get("drag"), "⠿")
        self.assertEqual(IconManager.get("move"), "↔️")
        self.assertEqual(IconManager.get("resize"), "⤡")
        self.assertEqual(IconManager.get("rotate"), "↻")
        self.assertEqual(IconManager.get("flip"), "⇄")
        self.assertEqual(IconManager.get("expand"), "⤢")
        self.assertEqual(IconManager.get("collapse"), "⤫")
        self.assertEqual(IconManager.get("show"), "👁️")
        self.assertEqual(IconManager.get("hide"), "👁️‍🗨️")
        self.assertEqual(IconManager.get("visible"), "👁️")
        self.assertEqual(IconManager.get("invisible"), "👁️‍🗨️")
        self.assertEqual(IconManager.get("lock"), "🔒")

    def test_get_nonexistent_icon(self):
        """測試取得不存在的圖示"""
        # 測試取得不存在的圖示，應該返回圖示名稱
        self.assertEqual(IconManager.get("nonexistent"), "nonexistent")

    def test_get_with_fallback(self):
        """測試取得圖示時使用預設值"""
        # 測試取得不存在的圖示，使用預設值
        self.assertEqual(IconManager.get("nonexistent", "fallback"), "fallback")

    def test_get_all(self):
        """測試取得所有圖示"""
        # 測試取得所有圖示
        icons = IconManager.get_all()
        self.assertIsInstance(icons, dict)
        self.assertGreater(len(icons), 0)
        self.assertIn("info", icons)
        self.assertIn("warning", icons)
        self.assertIn("error", icons)
        self.assertIn("success", icons)
        self.assertIn("home", icons)
        self.assertIn("user", icons)
        self.assertIn("settings", icons)
        self.assertIn("search", icons)
        self.assertIn("add", icons)
        self.assertIn("delete", icons)
        self.assertIn("edit", icons)
        self.assertIn("save", icons)
        self.assertIn("refresh", icons)
        self.assertIn("update", icons)
        self.assertIn("download", icons)
        self.assertIn("upload", icons)
        self.assertIn("import", icons)
        self.assertIn("export", icons)
        self.assertIn("copy", icons)
        self.assertIn("paste", icons)
        self.assertIn("cut", icons)
        self.assertIn("undo", icons)
        self.assertIn("redo", icons)
        self.assertIn("print", icons)
        self.assertIn("share", icons)
        self.assertIn("filter", icons)
        self.assertIn("sort", icons)
        self.assertIn("unlock", icons)
        self.assertIn("pin", icons)
        self.assertIn("unpin", icons)
        self.assertIn("favorite", icons)
        self.assertIn("unfavorite", icons)
        self.assertIn("bookmark", icons)
        self.assertIn("unbookmark", icons)
        self.assertIn("attach", icons)
        self.assertIn("detach", icons)
        self.assertIn("zoom_in", icons)
        self.assertIn("zoom_out", icons)
        self.assertIn("fullscreen", icons)
        self.assertIn("exit_fullscreen", icons)
        self.assertIn("close", icons)
        self.assertIn("minimize", icons)
        self.assertIn("maximize", icons)
        self.assertIn("restore", icons)
        self.assertIn("ok", icons)
        self.assertIn("cancel", icons)
        self.assertIn("apply", icons)
        self.assertIn("reset", icons)
        self.assertIn("clear", icons)
        self.assertIn("back", icons)
        self.assertIn("forward", icons)
        self.assertIn("up", icons)
        self.assertIn("down", icons)
        self.assertIn("left", icons)
        self.assertIn("right", icons)
        self.assertIn("menu", icons)
        self.assertIn("more", icons)
        self.assertIn("more_horiz", icons)
        self.assertIn("drag", icons)
        self.assertIn("move", icons)
        self.assertIn("resize", icons)
        self.assertIn("rotate", icons)
        self.assertIn("flip", icons)
        self.assertIn("expand", icons)
        self.assertIn("collapse", icons)
        self.assertIn("show", icons)
        self.assertIn("hide", icons)
        self.assertIn("visible", icons)
        self.assertIn("invisible", icons)
        self.assertIn("lock", icons)

if __name__ == '__main__':
    unittest.main()
