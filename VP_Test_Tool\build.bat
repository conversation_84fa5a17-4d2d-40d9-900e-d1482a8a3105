@echo off
setlocal enabledelayedexpansion

REM VP Test Tool Build Script
REM Simple build script for VP Test Tool

title VP Test Tool V2.6.1 Build System

echo.
echo ================================================================================
echo VP Test Tool V2.6.1 Build System
echo ================================================================================
echo.

REM 檢查 Python 環境
echo 🔍 檢查 Python 環境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安裝或不在 PATH 中
    echo 💡 請先安裝 Python 3.8 或更高版本
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ !PYTHON_VERSION!

REM 檢查必要文件
echo.
echo 🔍 檢查必要文件...
set MISSING_FILES=0

if not exist "main.py" (
    echo ❌ 缺少: main.py
    set MISSING_FILES=1
)
if not exist "utils\version.py" (
    echo ❌ 缺少: utils\version.py
    set MISSING_FILES=1
)
if not exist "config.json" (
    echo ❌ 缺少: config.json
    set MISSING_FILES=1
)
if not exist "setup_cx_freeze_stable.py" (
    echo ❌ 缺少: setup_cx_freeze_stable.py
    set MISSING_FILES=1
)
if not exist "build_release.py" (
    echo ❌ 缺少: build_release.py
    set MISSING_FILES=1
)

if !MISSING_FILES! equ 1 (
    echo.
    echo ❌ 缺少必要文件，無法繼續打包
    pause
    exit /b 1
)

echo ✅ 所有必要文件存在

REM 顯示選單
:MENU
echo.
echo ================================================================================
echo 📋 請選擇操作:
echo ================================================================================
echo.
echo 1. 🔍 檢查環境和依賴
echo 2. 📦 安裝/更新依賴庫
echo 3. 🧹 清理舊的打包文件
echo 4. 🔧 執行 cx_Freeze 打包
echo 5. 🧪 測試打包結果
echo 6. 🚀 完整打包流程 (推薦)
echo 7. ❓ 顯示詳細幫助
echo 0. 🚪 退出
echo.

set /p CHOICE="請輸入選項 (0-7): "

if "!CHOICE!"=="0" goto EXIT
if "!CHOICE!"=="1" goto CHECK_ENV
if "!CHOICE!"=="2" goto INSTALL_DEPS
if "!CHOICE!"=="3" goto CLEAN
if "!CHOICE!"=="4" goto CX_FREEZE
if "!CHOICE!"=="5" goto TEST
if "!CHOICE!"=="6" goto BUILD_ALL
if "!CHOICE!"=="7" goto HELP

echo ⚠️ 無效選項，請重新選擇
goto MENU

:CHECK_ENV
echo.
echo 🔍 檢查環境和依賴...
python build_release.py --check-env
if errorlevel 1 (
    echo.
    echo ❌ 環境檢查失敗
    pause
    goto MENU
)
echo.
echo ✅ 環境檢查完成
pause
goto MENU

:INSTALL_DEPS
echo.
echo 📦 安裝/更新依賴庫...
python build_release.py --install-deps
if errorlevel 1 (
    echo.
    echo ❌ 依賴安裝失敗
    pause
    goto MENU
)
echo.
echo ✅ 依賴安裝完成
pause
goto MENU

:CLEAN
echo.
echo 🧹 清理舊的打包文件...
python build_release.py --clean
if errorlevel 1 (
    echo.
    echo ❌ 清理失敗
    pause
    goto MENU
)
echo.
echo ✅ 清理完成
pause
goto MENU

:CX_FREEZE
echo.
echo 🔧 執行 cx_Freeze 打包...
echo 💡 這可能需要幾分鐘時間，請耐心等待...
python build_release.py --cx-freeze
if errorlevel 1 (
    echo.
    echo ❌ cx_Freeze 打包失敗
    echo 💡 請檢查錯誤信息，可能需要先安裝依賴庫
    pause
    goto MENU
)
echo.
echo ✅ cx_Freeze 打包完成
echo 📁 輸出位置: dist\cx_freeze_stable\
pause
goto MENU

:TEST
echo.
echo 🧪 測試打包結果...
python build_release.py --test-only
if errorlevel 1 (
    echo.
    echo ❌ 測試失敗
    pause
    goto MENU
)
echo.
echo ✅ 測試完成
pause
goto MENU

:BUILD_ALL
echo.
echo 🚀 執行完整打包流程...
echo 💡 這將執行所有步驟，可能需要 5-10 分鐘，請耐心等待...
echo.
python build_release.py --build-all
if errorlevel 1 (
    echo.
    echo ❌ 完整打包流程失敗
    echo 💡 請檢查上方的錯誤信息
    pause
    goto MENU
)
echo.
echo ================================================================================
echo 🎉 完整打包流程成功完成！
echo ================================================================================
echo.
echo 📁 打包結果位置:
echo    • cx_Freeze: dist\cx_freeze_stable\VP_Test_Tool.exe
echo    • 發布包: releases\
echo.
echo 💡 使用建議:
echo    • 將整個 dist\cx_freeze_stable 目錄分發給用戶
echo    • 確保目標系統安裝了 Visual C++ 運行時
echo    • 建議將程式加入防毒軟體白名單
echo.
pause
goto MENU

:HELP
echo.
echo ================================================================================
echo ❓ VP Test Tool 打包系統詳細幫助
echo ================================================================================
echo.
echo 🎯 打包流程說明:
echo    1. 檢查環境 - 驗證 Python 版本和依賴庫是否正確安裝
echo    2. 安裝依賴 - 自動安裝或更新所需的 Python 庫
echo    3. 清理目錄 - 移除舊的打包文件，確保乾淨的打包環境
echo    4. cx_Freeze 打包 - 使用穩定配置將 Python 程式打包為 exe
echo    5. 測試打包 - 驗證打包結果的完整性和正確性
echo    6. 創建發布包 - 整理最終的發布文件
echo.
echo 📁 輸出位置說明:
echo    • dist\cx_freeze_stable\ - cx_Freeze 打包的直接結果
echo    • releases\ - 整理後的發布包，包含文檔和版本信息
echo.
echo 🔧 故障排除:
echo    • 如果遇到 "ModuleNotFoundError"，請執行選項 2 安裝依賴
echo    • 如果 Python 版本過低，請升級到 Python 3.8 或更高版本
echo    • 如果打包的 exe 無法執行，可能被防毒軟體阻擋
echo    • 如果打包過程中斷，請先執行選項 3 清理，再重新打包
echo.
echo 💡 推薦流程:
echo    首次打包: 1 → 2 → 6
echo    日常打包: 3 → 4 或直接執行 6
echo    問題排除: 1 → 2 → 3 → 4 → 5
echo.
echo 📞 技術支援:
echo    如果遇到無法解決的問題，請保存完整的錯誤信息
echo    並聯繫 VP Test Tool 開發團隊
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感謝使用 VP Test Tool 自動化打包系統！
echo.
pause
exit /b 0
