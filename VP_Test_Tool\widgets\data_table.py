"""資料表格元件"""
import tkinter as tk
from tkinter import ttk
from typing import List, Dict, Any, Optional, Callable
from utils.theme import ThemeManager

class DataTable(ttk.Frame):
    """資料表格元件
    
    提供更現代化的表格顯示功能，支援排序、過濾和分頁。
    
    Args:
        parent: 父元件
        columns: 欄位定義，格式為 [{"id": "col1", "text": "欄位1", "width": 100}, ...]
        data: 資料列表，格式為 [{"col1": "值1", "col2": "值2", ...}, ...]
        on_select: 選擇資料時的回調函數
        on_double_click: 雙擊資料時的回調函數
        **kwargs: 其他 ttk.Frame 參數
    """
    
    def __init__(
        self, 
        parent, 
        columns: List[Dict[str, Any]], 
        data: Optional[List[Dict[str, Any]]] = None, 
        on_select: Optional[Callable[[Dict[str, Any]], None]] = None, 
        on_double_click: Optional[Callable[[Dict[str, Any]], None]] = None, 
        **kwargs
    ):
        super().__init__(parent, **kwargs)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.columns = columns
        self.data = data or []
        self.on_select = on_select
        self.on_double_click = on_double_click
        self.selected_item = None
        self.sort_column = None
        self.sort_order = "asc"
        
        # 初始化 UI
        self._init_ui()
        
        # 載入資料
        self.load_data(self.data)
        
    def _init_ui(self):
        """初始化 UI"""
        # 建立工具列
        toolbar = ttk.Frame(self)
        toolbar.pack(fill="x", padx=5, pady=5)
        
        # 建立搜尋欄位
        search_frame = ttk.Frame(toolbar)
        search_frame.pack(side="left")
        
        ttk.Label(
            search_frame, 
            text="搜尋:", 
            font=self.theme_manager.get_font("small")
        ).pack(side="left", padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace_add("write", self._on_search)
        
        search_entry = ttk.Entry(
            search_frame, 
            textvariable=self.search_var, 
            width=20, 
            font=self.theme_manager.get_font("small")
        )
        search_entry.pack(side="left", padx=5)
        
        # 建立表格
        table_frame = ttk.Frame(self)
        table_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 建立表格標題
        columns = [col["id"] for col in self.columns]
        self.tree = ttk.Treeview(
            table_frame, 
            columns=columns, 
            show="headings", 
            selectmode="browse"
        )
        
        # 設定表格標題
        for col in self.columns:
            self.tree.heading(
                col["id"], 
                text=col["text"], 
                command=lambda c=col["id"]: self._sort_by_column(c)
            )
            self.tree.column(
                col["id"], 
                width=col.get("width", 100), 
                anchor=col.get("anchor", "w")
            )
        
        # 建立捲軸
        vsb = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # 排列元件
        self.tree.grid(column=0, row=0, sticky="nsew")
        vsb.grid(column=1, row=0, sticky="ns")
        hsb.grid(column=0, row=1, sticky="ew")
        
        # 設定表格框架的網格權重
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # 綁定事件
        self.tree.bind("<<TreeviewSelect>>", self._on_select)
        self.tree.bind("<Double-1>", self._on_double_click)
        
        # 設定表格樣式
        style = ttk.Style()
        style.configure(
            "Treeview", 
            font=self.theme_manager.get_font("text"),
            rowheight=25
        )
        style.configure(
            "Treeview.Heading", 
            font=self.theme_manager.get_font("label"),
            background=self.theme_manager.get_color("primary"),
            foreground=self.theme_manager.get_color("surface")
        )
        
        # 設定表格行交替顏色
        style.map(
            "Treeview", 
            background=[
                ("selected", self.theme_manager.get_color("primary"))
            ],
            foreground=[
                ("selected", self.theme_manager.get_color("surface"))
            ]
        )
        
        # 建立分頁控制
        paging_frame = ttk.Frame(self)
        paging_frame.pack(fill="x", padx=5, pady=5)
        
        self.page_info_label = ttk.Label(
            paging_frame, 
            text="顯示 0 - 0 / 0 筆資料", 
            font=self.theme_manager.get_font("small")
        )
        self.page_info_label.pack(side="left", padx=5)
        
        self.prev_page_button = ttk.Button(
            paging_frame, 
            text="上一頁", 
            command=self._prev_page,
            state="disabled"
        )
        self.prev_page_button.pack(side="right", padx=5)
        
        self.next_page_button = ttk.Button(
            paging_frame, 
            text="下一頁", 
            command=self._next_page,
            state="disabled"
        )
        self.next_page_button.pack(side="right", padx=5)
        
        # 分頁設定
        self.page_size = 20
        self.current_page = 0
        self.total_pages = 0
        self.filtered_data = []
        
    def load_data(self, data: List[Dict[str, Any]]):
        """載入資料
        
        Args:
            data: 資料列表，格式為 [{"col1": "值1", "col2": "值2", ...}, ...]
        """
        self.data = data
        self.filtered_data = data.copy()
        self._update_table()
        
    def _update_table(self):
        """更新表格"""
        # 清除表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 計算分頁
        self.total_pages = (len(self.filtered_data) + self.page_size - 1) // self.page_size
        
        # 確保當前頁面有效
        if self.current_page >= self.total_pages:
            self.current_page = max(0, self.total_pages - 1)
        
        # 計算當前頁面的資料範圍
        start = self.current_page * self.page_size
        end = min(start + self.page_size, len(self.filtered_data))
        
        # 更新分頁資訊
        self.page_info_label.config(
            text=f"顯示 {start + 1 if self.filtered_data else 0} - {end} / {len(self.filtered_data)} 筆資料"
        )
        
        # 更新分頁按鈕狀態
        self.prev_page_button.config(state="normal" if self.current_page > 0 else "disabled")
        self.next_page_button.config(state="normal" if self.current_page < self.total_pages - 1 else "disabled")
        
        # 載入當前頁面的資料
        for i, item in enumerate(self.filtered_data[start:end]):
            values = [item.get(col["id"], "") for col in self.columns]
            item_id = self.tree.insert("", "end", values=values)
            
            # 設定行背景色
            if i % 2 == 0:
                self.tree.item(item_id, tags=("even",))
            else:
                self.tree.item(item_id, tags=("odd",))
        
        # 設定行背景色
        self.tree.tag_configure(
            "even", 
            background=self.theme_manager.get_color("surface")
        )
        self.tree.tag_configure(
            "odd", 
            background=self.theme_manager.get_color("hover")
        )
        
    def _on_search(self, *args):
        """搜尋事件處理"""
        search_text = self.search_var.get().lower()
        
        if not search_text:
            self.filtered_data = self.data.copy()
        else:
            self.filtered_data = []
            for item in self.data:
                for col in self.columns:
                    value = str(item.get(col["id"], "")).lower()
                    if search_text in value:
                        self.filtered_data.append(item)
                        break
        
        self.current_page = 0
        self._update_table()
        
    def _sort_by_column(self, column):
        """依欄位排序
        
        Args:
            column: 欄位 ID
        """
        if self.sort_column == column:
            # 切換排序順序
            self.sort_order = "desc" if self.sort_order == "asc" else "asc"
        else:
            # 設定新的排序欄位
            self.sort_column = column
            self.sort_order = "asc"
        
        # 更新欄位標題
        for col in self.columns:
            if col["id"] == column:
                self.tree.heading(
                    col["id"], 
                    text=f"{col['text']} {'↑' if self.sort_order == 'asc' else '↓'}"
                )
            else:
                self.tree.heading(col["id"], text=col["text"])
        
        # 排序資料
        self.filtered_data.sort(
            key=lambda x: str(x.get(column, "")), 
            reverse=(self.sort_order == "desc")
        )
        
        self._update_table()
        
    def _on_select(self, event):
        """選擇事件處理"""
        selection = self.tree.selection()
        if not selection:
            return
        
        # 取得選擇的項目
        item = self.tree.item(selection[0])
        values = item["values"]
        
        # 建立資料字典
        selected_data = {}
        for i, col in enumerate(self.columns):
            selected_data[col["id"]] = values[i] if i < len(values) else ""
        
        self.selected_item = selected_data
        
        # 呼叫回調函數
        if self.on_select:
            self.on_select(selected_data)
            
    def _on_double_click(self, event):
        """雙擊事件處理"""
        if not self.selected_item:
            return
            
        # 呼叫回調函數
        if self.on_double_click:
            self.on_double_click(self.selected_item)
            
    def _prev_page(self):
        """上一頁"""
        if self.current_page > 0:
            self.current_page -= 1
            self._update_table()
            
    def _next_page(self):
        """下一頁"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self._update_table()
            
    def get_selected_item(self) -> Optional[Dict[str, Any]]:
        """取得選擇的項目
        
        Returns:
            Dict[str, Any]: 選擇的項目資料
        """
        return self.selected_item
        
    def clear(self):
        """清除表格"""
        self.data = []
        self.filtered_data = []
        self.selected_item = None
        self.current_page = 0
        self.total_pages = 0
        self._update_table()
