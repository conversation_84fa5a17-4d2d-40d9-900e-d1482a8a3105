"""增強版 HTTP 客戶端

此模組提供增強版 HTTP 客戶端，支援重試機制、錯誤處理和快取功能。
增強版本：添加了網絡恢復功能和斷點續傳功能。
"""
import requests
import logging
import time
import json
import hashlib
import threading
import os
import tkinter as tk
from tkinter import messagebox
from typing import Dict, Any, Optional, Tuple, Union, List
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from .exceptions import (
    APIError, APITimeoutError, APIConnectionError,
    APIResponseError, APIAuthError, APIRateLimitError,
    NetworkConnectionError, NetworkTimeoutError
)
from .network_recovery import network_recovery

logger = logging.getLogger(__name__)

class HttpClientEnhanced:
    """增強版 HTTP 客戶端

    提供 HTTP 請求功能，支援重試機制、錯誤處理和快取功能。

    Args:
        base_url: 基礎 URL
        timeout: 請求超時時間（秒）
        max_retries: 最大重試次數
        retry_backoff_factor: 重試間隔因子
        retry_status_forcelist: 需要重試的狀態碼列表
        show_error_dialog: 是否顯示錯誤對話框
        cache_enabled: 是否啟用快取
        cache_ttl: 快取有效期（秒）
    """

    def __init__(
        self,
        base_url: str = "",
        timeout: int = 30,
        max_retries: int = 3,
        retry_backoff_factor: float = 0.5,
        retry_status_forcelist: Optional[List[int]] = None,
        show_error_dialog: bool = True,
        cache_enabled: bool = True,
        cache_ttl: int = 300,
        enable_network_recovery: bool = True,
        enable_resume_download: bool = True,
        resume_download_dir: str = "temp_downloads",
        network_check_interval: float = 30.0
    ):
        self.base_url = base_url
        self.timeout = timeout
        self.show_error_dialog = show_error_dialog
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.enable_network_recovery = enable_network_recovery
        self.enable_resume_download = enable_resume_download
        self.resume_download_dir = resume_download_dir
        self.network_check_interval = network_check_interval

        # 創建 session
        self.session = requests.Session()

        # 配置重試策略
        if retry_status_forcelist is None:
            retry_status_forcelist = [429, 500, 502, 503, 504]

        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=retry_backoff_factor,
            status_forcelist=retry_status_forcelist,
            allowed_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
            raise_on_status=True
        )

        # 配置 adapter
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 快取
        self._cache = {}
        self._cache_lock = threading.RLock()

        # 用於顯示錯誤對話框的根窗口
        self._root = None

        # 網絡恢復
        if self.enable_network_recovery:
            # 設置網絡恢復回調
            network_recovery.on_recovery_start = self._on_network_recovery_start
            network_recovery.on_recovery_success = self._on_network_recovery_success
            network_recovery.on_recovery_failure = self._on_network_recovery_failure
            network_recovery.on_connection_status = self._on_network_connection_status
            network_recovery.check_interval = self.network_check_interval

            # 啟動網絡監控
            network_recovery.start_monitoring()

        # 創建斷點續傳目錄
        if self.enable_resume_download:
            self._create_resume_download_dir()

    def get(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        use_cache: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """發送 GET 請求

        Args:
            url: 請求 URL
            params: 請求參數
            headers: 請求頭
            use_cache: 是否使用快取
            **kwargs: 其他參數

        Returns:
            Dict[str, Any]: 回應內容
        """
        # 構建完整 URL
        full_url = self._build_url(url)

        # 檢查快取
        if self.cache_enabled and use_cache:
            cache_key = self._generate_cache_key("GET", full_url, params, None)
            cached_response = self._get_from_cache(cache_key)
            if cached_response:
                logger.debug(f"從快取中獲取 GET {full_url}")
                return cached_response

        try:
            # 發送請求
            response = self.session.get(
                full_url,
                params=params,
                headers=headers,
                timeout=self.timeout,
                **kwargs
            )

            # 檢查狀態碼
            response.raise_for_status()

            # 解析回應
            result = self._parse_response(response)

            # 快取回應
            if self.cache_enabled and use_cache:
                self._add_to_cache(cache_key, result)

            return result

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {full_url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {full_url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {full_url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {full_url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)

            # 根據狀態碼創建不同的錯誤
            if e.response.status_code == 401:
                api_error = APIAuthError(f"認證失敗: {str(e)}", status_code=e.response.status_code)
            elif e.response.status_code == 429:
                api_error = APIRateLimitError(f"請求過於頻繁: {str(e)}", status_code=e.response.status_code)
            else:
                api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code)

            return {"success": False, "error": str(api_error)}

        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except Exception as e:
            error_msg = f"未知錯誤: {e}"
            logger.error(f"未知錯誤: {e}")
            self._show_error_message("未知錯誤", error_msg)
            api_error = APIError(f"未知錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}

    def post(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        use_cache: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """發送 POST 請求

        Args:
            url: 請求 URL
            data: 表單數據
            json_data: JSON 數據
            headers: 請求頭
            use_cache: 是否使用快取
            **kwargs: 其他參數

        Returns:
            Dict[str, Any]: 回應內容
        """
        # 構建完整 URL
        full_url = self._build_url(url)

        # 檢查快取
        if self.cache_enabled and use_cache:
            cache_key = self._generate_cache_key("POST", full_url, None, json_data or data)
            cached_response = self._get_from_cache(cache_key)
            if cached_response:
                logger.debug(f"從快取中獲取 POST {full_url}")
                return cached_response

        try:
            # 發送請求
            response = self.session.post(
                full_url,
                data=data,
                json=json_data,
                headers=headers,
                timeout=self.timeout,
                **kwargs
            )

            # 檢查狀態碼
            response.raise_for_status()

            # 解析回應
            result = self._parse_response(response)

            # 快取回應
            if self.cache_enabled and use_cache:
                self._add_to_cache(cache_key, result)

            return result

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {full_url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {full_url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {full_url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {full_url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)

            # 根據狀態碼創建不同的錯誤
            if e.response.status_code == 401:
                api_error = APIAuthError(f"認證失敗: {str(e)}", status_code=e.response.status_code)
            elif e.response.status_code == 429:
                api_error = APIRateLimitError(f"請求過於頻繁: {str(e)}", status_code=e.response.status_code)
            else:
                api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code)

            return {"success": False, "error": str(api_error)}

        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}

        except Exception as e:
            error_msg = f"未知錯誤: {e}"
            logger.error(f"未知錯誤: {e}")
            self._show_error_message("未知錯誤", error_msg)
            api_error = APIError(f"未知錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}

    def get_with_details(
        self,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Tuple[Dict[str, Any], int]:
        """發送 GET 請求並返回詳細資訊

        Args:
            url: 請求 URL
            params: 請求參數
            headers: 請求頭
            **kwargs: 其他參數

        Returns:
            Tuple[Dict[str, Any], int]: 回應內容和狀態碼
        """
        # 構建完整 URL
        full_url = self._build_url(url)

        try:
            # 發送請求
            response = self.session.get(
                full_url,
                params=params,
                headers=headers,
                timeout=self.timeout,
                **kwargs
            )

            # 檢查狀態碼
            response.raise_for_status()

            # 解析回應
            result = self._parse_response(response)

            return result, response.status_code

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {full_url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {full_url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}, 408  # 返回超時狀態碼

        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {full_url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {full_url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 503  # 返回服務不可用狀態碼

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)

            # 根據狀態碼創建不同的錯誤
            if e.response.status_code == 401:
                api_error = APIAuthError(f"認證失敗: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            elif e.response.status_code == 429:
                api_error = APIRateLimitError(f"請求過於頻繁: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            else:
                api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)

            return {"success": False, "error": str(api_error)}, e.response.status_code

        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

        except Exception as e:
            error_msg = f"未知錯誤: {e}"
            logger.error(f"未知錯誤: {e}")
            self._show_error_message("未知錯誤", error_msg)
            api_error = APIError(f"未知錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

    def post_with_details(
        self,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Tuple[Dict[str, Any], int]:
        """發送 POST 請求並返回詳細資訊

        Args:
            url: 請求 URL
            data: 表單數據
            json_data: JSON 數據
            headers: 請求頭
            **kwargs: 其他參數

        Returns:
            Tuple[Dict[str, Any], int]: 回應內容和狀態碼
        """
        # 構建完整 URL
        full_url = self._build_url(url)

        try:
            # 發送請求
            response = self.session.post(
                full_url,
                data=data,
                json=json_data,
                headers=headers,
                timeout=self.timeout,
                **kwargs
            )

            # 檢查狀態碼
            response.raise_for_status()

            # 解析回應
            result = self._parse_response(response)

            return result, response.status_code

        except requests.exceptions.Timeout as e:
            error_msg = f"請求超時: {e}\n連接 {full_url} 超時，超時設定為 {self.timeout} 秒"
            logger.error(f"請求超時: {e}")
            logger.warning(f"連接 {full_url} 超時，超時設定為 {self.timeout} 秒")
            self._show_error_message("網絡連接超時", error_msg)
            api_error = APITimeoutError(f"請求超時: {str(e)}")
            return {"success": False, "error": str(api_error)}, 408  # 返回超時狀態碼

        except requests.exceptions.ConnectionError as e:
            error_msg = f"連接錯誤: {e}\n無法連接到 {full_url}"
            logger.error(f"連接錯誤: {e}")
            logger.warning(f"無法連接到 {full_url}")
            self._show_error_message("網絡連接錯誤", error_msg)
            api_error = APIConnectionError(f"連接錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 503  # 返回服務不可用狀態碼

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 錯誤: {e}\n狀態碼: {e.response.status_code}"
            logger.error(f"HTTP 錯誤: {e}")
            logger.warning(f"HTTP 狀態碼: {e.response.status_code}")
            self._show_error_message("HTTP 錯誤", error_msg)

            # 根據狀態碼創建不同的錯誤
            if e.response.status_code == 401:
                api_error = APIAuthError(f"認證失敗: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            elif e.response.status_code == 429:
                api_error = APIRateLimitError(f"請求過於頻繁: {str(e)}", status_code=e.response.status_code, response=e.response.text)
            else:
                api_error = APIResponseError(f"HTTP 錯誤: {str(e)}", status_code=e.response.status_code, response=e.response.text)

            return {"success": False, "error": str(api_error)}, e.response.status_code

        except requests.exceptions.RequestException as e:
            error_msg = f"請求失敗: {e}\n無法連線到 API"
            logger.error(f"請求失敗: {e}")
            logger.warning("無法連線到 API")
            self._show_error_message("API 請求失敗", error_msg)
            api_error = APIConnectionError(f"請求失敗: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

        except Exception as e:
            error_msg = f"未知錯誤: {e}"
            logger.error(f"未知錯誤: {e}")
            self._show_error_message("未知錯誤", error_msg)
            api_error = APIError(f"未知錯誤: {str(e)}")
            return {"success": False, "error": str(api_error)}, 500  # 返回錯誤狀態碼

    def _build_url(self, url: str) -> str:
        """構建完整 URL

        Args:
            url: 請求 URL

        Returns:
            str: 完整 URL
        """
        if url.startswith(("http://", "https://")):
            return url
        elif self.base_url:
            return f"{self.base_url.rstrip('/')}/{url.lstrip('/')}"
        else:
            return url

    def _parse_response(self, response: requests.Response) -> Dict[str, Any]:
        """解析回應

        Args:
            response: 回應對象

        Returns:
            Dict[str, Any]: 解析後的回應內容
        """
        try:
            # 嘗試解析 JSON
            result = response.json()

            # 如果回應是字典，直接返回
            if isinstance(result, dict):
                return result

            # 如果回應是列表，包裝為字典
            if isinstance(result, list):
                return {"success": True, "data": result}

            # 其他情況，包裝為字典
            return {"success": True, "data": result}

        except ValueError:
            # 如果無法解析 JSON，返回文本內容
            return {"success": True, "data": response.text}

    def _generate_cache_key(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> str:
        """生成快取鍵

        Args:
            method: 請求方法
            url: 請求 URL
            params: 請求參數
            data: 請求數據

        Returns:
            str: 快取鍵
        """
        # 構建快取鍵
        key_parts = [method, url]

        if params:
            key_parts.append(json.dumps(params, sort_keys=True))

        if data:
            key_parts.append(json.dumps(data, sort_keys=True))

        key_str = "|".join(key_parts)

        # 使用 MD5 生成雜湊值
        return hashlib.md5(key_str.encode()).hexdigest()

    def _get_from_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """從快取中獲取數據

        Args:
            key: 快取鍵

        Returns:
            Optional[Dict[str, Any]]: 快取數據
        """
        with self._cache_lock:
            cache_item = self._cache.get(key)

            if not cache_item:
                return None

            # 檢查是否過期
            timestamp, data = cache_item
            if time.time() - timestamp > self.cache_ttl:
                # 刪除過期項目
                del self._cache[key]
                return None

            return data

    def _add_to_cache(self, key: str, data: Dict[str, Any]):
        """添加數據到快取

        Args:
            key: 快取鍵
            data: 快取數據
        """
        with self._cache_lock:
            self._cache[key] = (time.time(), data)

            # 清理過期項目
            self._clean_cache()

    def _clean_cache(self):
        """清理過期快取項目"""
        with self._cache_lock:
            current_time = time.time()
            expired_keys = [
                key for key, (timestamp, _) in self._cache.items()
                if current_time - timestamp > self.cache_ttl
            ]

            for key in expired_keys:
                del self._cache[key]

    def clear_cache(self):
        """清除快取"""
        with self._cache_lock:
            self._cache.clear()
            logger.debug("已清除 HTTP 快取")

    def _show_error_message(self, title: str, message: str):
        """顯示錯誤對話框

        Args:
            title: 標題
            message: 訊息
        """
        if not self.show_error_dialog:
            return

        # 在主線程中顯示對話框
        def show_dialog():
            try:
                # 檢查是否已有根視窗
                if not self._root:
                    self._root = tk.Tk()
                    self._root.withdraw()
                elif not self._root.winfo_exists():
                    # 如果根視窗已被銷毀，創建新的
                    self._root = tk.Tk()
                    self._root.withdraw()

                # 顯示錯誤對話框
                messagebox.showerror(title, message)
            except Exception as e:
                # 如果顯示對話框失敗，記錄錯誤
                print(f"顯示錯誤對話框失敗: {e}")

        # 如果在主線程中，直接顯示
        if threading.current_thread() is threading.main_thread():
            show_dialog()
        else:
            # 否則，使用 after 方法在主線程中顯示
            try:
                if not self._root:
                    self._root = tk.Tk()
                    self._root.withdraw()
                elif not self._root.winfo_exists():
                    # 如果根視窗已被銷毀，創建新的
                    self._root = tk.Tk()
                    self._root.withdraw()

                # 使用 after 方法在主線程中顯示
                self._root.after(0, show_dialog)
            except Exception as e:
                # 如果設置 after 調用失敗，記錄錯誤
                print(f"設置錯誤對話框顯示失敗: {e}")

    def close(self):
        """關閉 session"""
        self.session.close()

        # 關閉根窗口
        if self._root:
            try:
                if self._root.winfo_exists():
                    self._root.destroy()
            except Exception:
                pass  # 忽略任何銷毀視窗時的錯誤
            finally:
                self._root = None  # 確保引用被清空

        # 停止網絡監控
        if self.enable_network_recovery:
            network_recovery.stop_monitoring()

    def __enter__(self):
        """進入上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.close()

    def _create_resume_download_dir(self):
        """創建斷點續傳目錄"""
        try:
            if not os.path.exists(self.resume_download_dir):
                os.makedirs(self.resume_download_dir)
                logger.info(f"已創建斷點續傳目錄: {self.resume_download_dir}")
        except Exception as e:
            logger.error(f"創建斷點續傳目錄失敗: {e}")

    def _on_network_recovery_start(self):
        """網絡恢復開始回調函數"""
        logger.info("開始恢復網絡連接...")

        # 顯示錯誤對話框
        if self.show_error_dialog:
            self._show_error_message(
                "網絡連接中斷",
                "網絡連接已中斷，正在嘗試恢復連接...\n\n"
                "請檢查您的網絡連接，並確保網絡設備正常運行。"
            )

    def _on_network_recovery_success(self):
        """網絡恢復成功回調函數"""
        logger.info("網絡連接已恢復")

        # 顯示成功對話框
        if self.show_error_dialog:
            self._show_info_message(
                "網絡連接已恢復",
                "網絡連接已成功恢復，您可以繼續使用應用程式。"
            )

    def _on_network_recovery_failure(self, error: Exception):
        """網絡恢復失敗回調函數

        Args:
            error: 錯誤信息
        """
        logger.error(f"網絡連接恢復失敗: {error}")

        # 顯示錯誤對話框
        if self.show_error_dialog:
            self._show_error_message(
                "網絡連接恢復失敗",
                f"網絡連接恢復失敗，請檢查您的網絡連接，並確保網絡設備正常運行。\n\n"
                f"錯誤信息: {error}"
            )

    def _on_network_connection_status(self, is_connected: bool, error_msg: Optional[str]):
        """網絡連接狀態回調函數

        Args:
            is_connected: 是否已連接
            error_msg: 錯誤信息
        """
        if is_connected:
            logger.info("網絡連接正常")
        else:
            logger.warning(f"網絡連接已斷開: {error_msg}")

    def _show_info_message(self, title: str, message: str):
        """顯示信息對話框

        Args:
            title: 標題
            message: 信息
        """
        try:
            if self._root is None:
                self._root = tk.Tk()
                self._root.withdraw()

            messagebox.showinfo(title, message)
        except Exception as e:
            logger.error(f"顯示信息對話框失敗: {e}")

    def download_file(self, url: str, file_path: str, chunk_size: int = 8192) -> bool:
        """下載文件，支援斷點續傳

        Args:
            url: 文件 URL
            file_path: 保存路徑
            chunk_size: 分塊大小

        Returns:
            bool: 是否成功下載
        """
        # 檢查是否啟用斷點續傳
        if not self.enable_resume_download:
            return self._download_file_simple(url, file_path, chunk_size)

        # 獲取文件名
        file_name = os.path.basename(file_path)
        temp_file = os.path.join(self.resume_download_dir, f"{file_name}.part")

        # 檢查是否存在臨時文件
        file_size = 0
        if os.path.exists(temp_file):
            file_size = os.path.getsize(temp_file)
            logger.info(f"發現臨時文件: {temp_file}, 大小: {file_size} 字節")

        # 設置 Range 頭
        headers = {"Range": f"bytes={file_size}-"}

        try:
            # 發送請求
            response = self.session.get(
                url,
                headers=headers,
                stream=True,
                timeout=self.timeout
            )

            # 檢查狀態碼
            if response.status_code not in [200, 206]:
                logger.error(f"下載文件失敗: {url}, 狀態碼: {response.status_code}")
                return False

            # 獲取文件總大小
            total_size = int(response.headers.get("content-length", 0))
            if response.status_code == 206:
                content_range = response.headers.get("content-range", "")
                if content_range:
                    total_size = int(content_range.split("/")[1])

            # 打開臨時文件
            mode = "ab" if file_size > 0 else "wb"
            with open(temp_file, mode) as f:
                # 下載文件
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)

            # 重命名臨時文件
            os.rename(temp_file, file_path)
            logger.info(f"文件下載完成: {file_path}")

            return True

        except requests.exceptions.Timeout as e:
            logger.error(f"下載文件超時: {url}, {e}")
            return False

        except requests.exceptions.ConnectionError as e:
            logger.error(f"下載文件連接錯誤: {url}, {e}")
            return False

        except Exception as e:
            logger.error(f"下載文件失敗: {url}, {e}")
            return False

    def _download_file_simple(self, url: str, file_path: str, chunk_size: int = 8192) -> bool:
        """簡單下載文件，不支援斷點續傳

        Args:
            url: 文件 URL
            file_path: 保存路徑
            chunk_size: 分塊大小

        Returns:
            bool: 是否成功下載
        """
        try:
            # 發送請求
            response = self.session.get(
                url,
                stream=True,
                timeout=self.timeout
            )

            # 檢查狀態碼
            if response.status_code != 200:
                logger.error(f"下載文件失敗: {url}, 狀態碼: {response.status_code}")
                return False

            # 打開文件
            with open(file_path, "wb") as f:
                # 下載文件
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)

            logger.info(f"文件下載完成: {file_path}")

            return True

        except Exception as e:
            logger.error(f"下載文件失敗: {url}, {e}")
            return False

    def get_network_status(self) -> Dict[str, Any]:
        """獲取網絡狀態

        Returns:
            Dict[str, Any]: 網絡狀態信息
        """
        if self.enable_network_recovery:
            return network_recovery.get_connection_status()
        else:
            return {"is_connected": True, "last_error": None, "is_monitoring": False}

    def get_network_diagnostic_report(self) -> str:
        """獲取網絡診斷報告

        Returns:
            str: 網絡診斷報告
        """
        if self.enable_network_recovery:
            return network_recovery.get_diagnostic_report()
        else:
            return "網絡診斷功能未啟用"

    def handle_api_error(self, url: str, response: Dict[str, Any], resource_type: str = "") -> Tuple[bool, str]:
        """處理 API 特定錯誤

        Args:
            url: API URL
            response: API 回應
            resource_type: 資源類型 (用於資源更新 API)

        Returns:
            Tuple[bool, str]: 是否成功和錯誤訊息
        """
        # 導入 API 錯誤處理器
        from .api_error_handler import ApiErrorHandler
        from .constants import API_URLS

        # 根據 URL 判斷 API 類型
        if url == API_URLS["MYSQL_OPERATOR"] or url == API_URLS["QUERY_MEMBER"]:
            return ApiErrorHandler.handle_mysql_operator_error(response)

        elif url == API_URLS["UPDATE_COIN"]:
            return ApiErrorHandler.handle_resource_update_error(response, "金幣")

        elif url == API_URLS["UPDATE_VIP"]:
            return ApiErrorHandler.handle_resource_update_error(response, "VIP等級")

        elif url == API_URLS["UPDATE_GEM"]:
            return ApiErrorHandler.handle_resource_update_error(response, "寶石")

        elif url == API_URLS["UPDATE_LOTTERY"]:
            return ApiErrorHandler.handle_resource_update_error(response, "樂透券")

        elif url == API_URLS["SET_RNG"]:
            return ApiErrorHandler.handle_rng_set_error(response)

        elif url == API_URLS["CREATE_MEMBER"]:
            success, error_msg, member_id = ApiErrorHandler.handle_create_member_error(response)
            return success, error_msg

        elif url == API_URLS["ADD_CARDS"]:
            return ApiErrorHandler.handle_add_cards_error(response)

        # 如果沒有特定的處理方式，檢查通用的成功/失敗標誌
        if "success" in response:
            if response["success"]:
                return True, ""
            else:
                error_msg = response.get("error", "未知錯誤")
                return False, error_msg

        # 檢查 retStatus
        if "retStatus" in response:
            status_code = response["retStatus"].get("StatusCode")
            status_msg = response["retStatus"].get("StatusMsg", "")

            if status_code == 10000 or status_msg == "Success":
                return True, ""
            else:
                return False, f"API 錯誤: 錯誤碼 {status_code} ({status_msg})"

        # 如果沒有明確的成功/失敗標誌，假設成功
        return True, ""

# 創建全局 HTTP 客戶端實例
http_client = HttpClientEnhanced()
