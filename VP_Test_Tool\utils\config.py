"""設定檔管理"""
import os
import json
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class Config:
    """設定檔管理類"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.config_path = "config.json"
        self.config: Dict[str, Any] = {}
        self.load_config()
        self._initialized = True

    def load_config(self):
        """載入設定檔"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = self._get_default_config()
                self.save_config()

        except Exception as e:
            logger.error(f"載入設定檔失敗: {e}")
            self.config = self._get_default_config()

    def save_config(self):
        """儲存設定檔"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"儲存設定檔失敗: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """取得設定值"""
        return self.config.get(key, default)

    def set(self, key: str, value: Any):
        """設置設定值"""
        self.config[key] = value
        self.save_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """取得預設設定"""
        return {
            "api": {
                "base_url": "http://localhost:8080",
                "timeout": 30
            },
            "database": {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "",
                "database": "test"
            },
            "log": {
                "level": "INFO",
                "file": "app.log"
            },
            "ui": {
                "theme": "default",
                "font_size": 10,
                "window_width": 1085,  # 修改預設寬度
                "window_height": 985   # 修改預設高度
            },
            "git": {
                "api_url": "https://git-qa.yile808.com/api/v4",
                "api_url_alt": "https://git-qa.yile808.com",
                "project_path": "igaming/igaming-web-tool",
                "branch": "master",
                "folder_path": "data/slot_rng",
                "token": "**************************"
            }
        }

