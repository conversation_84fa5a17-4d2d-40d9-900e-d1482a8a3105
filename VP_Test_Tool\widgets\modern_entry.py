"""現代化輸入欄位元件"""
import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional
from utils.theme import ThemeManager

class ModernEntry(ttk.Frame):
    """現代化輸入欄位元件
    
    提供更現代化的輸入欄位外觀和互動效果，支援標籤、提示文字和驗證功能。
    
    Args:
        parent: 父元件
        label: 欄位標籤
        placeholder: 提示文字
        validation_func: 驗證函數，接受輸入值並返回布林值
        error_message: 驗證失敗時顯示的錯誤訊息
        **kwargs: 其他 ttk.Entry 參數
    """
    
    def __init__(
        self, 
        parent, 
        label: Optional[str] = None, 
        placeholder: Optional[str] = None, 
        validation_func: Optional[Callable[[str], bool]] = None, 
        error_message: str = "輸入無效", 
        **kwargs
    ):
        super().__init__(parent)
        
        # 取得主題管理器
        self.theme_manager = ThemeManager()
        
        # 設定變數
        self.validation_func = validation_func
        self.error_message = error_message
        self.placeholder = placeholder
        self.has_content = False
        
        # 建立標籤 (如果有)
        if label:
            self.label = ttk.Label(
                self, 
                text=label, 
                font=self.theme_manager.get_font("label"),
                foreground=self.theme_manager.get_color("text_primary")
            )
            self.label.pack(anchor="w", pady=(0, 2))
        
        # 建立輸入欄位
        self.entry = ttk.Entry(self, **kwargs)
        self.entry.pack(fill="x", expand=True)
        
        # 建立錯誤訊息標籤
        self.error_label = ttk.Label(
            self, 
            text="", 
            font=self.theme_manager.get_font("small"),
            foreground=self.theme_manager.get_color("danger")
        )
        self.error_label.pack(anchor="w", pady=(2, 0))
        
        # 設定提示文字
        if placeholder:
            self.entry.insert(0, placeholder)
            self.entry.config(foreground=self.theme_manager.get_color("text_secondary"))
            
        # 綁定事件
        self.entry.bind("<FocusIn>", self._on_focus_in)
        self.entry.bind("<FocusOut>", self._on_focus_out)
        self.entry.bind("<KeyRelease>", self._on_key_release)
        
    def _on_focus_in(self, event):
        """當輸入欄位獲得焦點時"""
        if not self.has_content and self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.config(foreground=self.theme_manager.get_color("text_primary"))
            
    def _on_focus_out(self, event):
        """當輸入欄位失去焦點時"""
        self._validate()
        
        if not self.entry.get() and self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.config(foreground=self.theme_manager.get_color("text_secondary"))
            self.has_content = False
        else:
            self.has_content = bool(self.entry.get())
            
    def _on_key_release(self, event):
        """當輸入欄位內容變更時"""
        self.has_content = bool(self.entry.get())
        
        # 如果有設定即時驗證，則在輸入時進行驗證
        if self.validation_func:
            self._validate(show_error=False)
            
    def _validate(self, show_error: bool = True) -> bool:
        """驗證輸入內容
        
        Args:
            show_error: 是否顯示錯誤訊息
            
        Returns:
            bool: 驗證是否通過
        """
        if not self.validation_func:
            return True
            
        value = self.entry.get()
        
        # 如果是提示文字，則視為空值
        if value == self.placeholder:
            value = ""
            
        is_valid = self.validation_func(value)
        
        if not is_valid and show_error:
            self.error_label.config(text=self.error_message)
            self.entry.config(foreground=self.theme_manager.get_color("danger"))
        else:
            self.error_label.config(text="")
            if value != self.placeholder:
                self.entry.config(foreground=self.theme_manager.get_color("text_primary"))
            else:
                self.entry.config(foreground=self.theme_manager.get_color("text_secondary"))
                
        return is_valid
        
    def get(self) -> str:
        """取得輸入值
        
        Returns:
            str: 輸入值，如果是提示文字則返回空字串
        """
        value = self.entry.get()
        
        # 如果是提示文字，則返回空字串
        if value == self.placeholder:
            return ""
            
        return value
        
    def set(self, value: str):
        """設定輸入值
        
        Args:
            value: 要設定的值
        """
        self.entry.delete(0, tk.END)
        
        if value:
            self.entry.insert(0, value)
            self.entry.config(foreground=self.theme_manager.get_color("text_primary"))
            self.has_content = True
        elif self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.config(foreground=self.theme_manager.get_color("text_secondary"))
            self.has_content = False
            
    def clear(self):
        """清除輸入值"""
        self.entry.delete(0, tk.END)
        
        if self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.config(foreground=self.theme_manager.get_color("text_secondary"))
            
        self.has_content = False
        self.error_label.config(text="")
