2025-05-16 09:59:58,307 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 09:59:58,378 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 09:59:58,379 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 09:59:58,380 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 09:59:58,386 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 33691.60 MB, 使用率 48.4%
2025-05-16 09:59:58,386 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.95 GB, 使用率 95.7%
2025-05-16 09:59:58,452 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 09:59:58,507 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 09:59:58,572 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 09:59:58,747 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 09:59:58,753 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 10:00:00,931 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 10:00:01,017 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 10:00:01,081 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 10:00:01,081 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 10:00:01,150 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 10:00:01,150 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 10:00:02,563 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 10:00:02,564 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 10:00:02,873 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 10:00:02,874 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 10:00:02,933 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 11:19:36,070 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 11:19:36,094 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 11:19:36,094 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:19:36,095 - INFO - 12728 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 11:19:36,101 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 11:19:36,102 - WARNING - 12728 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:19:36,102 - ERROR - 12728 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:52:23,693 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 11:52:23,773 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 11:52:23,779 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 36743.54 MB, 使用率 43.7%
2025-05-16 11:52:23,780 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 11:52:23,855 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 11:52:23,908 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 11:52:23,973 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 11:52:24,201 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 11:52:24,201 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 11:52:25,893 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 11:52:25,983 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 11:52:26,047 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:52:26,047 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:52:26,110 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:52:26,110 - INFO - 40760 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:58:11,119 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 11:58:11,186 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 11:58:11,186 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 11:58:11,187 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 11:58:11,192 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 40121.91 MB, 使用率 38.6%
2025-05-16 11:58:11,192 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 11:58:11,260 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 11:58:11,318 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 11:58:11,382 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 11:58:11,603 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 11:58:11,603 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 11:58:13,220 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 11:58:13,314 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 11:58:13,367 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:58:13,367 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 11:58:13,430 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 11:58:13,430 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:30,665 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:04:30,666 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:04:30,934 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:04:30,935 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:04:30,996 - INFO - 5500 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 12:04:43,549 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 12:04:43,624 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 12:04:43,624 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 12:04:43,625 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 12:04:43,630 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 41230.38 MB, 使用率 36.9%
2025-05-16 12:04:43,630 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 12:04:43,693 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 12:04:43,749 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 12:04:43,814 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 12:04:44,065 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 12:04:44,066 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 12:04:45,717 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 12:04:45,802 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 12:04:45,860 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:04:45,860 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:45,932 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:04:45,932 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:04:47,715 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:04:47,715 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:04:47,980 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:04:47,981 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:04:48,038 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 12:05:34,181 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 12:05:34,192 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 12:05:34,192 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:34,193 - INFO - 42920 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 12:05:34,198 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 12:05:34,198 - WARNING - 42920 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:34,199 - ERROR - 42920 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:42,740 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化錯誤處理器
2025-05-16 12:05:42,812 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 系統信息: Windows 10 10.0.26100
2025-05-16 12:05:42,812 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - Python 版本: 3.11.2
2025-05-16 12:05:42,813 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 處理器: Intel64 Family 6 Model 151 Stepping 2, GenuineIntel
2025-05-16 12:05:42,819 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 內存: 總計 65309.79 MB, 可用 41224.61 MB, 使用率 36.9%
2025-05-16 12:05:42,820 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 磁盤: 總計 931.50 GB, 可用 39.79 GB, 使用率 95.7%
2025-05-16 12:05:42,883 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化內存監控
2025-05-16 12:05:42,940 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化網絡監控
2025-05-16 12:05:43,003 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功載入設定
2025-05-16 12:05:43,165 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功載入應用程式圖示: d:\Gitlab\VP_Test_Tool\assets\icons\vp_test_tool.ico
2025-05-16 12:05:43,168 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化主題管理器
2025-05-16 12:05:44,619 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功建立主視窗
2025-05-16 12:05:44,706 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源監控元件
2025-05-16 12:05:44,766 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:05:44,766 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:05:44,824 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化增強版 HTTP 客戶端
2025-05-16 12:05:44,824 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化服務
2025-05-16 12:05:46,185 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化遊戲卡片工具控制器
2025-05-16 12:05:46,185 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化資源調整工具控制器
2025-05-16 12:05:46,439 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化帳號產生器控制器
2025-05-16 12:05:46,439 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功初始化 RNG 控制器
2025-05-16 12:05:46,500 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 功能檢測完成，可用功能: memory_monitor, network_recovery, enhanced_logger, resource_monitor, auto_updater, excel_support, data_processing, gui, image_processing
2025-05-16 18:14:19,695 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功使用啟動畫面初始化應用程式
2025-05-16 18:14:19,722 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 成功啟動資源監控
2025-05-16 18:14:19,724 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 初始化鍵盤快捷鍵管理器失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 18:14:19,725 - INFO - 35736 - MainThread - app - enhanced_logger.py:234 - 應用程式啟動完成
2025-05-16 18:14:19,733 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 顯示歡迎訊息失敗: invalid command name ".!label"
2025-05-16 18:14:19,734 - WARNING - 35736 - MainThread - app - enhanced_logger.py:245 - 添加功能檢測按鈕失敗: can't invoke "winfo" command: application has been destroyed
2025-05-16 18:14:19,735 - ERROR - 35736 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
